<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VDO.Ninja CSS to Base64 Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #1E1E1E;
            color: #E0E0E0;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            color: #E0E0E0;
        }
        textarea {
            width: 100%;
            height: 200px;
            margin-bottom: 10px;
            background-color: #2D2D2D;
            color: #E0E0E0;
            border: 1px solid #3D3D3D;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
        }
        #output {
            height: 100px;
        }
        button {
            padding: 10px 20px;
            background-color: #4ecca3;
            color: #1E1E1E;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45b392;
        }
        .container {
            background-color: #2D2D2D;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>VDO.Ninja CSS to Base64 Converter</h1>
        <textarea id="cssInput" placeholder="Enter your CSS here..."></textarea>
        <button onclick="convertToBase64()">Convert</button><br><br>
		<i>💡Tip: Adding <b>!important</b> after your CSS values can help override existing values.</i>
		
        <h2>Output:</h2>
        <textarea id="output" readonly></textarea>
        <button onclick="copyToClipboard()">Copy to Clipboard</button>
    </div>
    <script>
        function convertToBase64() {
            const cssInput = document.getElementById('cssInput').value;
            // Remove tabs and extra spaces
            const sanitizedCSS = cssInput.replace(/\s+/g, ' ').trim();
            const base64CSS = btoa(encodeURIComponent(sanitizedCSS));
            document.getElementById('output').value = '&cssb64=' + base64CSS;
        }
        function copyToClipboard() {
            const output = document.getElementById('output');
            output.select();
            document.execCommand('copy');
            alert('Copied to clipboard!');
        }
    </script>
</body>
</html>