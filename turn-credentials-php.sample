<?php
    //  If using static-auth-secret for your turn server, modify this file as needed; also rename to "turn-credentials.php"
	
	$expiry = 86400;
	$username = time() + $expiry;
	$secret = '<static-auth-secret>';
	$password = base64_encode ( hash_hmac ( 'sha1', $username, $secret, true ) );
	
	$turn_server = "turns:<turn-server>:<https-turn-port>"; // "turns" or "turn", depending on your turn server setup
	$stun_server = "stun:<stun-server>:<stun-port>"; // We're assuming our turn server also offers stun; uses the same username/password

	$arr = array($username, $password, $turn_server, $stun_server);
	
	// $arr = array($username, $password, $turn_server); // We can use this instead if using Google STUN
	
	echo json_encode($arr);
	
	// sample output: [1674572313,"iTofoKaflP\/pjyJOgUwstTUoT2Q=","turns:<turn-server>:<https-turn-port>","stun:<stun-server>:<stun-port>"]
?>