<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Director - VDO.Ninja Modern</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1f2e;
            color: white;
        }
        .test-section {
            background: #2a2f3e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #4a9eff;
        }
        button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3b82f6;
        }
        .result {
            background: #0a0e1a;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            border: 1px solid #333;
        }
        .success {
            border-color: #00d4aa;
            background: #0a1a15;
        }
        .error {
            border-color: #ff4757;
            background: #1a0a0a;
        }
    </style>
</head>
<body>
    <h1>🔧 Director Room Debug</h1>
    
    <div class="test-section">
        <h2>Test Join Director Form</h2>
        <button onclick="testJoinDirectorForm()">Test Join Director Form Submission</button>
        <div id="form-result" class="result">Click button to test...</div>
    </div>
    
    <div class="test-section">
        <h2>Test Director URL Generation</h2>
        <button onclick="testDirectorUrlGeneration()">Test URL Generation</button>
        <div id="url-result" class="result">Click button to test...</div>
    </div>
    
    <div class="test-section">
        <h2>Test Navigation to Director Room</h2>
        <button onclick="testDirectorNavigation()">Test Navigation (Will Redirect)</button>
        <div id="nav-result" class="result">Click button to test navigation...</div>
    </div>

    <!-- Import VDO.Ninja scripts -->
    <script src="./thirdparty/adapter.js"></script>
    <script src="./main.js"></script>
    <script src="./webrtc.js"></script>
    
    <!-- Bridge Script -->
    <script src="./app-bridge.js?v=4"></script>
    
    <script>
        function testJoinDirectorForm() {
            const result = document.getElementById('form-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                if (!bridge) {
                    throw new Error('Bridge not available');
                }
                
                // Test the modernJoinDirector function
                const roomName = 'TestDirectorRoom';
                const settings = {
                    roomPassword: 'secret',
                    displayName: 'TestDirector',
                    autoAddGuests: true,
                    startWithAudio: true,
                    startWithVideo: false
                };
                
                result.textContent = `🧪 Testing Join Director Form...
Room Name: ${roomName}
Settings: ${JSON.stringify(settings, null, 2)}

Checking if modernJoinDirector function exists...
Function exists: ${typeof bridge.modernJoinDirector === 'function'}

`;
                
                if (typeof bridge.modernJoinDirector !== 'function') {
                    throw new Error('modernJoinDirector function not found in bridge');
                }
                
                // Override navigation to prevent actual redirect during test
                const originalNavigate = bridge.navigateToRoom;
                let navigationCalled = false;
                let navigationUrl = '';
                
                bridge.navigateToRoom = function(url) {
                    navigationCalled = true;
                    navigationUrl = url;
                    console.log('Navigation intercepted:', url);
                };
                
                const joinResult = bridge.modernJoinDirector(roomName, settings);
                
                // Restore original function
                bridge.navigateToRoom = originalNavigate;
                
                result.textContent += `✅ SUCCESS!
Navigation Called: ${navigationCalled}
Navigation URL: ${navigationUrl}
Result: ${JSON.stringify(joinResult, null, 2)}`;
                
                result.className = 'result success';
                
            } catch (error) {
                result.textContent += `❌ ERROR: ${error.message}
Stack: ${error.stack}`;
                result.className = 'result error';
            }
        }
        
        function testDirectorUrlGeneration() {
            const result = document.getElementById('url-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                if (!bridge) {
                    throw new Error('Bridge not available');
                }
                
                // Test buildDirectorRoomUrl function
                const settings = {
                    roomName: 'TestRoom',
                    roomPassword: 'secret',
                    directorMode: true,
                    autoAddGuests: true,
                    startWithAudio: true,
                    startWithVideo: false
                };
                
                result.textContent = `🔗 Testing Director URL Generation...
Settings: ${JSON.stringify(settings, null, 2)}

Checking if buildDirectorRoomUrl function exists...
Function exists: ${typeof bridge.buildDirectorRoomUrl === 'function'}

`;
                
                if (typeof bridge.buildDirectorRoomUrl !== 'function') {
                    throw new Error('buildDirectorRoomUrl function not found in bridge');
                }
                
                const directorUrl = bridge.buildDirectorRoomUrl(settings);
                
                result.textContent += `✅ SUCCESS!
Generated Director URL: ${directorUrl}

URL Analysis:
- Contains director parameter: ${directorUrl.includes('director=')}
- Contains password parameter: ${directorUrl.includes('password=')}
- Base URL: ${directorUrl.split('?')[0]}
- Parameters: ${directorUrl.split('?')[1]}`;
                
                result.className = 'result success';
                
            } catch (error) {
                result.textContent += `❌ ERROR: ${error.message}
Stack: ${error.stack}`;
                result.className = 'result error';
            }
        }
        
        function testDirectorNavigation() {
            const result = document.getElementById('nav-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                if (!bridge) {
                    throw new Error('Bridge not available');
                }
                
                result.textContent = `🚀 Testing Director Navigation...
This will actually navigate to the director room in 3 seconds...

Room: TestNavigationRoom
Password: test123
`;
                
                setTimeout(() => {
                    const roomName = 'TestNavigationRoom';
                    const settings = {
                        roomPassword: 'test123',
                        displayName: 'NavigationTest',
                        autoAddGuests: true,
                        startWithAudio: true,
                        startWithVideo: false
                    };
                    
                    console.log('Calling modernJoinDirector for navigation test...');
                    bridge.modernJoinDirector(roomName, settings);
                }, 3000);
                
            } catch (error) {
                result.textContent = `❌ ERROR: ${error.message}
Stack: ${error.stack}`;
                result.className = 'result error';
            }
        }
        
        // Auto-check bridge status when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const result = document.getElementById('form-result');
                if (window.vdoNinjaBridge && window.vdoNinjaBridge.version) {
                    result.textContent = `✅ Bridge loaded successfully!
Version: ${window.vdoNinjaBridge.version}

Available Functions:
• modernCreateRoom: ${typeof window.vdoNinjaBridge.modernCreateRoom === 'function'}
• modernJoinRoom: ${typeof window.vdoNinjaBridge.modernJoinRoom === 'function'}
• modernJoinDirector: ${typeof window.vdoNinjaBridge.modernJoinDirector === 'function'}
• buildDirectorRoomUrl: ${typeof window.vdoNinjaBridge.buildDirectorRoomUrl === 'function'}
• navigateToRoom: ${typeof window.vdoNinjaBridge.navigateToRoom === 'function'}

Ready to test director functionality...`;
                } else {
                    result.textContent = `❌ Bridge not loaded properly. Please refresh the page.`;
                }
            }, 1000);
        });
    </script>
</body>
</html>
