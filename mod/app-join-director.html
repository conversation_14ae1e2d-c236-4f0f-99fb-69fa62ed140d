<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Join as Director - VD<PERSON>.Ninja Modern</title>
    
    <!-- Meta tags -->
    <meta name="description" content="Join an existing VDO.Ninja room as a director with advanced controls">
    <meta name="keywords" content="video conference, streaming, director, room management">
    <meta name="author" content="VDO.Ninja">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="./favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="./apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="./favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./favicon-16x16.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="./app-styles.css">
    
    <!-- Import ALL original VDO.Ninja scripts for functionality preservation -->
    <script src="./thirdparty/adapter.js"></script>
    <script src="./main.js"></script>
    <script src="./webrtc.js"></script>
</head>
<body>
    <!-- Hidden original functionality container -->
    <div id="original-functionality" style="display: none;">
        <!-- Original VDO.Ninja elements will be preserved here -->
    </div>

    <!-- Modern UI -->
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="header-left">
                    <a href="./app-landing.html" class="back-button">
                        <span class="icon">←</span>
                        Back
                    </a>
                </div>
                
                <div class="header-center">
                    <div class="logo">
                        <span class="logo-icon">📹</span>
                        <span class="logo-text">VDO.Ninja</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="connection-status" id="connection-status">Ready</div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <div class="max-w-2xl mx-auto w-full">
                <!-- Join Director Form -->
                <form id="join-director-form" data-modern-form="join-director" class="space-y-lg">
                    <!-- Room Information -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">🎬</span>
                                Join as Director
                            </h2>
                            <p class="card-description">
                                Enter an existing room as a director with advanced controls and management capabilities.
                            </p>
                        </div>
                        
                        <div class="card-content space-y-md">
                            <div class="form-group">
                                <label for="roomName" class="form-label">Room Name</label>
                                <input 
                                    type="text" 
                                    id="roomName" 
                                    name="roomName" 
                                    class="form-input" 
                                    placeholder="Enter room name"
                                    required
                                    autocomplete="off"
                                >
                                <p class="form-help">The name of the existing room you want to join as director</p>
                            </div>
                            
                            <div class="form-group">
                                <label for="roomPassword" class="form-label">Room Password</label>
                                <input 
                                    type="password" 
                                    id="roomPassword" 
                                    name="roomPassword" 
                                    class="form-input" 
                                    placeholder="Enter password (if required)"
                                    autocomplete="new-password"
                                >
                                <p class="form-help">Leave empty if the room doesn't have a password</p>
                            </div>
                        </div>
                    </div>

                    <!-- Director Settings -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <span class="icon">⚙️</span>
                                Director Settings
                            </h3>
                        </div>
                        
                        <div class="card-content space-y-md">
                            <div class="form-group">
                                <label for="displayName" class="form-label">Your Display Name</label>
                                <input 
                                    type="text" 
                                    id="displayName" 
                                    name="displayName" 
                                    class="form-input" 
                                    placeholder="Director"
                                    value="Director"
                                >
                                <p class="form-help">How you'll appear to other participants</p>
                            </div>
                            
                            <div class="settings-grid">
                                <div class="setting-item">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="form-label mb-0">Auto-add Guests</label>
                                            <p class="text-xs text-muted">Automatically accept new participants</p>
                                        </div>
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="autoAddGuests" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="setting-item">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="form-label mb-0">Start with Audio</label>
                                            <p class="text-xs text-muted">Enable microphone when joining</p>
                                        </div>
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="startWithAudio" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="setting-item">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="form-label mb-0">Start with Video</label>
                                            <p class="text-xs text-muted">Enable camera when joining</p>
                                        </div>
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="startWithVideo">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary btn-lg w-full" data-action="join-director">
                            <span class="icon">🎬</span>
                            Join as Director
                        </button>
                    </div>
                </form>

                <!-- Quick Access -->
                <div class="card mt-lg">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="icon">⚡</span>
                            Quick Access
                        </h3>
                    </div>
                    
                    <div class="card-content">
                        <div class="quick-actions">
                            <button onclick="joinRecentRoom()" class="quick-action-btn">
                                <span class="icon">🕒</span>
                                <span>Join Last Room</span>
                            </button>
                            <button onclick="showRoomHelp()" class="quick-action-btn">
                                <span class="icon">❓</span>
                                <span>Director Help</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Status Messages -->
        <div id="status-messages" class="status-messages"></div>
        
        <!-- Loading Overlay -->
        <div id="loading-overlay" class="loading-overlay hidden">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">Joining director room...</div>
            </div>
        </div>
    </div>

    <!-- Bridge Script -->
    <script src="./app-bridge.js?v=6"></script>
    
    <script>
        // Initialize join director functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeJoinDirector();
        });
        
        function initializeJoinDirector() {
            // Auto-focus room name input
            document.getElementById('roomName').focus();
            
            // Load last used room name if available
            const lastRoom = localStorage.getItem('vdo-ninja-last-director-room');
            if (lastRoom) {
                document.getElementById('roomName').value = lastRoom;
            }
        }
        
        function joinRecentRoom() {
            const lastRoom = localStorage.getItem('vdo-ninja-last-director-room');
            if (lastRoom) {
                document.getElementById('roomName').value = lastRoom;
                document.getElementById('roomName').focus();
                window.vdoNinjaBridge.showSuccessMessage('Last room loaded: ' + lastRoom);
            } else {
                window.vdoNinjaBridge.showErrorMessage('No recent room found');
            }
        }
        
        function showRoomHelp() {
            const helpText = `Director Mode Features:
            
• Advanced participant management
• Audio/video controls for all participants
• Recording and screenshot capabilities
• OBS scene generation
• Real-time room monitoring
• Guest invitation management

As a director, you'll have full control over the room and all participants.`;
            
            window.vdoNinjaBridge.showSuccessMessage(helpText);
        }
        
        // Override the bridge's join director function to handle UI updates
        const originalJoinDirector = window.vdoNinjaBridge.modernJoinDirector || function() {};
        window.vdoNinjaBridge.modernJoinDirector = function(roomName, settings) {
            try {
                // Save room for quick access
                localStorage.setItem('vdo-ninja-last-director-room', roomName);
                
                // Build director URL and navigate
                const directorUrl = this.buildDirectorRoomUrl({
                    roomName: roomName,
                    roomPassword: settings.roomPassword,
                    directorMode: true,
                    autoAddGuests: settings.autoAddGuests,
                    startWithAudio: settings.startWithAudio,
                    startWithVideo: settings.startWithVideo
                });
                
                console.log('Joining as director with URL:', directorUrl);
                this.navigateToRoom(directorUrl);
                
                return {
                    roomName: roomName,
                    directorUrl: directorUrl,
                    success: true
                };
                
            } catch (error) {
                console.error('Error joining as director:', error);
                throw error;
            }
        };
        
        // The form submission is handled automatically by the bridge
        // via the data-modern-form="join-director" attribute
    </script>
</body>
</html>
