<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Director Room - VDO.Ninja</title>
    
    <!-- Meta Tags -->
    <meta name="description" content="VDO.Ninja director room for advanced stream management and control">
    <meta name="theme-color" content="#0a0e1a">
    
    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="./media/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./media/favicon-16x16.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="./app-styles.css">
    
    <!-- Import ALL original VDO.Ninja scripts for functionality preservation -->
    <script src="./thirdparty/adapter.js"></script>
    <script src="./main.js"></script>
    <script src="./webrtc.js"></script>
    
    <style>
        /* Director room specific styles */
        .director-container {
            height: 100vh;
            display: grid;
            grid-template-columns: 300px 1fr 250px;
            grid-template-rows: auto 1fr auto;
            grid-template-areas:
                "header header header"
                "sidebar main controls"
                "footer footer footer";
            background: var(--primary-bg);
        }

        /* Responsive layout for director room */
        @media (max-width: 1024px) {
            .director-container {
                grid-template-columns: 250px 1fr 200px;
            }
        }

        @media (max-width: 768px) {
            .director-container {
                grid-template-columns: 1fr;
                grid-template-areas:
                    "header"
                    "main"
                    "sidebar"
                    "controls"
                    "footer";
            }
        }
        
        .director-header {
            grid-area: header;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: var(--space-md) var(--space-lg);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .director-sidebar {
            grid-area: sidebar;
            background: var(--secondary-bg);
            border-right: 1px solid var(--glass-border);
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .director-main {
            grid-area: main;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        
        .director-controls {
            grid-area: controls;
            background: var(--secondary-bg);
            border-left: 1px solid var(--glass-border);
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .director-footer {
            grid-area: footer;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-top: 1px solid var(--glass-border);
            padding: var(--space-sm) var(--space-lg);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .scene-tabs {
            display: flex;
            background: var(--tertiary-bg);
            border-radius: var(--radius-lg);
            padding: var(--space-xs);
            margin-bottom: var(--space-md);
        }
        
        .scene-tab {
            flex: 1;
            padding: var(--space-sm) var(--space-md);
            border: none;
            background: transparent;
            color: var(--text-secondary);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--transition-fast);
            font-size: var(--font-size-sm);
        }
        
        .scene-tab.active {
            background: var(--accent-blue);
            color: white;
        }
        
        .preview-area {
            flex: 1;
            background: var(--tertiary-bg);
            border-radius: var(--radius-lg);
            margin: var(--space-md);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .preview-video {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: var(--radius-lg);
        }
        
        .live-indicator {
            position: absolute;
            top: var(--space-md);
            left: var(--space-md);
            background: var(--accent-red);
            color: white;
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-xs);
            font-weight: 600;
            animation: pulse 2s infinite;
        }
        
        .guest-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: var(--space-sm);
            padding: var(--space-md);
        }
        
        .guest-tile {
            background: var(--tertiary-bg);
            border-radius: var(--radius-md);
            padding: var(--space-sm);
            text-align: center;
            position: relative;
            cursor: pointer;
            transition: all var(--transition-fast);
            border: 2px solid transparent;
        }
        
        .guest-tile:hover {
            border-color: var(--accent-blue);
        }
        
        .guest-tile.selected {
            border-color: var(--accent-green);
            background: var(--accent-green);
            color: white;
        }
        
        .guest-video {
            width: 100%;
            height: 80px;
            object-fit: cover;
            border-radius: var(--radius-sm);
            margin-bottom: var(--space-xs);
        }
        
        .guest-name {
            font-size: var(--font-size-xs);
            font-weight: 500;
            margin-bottom: var(--space-xs);
        }
        
        .guest-controls {
            display: flex;
            gap: var(--space-xs);
            justify-content: center;
        }
        
        .guest-control-btn {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: none;
            background: var(--glass-bg);
            color: var(--text-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-fast);
        }
        
        .guest-control-btn:hover {
            background: var(--accent-blue);
            color: white;
        }
        
        .guest-control-btn.muted {
            background: var(--accent-red);
            color: white;
        }
        
        .control-section {
            padding: var(--space-md);
            border-bottom: 1px solid var(--glass-border);
        }
        
        .control-section h3 {
            font-size: var(--font-size-sm);
            font-weight: 600;
            margin-bottom: var(--space-md);
            color: var(--text-primary);
        }
        
        .control-group {
            margin-bottom: var(--space-md);
        }
        
        .control-label {
            display: block;
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
            margin-bottom: var(--space-xs);
        }
        
        .audio-mixer {
            display: flex;
            flex-direction: column;
            gap: var(--space-sm);
        }
        
        .audio-channel {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }
        
        .audio-label {
            font-size: var(--font-size-xs);
            min-width: 60px;
        }
        
        .audio-slider {
            flex: 1;
            height: 4px;
            background: var(--tertiary-bg);
            border-radius: 2px;
            outline: none;
            cursor: pointer;
        }
        
        .audio-meter-small {
            width: 40px;
            height: 4px;
            background: var(--tertiary-bg);
            border-radius: 2px;
            overflow: hidden;
        }
        
        .recording-controls {
            display: flex;
            flex-direction: column;
            gap: var(--space-sm);
        }
        
        .recording-status {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            font-size: var(--font-size-sm);
        }
        
        .recording-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--accent-red);
            animation: pulse 1s infinite;
        }
        
        .obs-links {
            display: flex;
            flex-direction: column;
            gap: var(--space-sm);
        }
        
        .obs-link {
            background: var(--tertiary-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-sm);
            padding: var(--space-xs) var(--space-sm);
            font-size: var(--font-size-xs);
            font-family: monospace;
            word-break: break-all;
            position: relative;
        }
        
        .obs-copy-btn {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 20px;
            height: 20px;
            border: none;
            background: var(--accent-blue);
            color: white;
            border-radius: 2px;
            cursor: pointer;
            font-size: 10px;
        }
        
        @media (max-width: 1024px) {
            .director-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr auto;
                grid-template-areas: 
                    "header"
                    "main"
                    "footer";
            }
            
            .director-sidebar,
            .director-controls {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Hidden original functionality container -->
    <div id="original-functionality" style="display: none;">
        <!-- Original VDO.Ninja elements will be preserved here -->
    </div>

    <!-- Modern UI Container -->
    <div id="modern-app" class="director-container">
        <!-- Director Header -->
        <header class="director-header">
            <div class="flex items-center gap-md">
                <a href="./app-landing.html" class="btn btn-secondary btn-sm">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                    </svg>
                    Back
                </a>
                <h1 class="text-lg font-semibold" id="director-title">Director: Loading...</h1>
                <div class="status-indicator">
                    <div class="status-dot connected" data-connection-status></div>
                    <span class="text-sm" data-connection-status>Connected</span>
                </div>
            </div>
            
            <div class="flex items-center gap-sm">
                <button class="btn btn-success btn-sm" onclick="goLive()" id="live-btn">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                    </svg>
                    Go Live
                </button>
                
                <button class="btn btn-secondary btn-sm" onclick="toggleRecording()" id="record-btn">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clip-rule="evenodd"/>
                    </svg>
                    Record
                </button>
                
                <button class="btn btn-secondary btn-sm" onclick="inviteGuests()">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>
                    </svg>
                    Invite
                </button>
            </div>
        </header>

        <!-- Sidebar - Guest Management -->
        <aside class="director-sidebar">
            <div class="control-section">
                <h3>Guests</h3>
                <div class="guest-grid" id="guest-grid">
                    <!-- Guests will be added dynamically -->
                    <div class="text-center text-muted p-lg">
                        <div class="text-2xl mb-sm">👥</div>
                        <p class="text-xs">No guests connected</p>
                        <button class="btn btn-primary btn-sm mt-sm" onclick="inviteGuests()">Invite Guests</button>
                    </div>
                </div>
            </div>
            
            <div class="control-section">
                <h3>Bulk Actions</h3>
                <div class="flex flex-col gap-sm">
                    <button class="btn btn-secondary btn-sm" onclick="muteAllGuests()">
                        <svg width="14" height="14" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z"/>
                        </svg>
                        Mute All
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="unmuteAllGuests()">
                        <svg width="14" height="14" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0 9.972 9.972 0 010 14.142 1 1 0 11-1.414-1.414 7.971 7.971 0 000-11.314 1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                        Unmute All
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="hideAllVideo()">
                        <svg width="14" height="14" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3.28 2.22a.75.75 0 00-1.06 1.06l14.5 14.5a.75.75 0 101.06-1.06L3.28 2.22z"/>
                        </svg>
                        Hide All Video
                    </button>
                </div>
            </div>
        </aside>

        <!-- Main Preview Area -->
        <main class="director-main">
            <!-- Scene Tabs -->
            <div class="scene-tabs">
                <button class="scene-tab active" onclick="switchScene(0)" data-scene="0">Scene 1</button>
                <button class="scene-tab" onclick="switchScene(1)" data-scene="1">Scene 2</button>
                <button class="scene-tab" onclick="switchScene(2)" data-scene="2">Scene 3</button>
                <button class="scene-tab" onclick="switchScene(3)" data-scene="3">Scene 4</button>
            </div>
            
            <!-- Preview Area -->
            <div class="preview-area">
                <video id="preview-video" class="preview-video" autoplay muted playsinline></video>
                <div id="live-indicator" class="live-indicator" style="display: none;">● LIVE</div>
                <div class="text-center text-muted" id="preview-placeholder">
                    <div class="text-4xl mb-md">🎬</div>
                    <h3>Scene Preview</h3>
                    <p>Select guests to add to the current scene</p>
                </div>
            </div>
        </main>

        <!-- Controls Panel -->
        <aside class="director-controls">
            <!-- Audio Mixer -->
            <div class="control-section">
                <h3>Audio Mixer</h3>
                <div class="audio-mixer" id="audio-mixer">
                    <div class="audio-channel">
                        <span class="audio-label">Master</span>
                        <input type="range" class="audio-slider" min="0" max="100" value="75">
                        <div class="audio-meter-small">
                            <div style="width: 60%; height: 100%; background: var(--accent-green);"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recording Controls -->
            <div class="control-section">
                <h3>Recording</h3>
                <div class="recording-controls">
                    <div class="recording-status" id="recording-status">
                        <div class="recording-dot" style="display: none;"></div>
                        <span class="text-sm">Not Recording</span>
                    </div>
                    <button class="btn btn-danger btn-sm w-full" onclick="toggleRecording()" id="record-control-btn">
                        Start Recording
                    </button>
                    <div class="control-group">
                        <label class="control-label">Quality</label>
                        <select class="form-input" style="font-size: var(--font-size-xs);">
                            <option>1080p</option>
                            <option>720p</option>
                            <option>480p</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- OBS Integration -->
            <div class="control-section">
                <h3>OBS Links</h3>
                <div class="obs-links">
                    <div class="control-group">
                        <label class="control-label">Scene 1</label>
                        <div class="obs-link">
                            <span id="obs-scene-1">Loading...</span>
                            <button class="obs-copy-btn" onclick="copyOBSLink('obs-scene-1')">📋</button>
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label">Scene 2</label>
                        <div class="obs-link">
                            <span id="obs-scene-2">Loading...</span>
                            <button class="obs-copy-btn" onclick="copyOBSLink('obs-scene-2')">📋</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Stream Settings -->
            <div class="control-section">
                <h3>Stream Settings</h3>
                <div class="control-group">
                    <label class="control-label">Bitrate</label>
                    <select class="form-input" style="font-size: var(--font-size-xs);">
                        <option>Auto</option>
                        <option>2500 kbps</option>
                        <option>1500 kbps</option>
                        <option>1000 kbps</option>
                    </select>
                </div>
                <div class="control-group">
                    <label class="control-label">Layout</label>
                    <select class="form-input" style="font-size: var(--font-size-xs);" onchange="changeLayout(this.value)">
                        <option value="grid">Grid</option>
                        <option value="interview">Interview</option>
                        <option value="presentation">Presentation</option>
                        <option value="custom">Custom</option>
                    </select>
                </div>
            </div>
        </aside>

        <!-- Footer -->
        <footer class="director-footer">
            <div class="flex items-center gap-md">
                <span class="text-sm text-muted">Director Mode Active</span>
                <div class="flex items-center gap-sm">
                    <div class="status-dot connected"></div>
                    <span class="text-xs">Connected to room</span>
                </div>
            </div>
            
            <div class="flex items-center gap-sm text-xs text-muted">
                <span>Guests: <span id="guest-count">0</span></span>
                <span>•</span>
                <span>Scenes: 4</span>
                <span>•</span>
                <span>Quality: HD</span>
            </div>
        </footer>
    </div>

    <!-- Bridge Script -->
    <script src="./app-bridge.js?v=5"></script>
    
    <script>
        // Director room specific JavaScript
        let isLive = false;
        let isRecording = false;
        let currentScene = 0;
        let guests = [];
        let selectedGuests = [];
        
        // Initialize director room on load
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const directorId = urlParams.get('director');
            
            if (directorId) {
                document.getElementById('director-title').textContent = `Director: ${directorId}`;
                initializeDirectorRoom(directorId);
            } else {
                window.location.href = './app-create-room.html';
            }
        });
        
        function initializeDirectorRoom(directorId) {
            console.log('Initializing director room:', directorId);
            
            // Generate OBS links
            generateOBSLinks(directorId);
            
            // Setup event listeners
            setupDirectorEventListeners();
            
            // Simulate some guests for demo
            setTimeout(() => {
                addDemoGuests();
            }, 2000);
        }
        
        function generateOBSLinks(directorId) {
            const baseUrl = window.location.origin + window.location.pathname.replace('app-director-room.html', '');
            
            for (let i = 1; i <= 2; i++) {
                const obsLink = `${baseUrl}index.html?view=${directorId}&scene=${i-1}`;
                document.getElementById(`obs-scene-${i}`).textContent = obsLink;
            }
        }
        
        function setupDirectorEventListeners() {
            // Keyboard shortcuts for director
            document.addEventListener('keydown', function(e) {
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'SELECT') return;
                
                switch(e.key) {
                    case '1':
                    case '2':
                    case '3':
                    case '4':
                        switchScene(parseInt(e.key) - 1);
                        break;
                    case ' ':
                        e.preventDefault();
                        goLive();
                        break;
                    case 'r':
                        toggleRecording();
                        break;
                }
            });
        }
        
        function addDemoGuests() {
            const demoGuests = [
                { id: 'guest1', name: 'Alice', video: true, audio: true },
                { id: 'guest2', name: 'Bob', video: true, audio: false },
                { id: 'guest3', name: 'Charlie', video: false, audio: true }
            ];
            
            demoGuests.forEach(guest => addGuest(guest));
        }
        
        function addGuest(guestData) {
            guests.push(guestData);
            
            const guestGrid = document.getElementById('guest-grid');
            
            // Remove placeholder if it exists
            const placeholder = guestGrid.querySelector('.text-center');
            if (placeholder) {
                placeholder.remove();
            }
            
            const guestTile = document.createElement('div');
            guestTile.className = 'guest-tile';
            guestTile.dataset.guestId = guestData.id;
            guestTile.onclick = () => toggleGuestSelection(guestData.id);
            
            guestTile.innerHTML = `
                <div class="guest-video" style="background: var(--tertiary-bg); display: flex; align-items: center; justify-content: center; color: var(--text-muted);">
                    ${guestData.video ? '📹' : '🎤'}
                </div>
                <div class="guest-name">${guestData.name}</div>
                <div class="guest-controls">
                    <button class="guest-control-btn ${guestData.audio ? '' : 'muted'}" onclick="event.stopPropagation(); toggleGuestAudio('${guestData.id}')" title="Toggle Audio">
                        <svg width="12" height="12" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                    <button class="guest-control-btn ${guestData.video ? '' : 'muted'}" onclick="event.stopPropagation(); toggleGuestVideo('${guestData.id}')" title="Toggle Video">
                        <svg width="12" height="12" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                        </svg>
                    </button>
                    <button class="guest-control-btn" onclick="event.stopPropagation(); removeGuest('${guestData.id}')" title="Remove Guest">
                        <svg width="12" height="12" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                </div>
            `;
            
            guestGrid.appendChild(guestTile);
            
            // Update guest count
            document.getElementById('guest-count').textContent = guests.length;
            
            // Add audio mixer channel
            addAudioMixerChannel(guestData);
        }
        
        function addAudioMixerChannel(guestData) {
            const audioMixer = document.getElementById('audio-mixer');
            const channel = document.createElement('div');
            channel.className = 'audio-channel';
            channel.innerHTML = `
                <span class="audio-label">${guestData.name}</span>
                <input type="range" class="audio-slider" min="0" max="100" value="75" data-guest="${guestData.id}">
                <div class="audio-meter-small">
                    <div style="width: ${Math.random() * 80 + 10}%; height: 100%; background: var(--accent-green);"></div>
                </div>
            `;
            audioMixer.appendChild(channel);
        }
        
        function toggleGuestSelection(guestId) {
            const guestTile = document.querySelector(`[data-guest-id="${guestId}"]`);
            
            if (selectedGuests.includes(guestId)) {
                selectedGuests = selectedGuests.filter(id => id !== guestId);
                guestTile.classList.remove('selected');
            } else {
                selectedGuests.push(guestId);
                guestTile.classList.add('selected');
            }
            
            console.log('Selected guests:', selectedGuests);
        }
        
        function switchScene(sceneIndex) {
            currentScene = sceneIndex;
            
            // Update scene tabs
            document.querySelectorAll('.scene-tab').forEach((tab, index) => {
                if (index === sceneIndex) {
                    tab.classList.add('active');
                } else {
                    tab.classList.remove('active');
                }
            });
            
            console.log('Switched to scene:', sceneIndex);
            window.vdoNinjaBridge.modernDirectorControl('change-scene', null, { sceneId: sceneIndex });
        }
        
        function goLive() {
            isLive = !isLive;
            const liveBtn = document.getElementById('live-btn');
            const liveIndicator = document.getElementById('live-indicator');
            
            if (isLive) {
                liveBtn.textContent = 'Stop Live';
                liveBtn.classList.remove('btn-success');
                liveBtn.classList.add('btn-danger');
                liveIndicator.style.display = 'block';
                window.vdoNinjaBridge.showSuccessMessage('Stream is now LIVE!');
            } else {
                liveBtn.textContent = 'Go Live';
                liveBtn.classList.remove('btn-danger');
                liveBtn.classList.add('btn-success');
                liveIndicator.style.display = 'none';
                window.vdoNinjaBridge.showSuccessMessage('Stream stopped');
            }
        }
        
        function toggleRecording() {
            isRecording = !isRecording;
            const recordBtn = document.getElementById('record-btn');
            const recordControlBtn = document.getElementById('record-control-btn');
            const recordingStatus = document.getElementById('recording-status');
            const recordingDot = recordingStatus.querySelector('.recording-dot');
            const recordingText = recordingStatus.querySelector('span');
            
            if (isRecording) {
                recordBtn.classList.add('btn-danger');
                recordBtn.classList.remove('btn-secondary');
                recordControlBtn.textContent = 'Stop Recording';
                recordControlBtn.classList.add('btn-danger');
                recordingDot.style.display = 'block';
                recordingText.textContent = 'Recording...';
                
                window.vdoNinjaBridge.modernDirectorControl('start-recording', null, { quality: '1080p' });
            } else {
                recordBtn.classList.remove('btn-danger');
                recordBtn.classList.add('btn-secondary');
                recordControlBtn.textContent = 'Start Recording';
                recordControlBtn.classList.remove('btn-danger');
                recordingDot.style.display = 'none';
                recordingText.textContent = 'Not Recording';
                
                window.vdoNinjaBridge.modernDirectorControl('stop-recording');
            }
        }
        
        function toggleGuestAudio(guestId) {
            const guest = guests.find(g => g.id === guestId);
            if (guest) {
                guest.audio = !guest.audio;
                const btn = document.querySelector(`[data-guest-id="${guestId}"] .guest-control-btn`);
                btn.classList.toggle('muted', !guest.audio);
                
                if (guest.audio) {
                    window.vdoNinjaBridge.modernDirectorControl('unmute-guest', guestId);
                } else {
                    window.vdoNinjaBridge.modernDirectorControl('mute-guest', guestId);
                }
            }
        }
        
        function toggleGuestVideo(guestId) {
            const guest = guests.find(g => g.id === guestId);
            if (guest) {
                guest.video = !guest.video;
                const btns = document.querySelectorAll(`[data-guest-id="${guestId}"] .guest-control-btn`);
                btns[1].classList.toggle('muted', !guest.video);
                console.log('Guest video toggled:', guestId, guest.video);
            }
        }
        
        function removeGuest(guestId) {
            if (confirm('Remove this guest from the room?')) {
                guests = guests.filter(g => g.id !== guestId);
                document.querySelector(`[data-guest-id="${guestId}"]`).remove();
                
                // Remove from audio mixer
                const audioChannel = document.querySelector(`[data-guest="${guestId}"]`).closest('.audio-channel');
                if (audioChannel) audioChannel.remove();
                
                // Update guest count
                document.getElementById('guest-count').textContent = guests.length;
                
                window.vdoNinjaBridge.modernDirectorControl('remove-guest', guestId);
            }
        }
        
        function muteAllGuests() {
            guests.forEach(guest => {
                if (guest.audio) {
                    toggleGuestAudio(guest.id);
                }
            });
        }
        
        function unmuteAllGuests() {
            guests.forEach(guest => {
                if (!guest.audio) {
                    toggleGuestAudio(guest.id);
                }
            });
        }
        
        function hideAllVideo() {
            guests.forEach(guest => {
                if (guest.video) {
                    toggleGuestVideo(guest.id);
                }
            });
        }
        
        function inviteGuests() {
            const urlParams = new URLSearchParams(window.location.search);
            const directorId = urlParams.get('director');
            const baseUrl = window.location.origin + window.location.pathname.replace('app-director-room.html', '');
            const inviteLink = `${baseUrl}app-join-room.html?room=${directorId}`;
            
            window.vdoNinjaBridge.copyToClipboard(inviteLink);
        }
        
        function copyOBSLink(elementId) {
            const link = document.getElementById(elementId).textContent;
            window.vdoNinjaBridge.copyToClipboard(link);
        }
        
        function changeLayout(layout) {
            console.log('Changing layout to:', layout);
            window.vdoNinjaBridge.showSuccessMessage(`Layout changed to ${layout}`);
        }
    </script>
</body>
</html>
