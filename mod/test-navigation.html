<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Navigation - VDO.Ninja Modern</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1f2e;
            color: white;
        }
        .test-section {
            background: #2a2f3e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #4a9eff;
        }
        button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3b82f6;
        }
        .result {
            background: #0a0e1a;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            border: 1px solid #333;
        }
        .warning {
            background: #2a1f0a;
            border-color: #ff9500;
            color: #ffb84d;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Navigation Test - VDO.Ninja Modern Interface</h1>
    
    <div class="warning">
        <strong>⚠️ Warning:</strong> These tests will actually navigate to the room interfaces. 
        Use the browser back button to return to this test page.
    </div>
    
    <div class="test-section">
        <h2>Test Modern Interface Navigation</h2>
        <p>These buttons will test the new navigation that keeps you in the modern interface:</p>
        
        <button onclick="testDirectorNavigation()">Test Director Room Navigation</button>
        <button onclick="testParticipantNavigation()">Test Participant Room Navigation</button>
        <button onclick="testNavigationWithPassword()">Test Navigation with Password</button>
        
        <div id="navigation-result" class="result">Click buttons to test navigation...</div>
    </div>
    
    <div class="test-section">
        <h2>URL Building Preview</h2>
        <p>Preview the URLs that will be generated without actually navigating:</p>
        
        <button onclick="previewDirectorUrl()">Preview Director URL</button>
        <button onclick="previewParticipantUrl()">Preview Participant URL</button>
        
        <div id="preview-result" class="result">Click buttons to preview URLs...</div>
    </div>

    <!-- Import VDO.Ninja scripts -->
    <script src="./thirdparty/adapter.js"></script>
    <script src="./main.js"></script>
    <script src="./webrtc.js"></script>
    
    <!-- Bridge Script -->
    <script src="./app-bridge.js?v=3"></script>
    
    <script>
        function testDirectorNavigation() {
            const result = document.getElementById('navigation-result');
            
            try {
                result.textContent = '🧪 Testing Director Room Navigation...\n\nThis will navigate to the modern director interface in 3 seconds...';
                
                setTimeout(() => {
                    // Simulate director room creation
                    const directorUrl = 'https://vdo.dpnmediaworks.com/index.html?director=TestRoom123&password=secret&quality=1080p';
                    
                    console.log('Testing navigation with director URL:', directorUrl);
                    window.vdoNinjaBridge.navigateToRoom(directorUrl);
                }, 3000);
                
            } catch (error) {
                result.textContent = `❌ Error: ${error.message}`;
            }
        }
        
        function testParticipantNavigation() {
            const result = document.getElementById('navigation-result');
            
            try {
                result.textContent = '🧪 Testing Participant Room Navigation...\n\nThis will navigate to the modern communications interface in 3 seconds...';
                
                setTimeout(() => {
                    // Simulate participant room joining
                    const participantUrl = 'https://vdo.dpnmediaworks.com/index.html?room=TestRoom123&label=TestUser&password=secret';
                    
                    console.log('Testing navigation with participant URL:', participantUrl);
                    window.vdoNinjaBridge.navigateToRoom(participantUrl);
                }, 3000);
                
            } catch (error) {
                result.textContent = `❌ Error: ${error.message}`;
            }
        }
        
        function testNavigationWithPassword() {
            const result = document.getElementById('navigation-result');
            
            try {
                result.textContent = '🧪 Testing Navigation with Password...\n\nThis will navigate to the modern interface with password protection in 3 seconds...';
                
                setTimeout(() => {
                    // Simulate room with password and additional settings
                    const urlWithPassword = 'https://vdo.dpnmediaworks.com/index.html?room=SecureRoom&label=SecureUser&password=topsecret&novideo=1&muted=1';
                    
                    console.log('Testing navigation with password URL:', urlWithPassword);
                    window.vdoNinjaBridge.navigateToRoom(urlWithPassword);
                }, 3000);
                
            } catch (error) {
                result.textContent = `❌ Error: ${error.message}`;
            }
        }
        
        function previewDirectorUrl() {
            const result = document.getElementById('preview-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                // Test director URL building
                const settings = {
                    roomName: 'PreviewRoom',
                    roomPassword: 'preview123',
                    videoQuality: '1080p',
                    directorMode: true
                };
                
                const directorUrl = bridge.buildDirectorRoomUrl(settings);
                
                // Parse what the modern navigation would generate
                const originalUrl = new URL(directorUrl);
                const params = new URLSearchParams(originalUrl.search);
                
                let modernUrl = `${window.location.origin}${window.location.pathname.replace(/test-navigation\.html$/, 'app-director-room.html')}?director=${encodeURIComponent(params.get('director'))}`;
                
                if (params.has('password')) {
                    modernUrl += `&password=${encodeURIComponent(params.get('password'))}`;
                }
                if (params.has('quality')) {
                    modernUrl += `&quality=${encodeURIComponent(params.get('quality'))}`;
                }
                
                result.textContent = `🔗 Director URL Preview:

Original VDO.Ninja URL:
${directorUrl}

Modern Interface URL (where you'll actually go):
${modernUrl}

✅ This keeps you in the modern interface!`;
                
            } catch (error) {
                result.textContent = `❌ Error: ${error.message}`;
            }
        }
        
        function previewParticipantUrl() {
            const result = document.getElementById('preview-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                // Test participant URL building
                const roomId = 'PreviewRoom';
                const settings = {
                    displayName: 'PreviewUser',
                    roomPassword: 'preview123',
                    audioOnly: true,
                    startMuted: true
                };
                
                const participantUrl = bridge.buildJoinRoomUrl(roomId, settings);
                
                // Parse what the modern navigation would generate
                const originalUrl = new URL(participantUrl);
                const params = new URLSearchParams(originalUrl.search);
                
                let modernUrl = `${window.location.origin}${window.location.pathname.replace(/test-navigation\.html$/, 'app-comms-room.html')}?room=${encodeURIComponent(params.get('room'))}`;
                
                if (params.has('password')) {
                    modernUrl += `&password=${encodeURIComponent(params.get('password'))}`;
                }
                if (params.has('label')) {
                    modernUrl += `&displayName=${encodeURIComponent(params.get('label'))}`;
                }
                if (params.has('novideo')) {
                    modernUrl += `&audioOnly=true`;
                }
                if (params.has('muted')) {
                    modernUrl += `&startMuted=true`;
                }
                
                result.textContent = `🔗 Participant URL Preview:

Original VDO.Ninja URL:
${participantUrl}

Modern Interface URL (where you'll actually go):
${modernUrl}

✅ This keeps you in the modern interface!`;
                
            } catch (error) {
                result.textContent = `❌ Error: ${error.message}`;
            }
        }
        
        // Auto-check bridge status when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const result = document.getElementById('navigation-result');
                if (window.vdoNinjaBridge && window.vdoNinjaBridge.version) {
                    result.textContent = `✅ Bridge loaded successfully!\nVersion: ${window.vdoNinjaBridge.version}\n\nReady to test navigation...`;
                } else {
                    result.textContent = `❌ Bridge not loaded properly. Please refresh the page.`;
                }
            }, 1000);
        });
    </script>
</body>
</html>
