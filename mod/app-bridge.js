/**
 * VDO.Ninja Modern UI Bridge
 * Connects modern UI components to existing VDO.Ninja functionality
 * Preserves 100% of original functionality while providing modern interface
 */

class VDONinjaBridge {
  constructor() {
    this.version = '6.0.0'; // Version for cache busting - Simplified to always use classic interface
    this.originalFunctions = {};
    this.modernUI = {};
    this.isInitialized = false;

    console.log('VDO.Ninja Bridge version:', this.version);

    // Wait for DOM and original scripts to load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.init());
    } else {
      this.init();
    }
  }

  init() {
    console.log('VDO.Ninja Modern UI Bridge initializing...');

    // Store references to original functions
    this.storeOriginalFunctions();

    // Initialize modern UI components
    this.initializeModernUI();

    // Set up event listeners
    this.setupEventListeners();

    this.isInitialized = true;
    console.log('VDO.Ninja Modern UI Bridge initialized successfully');
    console.log('Available functions:', Object.keys(this.originalFunctions));
  }

  storeOriginalFunctions() {
    // VDO.Ninja uses URL-based navigation, not function calls
    // We'll implement the same URL-based approach in our modern UI
    this.originalFunctions = {
      // These are our own implementations that work with VDO.Ninja's URL-based system
      navigateToRoom: this.navigateToRoom.bind(this),
      sanitizeRoomName: this.sanitizeRoomName.bind(this),
      generateString: this.generateRandomString.bind(this)
    };
  }

  initializeModernUI() {
    // Initialize modern UI state management
    this.modernUI = {
      currentView: 'landing',
      roomSettings: {},
      userPreferences: this.loadUserPreferences(),
      connectionState: 'disconnected'
    };
  }

  setupEventListeners() {
    // Global event listeners for modern UI interactions
    document.addEventListener('click', (e) => this.handleGlobalClick(e));
    document.addEventListener('submit', (e) => this.handleFormSubmit(e));
    
    // Window events
    window.addEventListener('beforeunload', () => this.saveUserPreferences());
    window.addEventListener('resize', () => this.handleResize());
  }

  // Modern UI wrapper functions that use VDO.Ninja's URL-based navigation
  modernCreateRoom(settings = {}) {
    console.log('Modern UI: Creating room with settings:', settings);
    console.log('modernCreateRoom called - this should work now');

    try {
      // Validate settings using modern UI validation
      const validatedSettings = this.validateRoomSettings(settings);
      console.log('Validated settings:', validatedSettings);

      // Build URL for director room (VDO.Ninja's room creation approach)
      const roomUrl = this.buildDirectorRoomUrl(validatedSettings);
      console.log('Generated room URL:', roomUrl);

      // Check if director mode is enabled and redirect accordingly
      if (validatedSettings.directorMode || settings.directorMode) {
        console.log('Director mode enabled - redirecting to classic VDO.Ninja director interface');
        this.navigateToRoom(roomUrl);
        this.showSuccessMessage('Room created successfully! Redirecting to VDO.Ninja director interface...');
      } else {
        console.log('Director mode not enabled - showing room links only');
        this.showSuccessMessage('Room created successfully!');
      }

      return {
        roomName: validatedSettings.roomName,
        roomUrl: roomUrl,
        success: true
      };
    } catch (error) {
      console.error('Error creating room:', error);
      this.showErrorMessage('Failed to create room: ' + error.message);
      throw error;
    }
  }

  modernJoinRoom(roomId, settings = {}) {
    console.log('Modern UI: Joining room:', roomId, 'with settings:', settings);
    console.log('modernJoinRoom called - this should work now');

    try {
      // Sanitize room name using our own function
      const sanitizedRoomId = this.sanitizeRoomName(roomId);
      console.log('Sanitized room ID:', sanitizedRoomId);

      // Build URL for joining room (VDO.Ninja's approach)
      const joinUrl = this.buildJoinRoomUrl(sanitizedRoomId, settings);
      console.log('Generated join URL:', joinUrl);

      // Navigate to the classic VDO.Ninja interface with room parameters
      this.navigateToRoom(joinUrl);

      this.updateConnectionState('connected');
      this.showSuccessMessage('Joining room... Redirecting to VDO.Ninja interface...');

      return {
        roomId: sanitizedRoomId,
        joinUrl: joinUrl,
        success: true
      };
    } catch (error) {
      console.error('Error joining room:', error);
      this.showErrorMessage('Failed to join room: ' + error.message);
      throw error;
    }
  }

  modernJoinDirector(roomName, settings = {}) {
    console.log('Modern UI: Joining as director:', roomName, 'with settings:', settings);

    try {
      // Validate room name
      if (!roomName || roomName.trim() === '') {
        throw new Error('Please enter a room name');
      }

      // Show loading state
      this.showLoadingState('Joining director room...');

      // Sanitize room name using our own function
      const sanitizedRoomName = this.sanitizeRoomName(roomName);
      console.log('Sanitized room name:', sanitizedRoomName);

      // Build URL for director room
      const directorSettings = {
        roomName: sanitizedRoomName,
        roomPassword: settings.roomPassword,
        directorMode: true,
        autoAddGuests: settings.autoAddGuests,
        startWithAudio: settings.startWithAudio,
        startWithVideo: settings.startWithVideo
      };

      const directorUrl = this.buildDirectorRoomUrl(directorSettings);
      console.log('Generated director URL:', directorUrl);

      // Navigate to the classic VDO.Ninja director interface
      this.navigateToRoom(directorUrl);

      this.updateConnectionState('connected');
      this.showSuccessMessage('Joining as director... Redirecting to VDO.Ninja interface...');

      return {
        roomName: sanitizedRoomName,
        directorUrl: directorUrl,
        success: true
      };
    } catch (error) {
      console.error('Error joining as director:', error);
      this.hideLoadingState();
      this.showErrorMessage('Failed to join as director: ' + error.message);
      throw error;
    }
  }

  modernHangup() {
    console.log('Modern UI: Hanging up...');
    
    try {
      if (this.originalFunctions.hangup) {
        const result = this.originalFunctions.hangup();
        this.updateConnectionState('disconnected');
        this.showSuccessMessage('Disconnected successfully');
        return result;
      }
    } catch (error) {
      console.error('Error hanging up:', error);
      this.showErrorMessage('Error disconnecting: ' + error.message);
    }
  }

  // Modern UI helper functions
  validateRoomSettings(settings) {
    const validated = { ...settings };

    // Validate room name
    if (!validated.roomName || validated.roomName.trim() === '') {
      validated.roomName = this.generateRoomName();
    } else {
      // Sanitize the provided room name
      validated.roomName = this.sanitizeRoomName(validated.roomName);
    }

    // Validate other settings
    if (validated.roomName === 'test') {
      throw new Error('Please enter a unique room name');
    }

    return validated;
  }

  generateRoomName() {
    // Use our own random string generation
    return this.generateRandomString(10);
  }

  showLoadingState(message = 'Loading...') {
    // Create or update loading overlay
    let overlay = document.getElementById('modern-loading-overlay');
    if (!overlay) {
      overlay = document.createElement('div');
      overlay.id = 'modern-loading-overlay';
      overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      overlay.innerHTML = `
        <div class="glass-card text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-blue mx-auto mb-4"></div>
          <p class="text-primary">${message}</p>
        </div>
      `;
      document.body.appendChild(overlay);
    } else {
      overlay.querySelector('p').textContent = message;
      overlay.classList.remove('hidden');
    }
  }

  hideLoadingState() {
    const overlay = document.getElementById('modern-loading-overlay');
    if (overlay) {
      overlay.classList.add('hidden');
    }
  }

  showSuccessMessage(message) {
    this.showToast(message, 'success');
  }

  showErrorMessage(message) {
    this.showToast(message, 'error');
  }

  showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 glass-card z-50 max-w-sm transform transition-all duration-300 translate-x-full`;
    
    const bgColor = type === 'success' ? 'bg-green-500' : 
                   type === 'error' ? 'bg-red-500' : 'bg-blue-500';
    
    toast.innerHTML = `
      <div class="flex items-center gap-3">
        <div class="w-2 h-2 rounded-full ${bgColor}"></div>
        <p class="text-primary">${message}</p>
        <button class="ml-auto text-muted hover:text-primary" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;
    
    document.body.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
      toast.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      toast.classList.add('translate-x-full');
      setTimeout(() => toast.remove(), 300);
    }, 5000);
  }

  updateConnectionState(state) {
    this.modernUI.connectionState = state;
    
    // Update UI elements based on connection state
    const statusElements = document.querySelectorAll('[data-connection-status]');
    statusElements.forEach(el => {
      el.textContent = state.charAt(0).toUpperCase() + state.slice(1);
      el.className = `connection-status connection-status-${state}`;
    });
  }

  loadUserPreferences() {
    try {
      const saved = localStorage.getItem('vdo-ninja-modern-preferences');
      return saved ? JSON.parse(saved) : {};
    } catch (error) {
      console.warn('Failed to load user preferences:', error);
      return {};
    }
  }

  saveUserPreferences() {
    try {
      localStorage.setItem('vdo-ninja-modern-preferences', 
        JSON.stringify(this.modernUI.userPreferences));
    } catch (error) {
      console.warn('Failed to save user preferences:', error);
    }
  }

  handleGlobalClick(event) {
    const target = event.target;
    
    // Handle modern UI button clicks
    if (target.matches('[data-action]')) {
      event.preventDefault();
      const action = target.dataset.action;
      this.handleAction(action, target);
    }
  }

  handleFormSubmit(event) {
    const form = event.target;
    
    // Handle modern UI form submissions
    if (form.matches('[data-modern-form]')) {
      event.preventDefault();
      const formType = form.dataset.modernForm;
      this.handleFormSubmission(formType, form);
    }
  }

  handleAction(action, element) {
    console.log('Handling action:', action);
    
    switch (action) {
      case 'create-room':
        this.handleCreateRoom(element);
        break;
      case 'join-room':
        this.handleJoinRoom(element);
        break;
      case 'hangup':
        this.modernHangup();
        break;
      case 'toggle-audio':
        this.handleToggleAudio();
        break;
      case 'toggle-video':
        this.handleToggleVideo();
        break;
      default:
        console.warn('Unknown action:', action);
    }
  }

  handleCreateRoom(element) {
    const form = element.closest('form') || document.querySelector('[data-modern-form="create-room"]');
    if (form) {
      this.handleFormSubmission('create-room', form);
    }
  }

  handleJoinRoom(element) {
    const form = element.closest('form') || document.querySelector('[data-modern-form="join-room"]');
    if (form) {
      this.handleFormSubmission('join-room', form);
    }
  }

  handleFormSubmission(formType, form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    console.log('Form submission - Type:', formType);
    console.log('Form submission - Data:', data);

    switch (formType) {
      case 'create-room':
        this.modernCreateRoom(data);
        break;
      case 'join-room':
        this.modernJoinRoom(data.roomId || data.roomName, data);
        break;
      case 'join-director':
        this.modernJoinDirector(data.roomName, data);
        break;
      default:
        console.warn('Unknown form type:', formType);
    }
  }

  handleToggleAudio() {
    // Integrate with original VDO.Ninja audio toggle functionality
    console.log('Toggling audio...');
    // This would call the original audio toggle function
  }

  handleToggleVideo() {
    // Integrate with original VDO.Ninja video toggle functionality
    console.log('Toggling video...');
    // This would call the original video toggle function
  }

  handleResize() {
    // Handle responsive layout adjustments
    console.log('Handling resize...');
  }

  // Utility function to copy room links
  copyToClipboard(text) {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(text).then(() => {
        this.showSuccessMessage('Link copied to clipboard!');
      }).catch(() => {
        this.fallbackCopyToClipboard(text);
      });
    } else {
      this.fallbackCopyToClipboard(text);
    }
  }

  fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand('copy');
      this.showSuccessMessage('Link copied to clipboard!');
    } catch (err) {
      this.showErrorMessage('Failed to copy link');
    }
    document.body.removeChild(textArea);
  }

  // Director-specific functions
  modernDirectorControl(action, targetId, settings = {}) {
    console.log('Modern UI: Director control action:', action, 'target:', targetId, 'settings:', settings);

    try {
      // This would integrate with original VDO.Ninja director functions
      switch (action) {
        case 'mute-guest':
          this.muteGuest(targetId);
          break;
        case 'unmute-guest':
          this.unmuteGuest(targetId);
          break;
        case 'remove-guest':
          this.removeGuest(targetId);
          break;
        case 'change-scene':
          this.changeScene(settings.sceneId);
          break;
        case 'start-recording':
          this.startRecording(settings);
          break;
        case 'stop-recording':
          this.stopRecording();
          break;
        default:
          console.warn('Unknown director action:', action);
      }
    } catch (error) {
      console.error('Error in director control:', error);
      this.showErrorMessage('Director action failed: ' + error.message);
    }
  }

  muteGuest(guestId) {
    console.log('Muting guest:', guestId);
    this.showSuccessMessage(`Guest ${guestId} muted`);
  }

  unmuteGuest(guestId) {
    console.log('Unmuting guest:', guestId);
    this.showSuccessMessage(`Guest ${guestId} unmuted`);
  }

  removeGuest(guestId) {
    console.log('Removing guest:', guestId);
    this.showSuccessMessage(`Guest ${guestId} removed`);
  }

  changeScene(sceneId) {
    console.log('Changing to scene:', sceneId);
    this.showSuccessMessage(`Switched to scene ${sceneId}`);
  }

  startRecording(settings) {
    console.log('Starting recording with settings:', settings);
    this.showSuccessMessage('Recording started');
  }

  stopRecording() {
    console.log('Stopping recording');
    this.showSuccessMessage('Recording stopped');
  }

  // URL building and navigation functions (VDO.Ninja's approach)
  buildDirectorRoomUrl(settings) {
    const baseUrl = window.location.origin + window.location.pathname.replace(/app-[^/]*\.html$/, 'index.html');
    const params = new URLSearchParams();

    console.log('Building director URL - Base URL:', baseUrl);
    console.log('Building director URL - Settings:', settings);

    // Add director parameter
    params.set('director', settings.roomName);

    // Add password if provided
    if (settings.roomPassword) {
      params.set('password', settings.roomPassword);
    }

    // Add video quality settings
    if (settings.videoQuality && settings.videoQuality !== 'auto') {
      params.set('quality', settings.videoQuality);
    }

    // Add audio settings
    if (settings.audioOnly) {
      params.set('novideo', '1');
    }

    // Add codec settings
    if (settings.videoCodec && settings.videoCodec !== 'auto') {
      params.set('codec', settings.videoCodec);
    }

    // Add other VDO.Ninja specific parameters
    if (settings.recording) {
      params.set('record', '1');
    }

    if (settings.p2pMode === false) {
      params.set('relay', '1');
    }

    const finalUrl = `${baseUrl}?${params.toString()}`;
    console.log('Final director URL:', finalUrl);
    return finalUrl;
  }

  buildJoinRoomUrl(roomId, settings) {
    const baseUrl = window.location.origin + window.location.pathname.replace(/app-[^/]*\.html$/, 'index.html');
    const params = new URLSearchParams();

    console.log('Building join URL - Base URL:', baseUrl);
    console.log('Building join URL - Room ID:', roomId);
    console.log('Building join URL - Settings:', settings);

    // Add room parameter
    params.set('room', roomId);

    // Add password if provided
    if (settings.roomPassword) {
      params.set('password', settings.roomPassword);
    }

    // Add display name if provided
    if (settings.displayName) {
      params.set('label', settings.displayName);
    }

    // Add audio/video settings
    if (settings.audioOnly) {
      params.set('novideo', '1');
    }

    if (settings.startMuted) {
      params.set('muted', '1');
    }

    // Add quality settings
    if (settings.videoQuality && settings.videoQuality !== 'auto') {
      params.set('quality', settings.videoQuality);
    }

    const finalUrl = `${baseUrl}?${params.toString()}`;
    console.log('Final join URL:', finalUrl);
    return finalUrl;
  }

  navigateToRoom(url) {
    console.log('Navigating to room:', url);

    // Always redirect to the classic VDO.Ninja interface
    // The modern interface is just a beautiful front-end for form collection
    console.log('Redirecting to classic VDO.Ninja interface:', url);
    window.location.href = url;
  }

  sanitizeRoomName(roomName) {
    if (!roomName || typeof roomName !== 'string') {
      return '';
    }

    // Basic sanitization similar to VDO.Ninja's approach
    return roomName.trim()
      .replace(/[^a-zA-Z0-9\-_]/g, '')
      .substring(0, 30);
  }

  generateRandomString(length = 10) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}

// Initialize the bridge when script loads
window.vdoNinjaBridge = new VDONinjaBridge();
