<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Create Room - VDO.Ninja</title>
    
    <!-- Meta Tags -->
    <meta name="description" content="Create a new VDO.Ninja room for live streaming and video communication">
    <meta name="theme-color" content="#0a0e1a">
    
    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="./media/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./media/favicon-16x16.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="./app-styles.css">
    
    <!-- Import ALL original VDO.Ninja scripts for functionality preservation -->
    <script src="./thirdparty/adapter.js"></script>
    <script src="./main.js"></script>
    <script src="./webrtc.js"></script>
</head>
<body>
    <!-- Hidden original functionality container -->
    <div id="original-functionality" style="display: none;">
        <!-- Original VDO.Ninja elements will be preserved here -->
    </div>

    <!-- Modern UI Container -->
    <div id="modern-app" class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-md">
                    <a href="./app-landing.html" class="btn btn-secondary btn-sm">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                        </svg>
                        Back
                    </a>
                    <h1 class="text-xl font-semibold">Create Room</h1>
                </div>
                <div class="status-indicator">
                    <div class="status-dot" data-connection-status></div>
                    <span data-connection-status>Disconnected</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <div class="max-w-4xl mx-auto w-full">
                <!-- Room Creation Form -->
                <form id="create-room-form" data-modern-form="create-room" class="space-y-lg">
                    <!-- Basic Settings -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">Room Settings</h2>
                            <p class="card-description">Configure your room name and basic settings</p>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-lg">
                            <div class="form-group">
                                <label for="roomName" class="form-label">Room Name</label>
                                <input 
                                    type="text" 
                                    id="roomName" 
                                    name="roomName" 
                                    class="form-input" 
                                    placeholder="Enter a unique room name"
                                    required
                                >
                                <p class="text-xs text-muted mt-xs">Leave empty to generate a random room name</p>
                            </div>
                            
                            <div class="form-group">
                                <label for="roomPassword" class="form-label">Room Password (Optional)</label>
                                <input 
                                    type="password" 
                                    id="roomPassword" 
                                    name="roomPassword" 
                                    class="form-input" 
                                    placeholder="Optional password protection"
                                >
                            </div>
                        </div>
                        
                        <!-- Quick Setup Toggles -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-lg mt-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="form-label mb-0">Audio Only</label>
                                    <p class="text-xs text-muted">Disable video streaming</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="audioOnly">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="form-label mb-0">HD Video</label>
                                    <p class="text-xs text-muted">Enable high definition</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="hdVideo" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="form-label mb-0">Director Mode</label>
                                    <p class="text-xs text-muted">Advanced controls</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="directorMode">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Settings (Collapsible) -->
                    <div class="settings-section">
                        <div class="settings-header" onclick="toggleAdvancedSettings()">
                            <h3>Advanced Settings</h3>
                            <svg id="advanced-arrow" width="20" height="20" fill="currentColor" viewBox="0 0 20 20" class="transition-transform">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        
                        <div id="advanced-content" class="settings-content">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-lg">
                                <!-- Video Quality -->
                                <div class="form-group">
                                    <label for="videoQuality" class="form-label">Video Quality</label>
                                    <select id="videoQuality" name="videoQuality" class="form-input">
                                        <option value="auto">Auto</option>
                                        <option value="4k">4K (2160p)</option>
                                        <option value="1080p" selected>Full HD (1080p)</option>
                                        <option value="720p">HD (720p)</option>
                                        <option value="480p">SD (480p)</option>
                                    </select>
                                </div>
                                
                                <!-- Audio Quality -->
                                <div class="form-group">
                                    <label for="audioQuality" class="form-label">Audio Quality</label>
                                    <select id="audioQuality" name="audioQuality" class="form-input">
                                        <option value="auto" selected>Auto</option>
                                        <option value="high">High (128 kbps)</option>
                                        <option value="medium">Medium (64 kbps)</option>
                                        <option value="low">Low (32 kbps)</option>
                                    </select>
                                </div>
                                
                                <!-- Codec Selection -->
                                <div class="form-group">
                                    <label for="videoCodec" class="form-label">Video Codec</label>
                                    <select id="videoCodec" name="videoCodec" class="form-input">
                                        <option value="auto" selected>Auto</option>
                                        <option value="vp9">VP9</option>
                                        <option value="vp8">VP8</option>
                                        <option value="h264">H.264</option>
                                    </select>
                                </div>
                                
                                <!-- Max Participants -->
                                <div class="form-group">
                                    <label for="maxParticipants" class="form-label">Max Participants</label>
                                    <select id="maxParticipants" name="maxParticipants" class="form-input">
                                        <option value="unlimited" selected>Unlimited</option>
                                        <option value="30">30</option>
                                        <option value="20">20</option>
                                        <option value="10">10</option>
                                        <option value="5">5</option>
                                        <option value="2">2</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Additional Options -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-lg mt-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <label class="form-label mb-0">Recording</label>
                                        <p class="text-xs text-muted">Enable room recording</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="recording">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <div>
                                        <label class="form-label mb-0">Virtual Backgrounds</label>
                                        <p class="text-xs text-muted">Allow background effects</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="virtualBackgrounds" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <div>
                                        <label class="form-label mb-0">P2P Mode</label>
                                        <p class="text-xs text-muted">Direct peer connections</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="p2pMode" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <div>
                                        <label class="form-label mb-0">Auto-mix Scenes</label>
                                        <p class="text-xs text-muted">Automatic scene switching</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="autoMix">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col md:flex-row gap-md justify-center">
                        <button type="submit" class="btn btn-primary btn-lg" data-action="create-room">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                            </svg>
                            Create & Share Room
                        </button>
                        <button type="button" class="btn btn-secondary btn-lg" onclick="previewSettings()">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                            </svg>
                            Preview Settings
                        </button>
                    </div>
                </form>

                <!-- Room Links Display (Hidden initially) -->
                <div id="room-links" class="card hidden mt-lg">
                    <div class="card-header">
                        <h3 class="card-title">Room Created Successfully!</h3>
                        <p class="card-description">Share these links with your participants</p>
                    </div>
                    
                    <div class="space-y-md">
                        <div>
                            <label class="form-label">Guest Link</label>
                            <div class="room-link-display">
                                <span id="guest-link"></span>
                                <button class="copy-button" onclick="copyLink('guest-link')">Copy</button>
                            </div>
                        </div>
                        
                        <div>
                            <label class="form-label">OBS Browser Source</label>
                            <div class="room-link-display">
                                <span id="obs-link"></span>
                                <button class="copy-button" onclick="copyLink('obs-link')">Copy</button>
                            </div>
                        </div>
                        
                        <div class="flex gap-md">
                            <button class="btn btn-primary" onclick="enterRoom()">
                                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-2 0V5H5v10h10v-1a1 1 0 112 0v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm13.293 4.293a1 1 0 011.414 1.414L15.414 12l2.293 2.293a1 1 0 01-1.414 1.414L14 13.414l-2.293 2.293a1 1 0 01-1.414-1.414L12.586 12l-2.293-2.293a1 1 0 011.414-1.414L14 10.586l2.293-2.293z" clip-rule="evenodd"/>
                                </svg>
                                Enter Room
                            </button>
                            <button class="btn btn-secondary" onclick="createAnother()">Create Another</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bridge Script -->
    <script src="./app-bridge.js?v=6"></script>
    
    <script>
        // Room creation specific JavaScript
        let currentRoomData = null;
        
        function toggleAdvancedSettings() {
            const content = document.getElementById('advanced-content');
            const arrow = document.getElementById('advanced-arrow');
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                arrow.style.transform = 'rotate(0deg)';
            } else {
                content.classList.add('expanded');
                arrow.style.transform = 'rotate(180deg)';
            }
        }
        
        function previewSettings() {
            const form = document.getElementById('create-room-form');
            const formData = new FormData(form);
            const settings = Object.fromEntries(formData.entries());
            
            // Show preview modal or update UI with current settings
            console.log('Preview settings:', settings);
            window.vdoNinjaBridge.showSuccessMessage('Settings preview: ' + JSON.stringify(settings, null, 2));
        }
        
        function copyLink(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            window.vdoNinjaBridge.copyToClipboard(text);
        }
        
        function enterRoom() {
            if (currentRoomData && currentRoomData.roomUrl) {
                // Navigate to the original VDO.Ninja director interface
                window.location.href = currentRoomData.roomUrl;
            }
        }
        
        function createAnother() {
            // Reset form and hide room links
            document.getElementById('create-room-form').reset();
            document.getElementById('room-links').classList.add('hidden');
            currentRoomData = null;
            
            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
        
        // Override the bridge's room creation to handle UI updates
        const originalCreateRoom = window.vdoNinjaBridge.modernCreateRoom;
        window.vdoNinjaBridge.modernCreateRoom = function(settings) {
            try {
                // Call original function
                const result = originalCreateRoom.call(this, settings);

                // Update UI with room links
                currentRoomData = result;
                displayRoomLinks(result);

                return result;
            } catch (error) {
                console.error('Error in room creation:', error);
                throw error;
            }
        };
        
        function displayRoomLinks(result) {
            const roomName = result.roomName;
            const baseUrl = window.location.origin + window.location.pathname.replace('app-create-room.html', '');

            // Generate guest join link (modern UI)
            const guestLink = `${baseUrl}app-join-room.html?room=${encodeURIComponent(roomName)}`;

            // Generate OBS browser source link (original VDO.Ninja)
            const obsLink = `${baseUrl}index.html?view=${encodeURIComponent(roomName)}&scene`;

            // Update UI
            document.getElementById('guest-link').textContent = guestLink;
            document.getElementById('obs-link').textContent = obsLink;
            document.getElementById('room-links').classList.remove('hidden');

            // Scroll to room links
            document.getElementById('room-links').scrollIntoView({ behavior: 'smooth' });
        }
        
        // Auto-generate room name if empty
        document.getElementById('roomName').addEventListener('blur', function() {
            if (!this.value.trim()) {
                this.value = 'room-' + Math.random().toString(36).substring(2, 8);
            }
        });
        
        // Load saved preferences
        document.addEventListener('DOMContentLoaded', function() {
            const savedPrefs = window.vdoNinjaBridge.modernUI.userPreferences;
            if (savedPrefs.defaultVideoQuality) {
                document.getElementById('videoQuality').value = savedPrefs.defaultVideoQuality;
            }
            if (savedPrefs.defaultAudioQuality) {
                document.getElementById('audioQuality').value = savedPrefs.defaultAudioQuality;
            }
        });
    </script>
</body>
</html>
