<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug VDO.Ninja Bridge</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1f2e;
            color: white;
        }
        .debug-section {
            background: #2a2f3e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #4a9eff;
        }
        button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3b82f6;
        }
        .result {
            background: #0a0e1a;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            border: 1px solid #333;
        }
        .success {
            border-color: #00d4aa;
            background: #0a1a15;
        }
        .error {
            border-color: #ff4757;
            background: #1a0a0a;
        }
    </style>
</head>
<body>
    <h1>🔧 VDO.Ninja Bridge Debug Console</h1>
    
    <div class="debug-section">
        <h2>Bridge Status</h2>
        <button onclick="checkBridgeStatus()">Check Bridge Status</button>
        <div id="status-result" class="result">Click button to check status...</div>
    </div>
    
    <div class="debug-section">
        <h2>Direct Function Tests</h2>
        <button onclick="testCreateRoomDirect()">Test Create Room (Direct)</button>
        <button onclick="testJoinRoomDirect()">Test Join Room (Direct)</button>
        <div id="direct-result" class="result">Click buttons to test functions directly...</div>
    </div>
    
    <div class="debug-section">
        <h2>URL Building Tests</h2>
        <button onclick="testUrlBuilding()">Test URL Building</button>
        <div id="url-result" class="result">Click button to test URL building...</div>
    </div>
    
    <div class="debug-section">
        <h2>Form Simulation</h2>
        <button onclick="simulateCreateForm()">Simulate Create Room Form</button>
        <button onclick="simulateJoinForm()">Simulate Join Room Form</button>
        <div id="form-result" class="result">Click buttons to simulate form submissions...</div>
    </div>

    <!-- Import VDO.Ninja scripts -->
    <script src="./thirdparty/adapter.js"></script>
    <script src="./main.js"></script>
    <script src="./webrtc.js"></script>
    
    <!-- Bridge Script with cache busting -->
    <script src="./app-bridge.js?v=3"></script>
    
    <script>
        function checkBridgeStatus() {
            const result = document.getElementById('status-result');
            
            try {
                if (typeof window.vdoNinjaBridge === 'undefined') {
                    result.textContent = 'ERROR: Bridge not loaded!';
                    result.className = 'result error';
                    return;
                }
                
                const bridge = window.vdoNinjaBridge;
                
                result.textContent = `✅ Bridge Status:
Version: ${bridge.version || 'Unknown'}
Initialized: ${bridge.isInitialized}
Available Functions: ${Object.keys(bridge.originalFunctions || {}).join(', ')}
Modern UI State: ${JSON.stringify(bridge.modernUI || {}, null, 2)}

✅ Function Availability:
modernCreateRoom: ${typeof bridge.modernCreateRoom === 'function'}
modernJoinRoom: ${typeof bridge.modernJoinRoom === 'function'}
buildDirectorRoomUrl: ${typeof bridge.buildDirectorRoomUrl === 'function'}
buildJoinRoomUrl: ${typeof bridge.buildJoinRoomUrl === 'function'}
sanitizeRoomName: ${typeof bridge.sanitizeRoomName === 'function'}
generateRandomString: ${typeof bridge.generateRandomString === 'function'}`;
                
                result.className = 'result success';
                
            } catch (error) {
                result.textContent = `ERROR: ${error.message}\n\nStack: ${error.stack}`;
                result.className = 'result error';
            }
        }
        
        function testCreateRoomDirect() {
            const result = document.getElementById('direct-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                if (!bridge) {
                    throw new Error('Bridge not available');
                }
                
                const settings = {
                    roomName: 'DebugRoom123',
                    roomPassword: 'secret',
                    directorMode: true,
                    hdVideo: true
                };
                
                result.textContent = `🧪 Testing modernCreateRoom directly...
Settings: ${JSON.stringify(settings, null, 2)}

Calling bridge.modernCreateRoom(settings)...
`;
                
                const createResult = bridge.modernCreateRoom(settings);
                
                result.textContent += `
✅ SUCCESS!
Result: ${JSON.stringify(createResult, null, 2)}`;
                
                result.className = 'result success';
                
            } catch (error) {
                result.textContent += `
❌ ERROR: ${error.message}
Stack: ${error.stack}`;
                result.className = 'result error';
            }
        }
        
        function testJoinRoomDirect() {
            const result = document.getElementById('direct-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                if (!bridge) {
                    throw new Error('Bridge not available');
                }
                
                const roomId = 'DebugRoom123';
                const settings = {
                    displayName: 'DebugUser',
                    roomPassword: '',
                    audioOnly: false
                };
                
                result.textContent = `🧪 Testing modernJoinRoom directly...
Room ID: ${roomId}
Settings: ${JSON.stringify(settings, null, 2)}

Calling bridge.modernJoinRoom(roomId, settings)...
`;
                
                // Override navigateToRoom to prevent actual navigation during test
                const originalNavigate = bridge.navigateToRoom;
                bridge.navigateToRoom = function(url) {
                    console.log('Navigation intercepted:', url);
                    return { navigated: true, url: url };
                };
                
                const joinResult = bridge.modernJoinRoom(roomId, settings);
                
                // Restore original function
                bridge.navigateToRoom = originalNavigate;
                
                result.textContent += `
✅ SUCCESS!
Result: ${JSON.stringify(joinResult, null, 2)}`;
                
                result.className = 'result success';
                
            } catch (error) {
                result.textContent += `
❌ ERROR: ${error.message}
Stack: ${error.stack}`;
                result.className = 'result error';
            }
        }
        
        function testUrlBuilding() {
            const result = document.getElementById('url-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                if (!bridge) {
                    throw new Error('Bridge not available');
                }
                
                // Test director URL building
                const directorSettings = {
                    roomName: 'TestRoom',
                    roomPassword: 'secret',
                    videoQuality: '1080p',
                    audioOnly: false
                };
                
                const directorUrl = bridge.buildDirectorRoomUrl(directorSettings);
                
                // Test join URL building
                const joinSettings = {
                    displayName: 'TestUser',
                    roomPassword: 'secret',
                    audioOnly: false,
                    startMuted: true
                };
                
                const joinUrl = bridge.buildJoinRoomUrl('TestRoom', joinSettings);
                
                result.textContent = `🔗 URL Building Tests:

Director URL:
Settings: ${JSON.stringify(directorSettings, null, 2)}
Generated URL: ${directorUrl}

Join URL:
Room ID: TestRoom
Settings: ${JSON.stringify(joinSettings, null, 2)}
Generated URL: ${joinUrl}

✅ URL building successful!`;
                
                result.className = 'result success';
                
            } catch (error) {
                result.textContent = `❌ URL Building Error: ${error.message}
Stack: ${error.stack}`;
                result.className = 'result error';
            }
        }
        
        function simulateCreateForm() {
            const result = document.getElementById('form-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                if (!bridge) {
                    throw new Error('Bridge not available');
                }
                
                // Create a mock form data object
                const formData = {
                    roomName: 'FormTestRoom',
                    roomPassword: 'formSecret',
                    directorMode: 'on',
                    hdVideo: 'on',
                    videoQuality: '1080p'
                };
                
                result.textContent = `📝 Simulating Create Room Form Submission...
Form Data: ${JSON.stringify(formData, null, 2)}

Calling handleFormSubmission('create-room', mockForm)...
`;
                
                // Call the form submission handler directly
                const createResult = bridge.modernCreateRoom(formData);
                
                result.textContent += `
✅ Form Simulation SUCCESS!
Result: ${JSON.stringify(createResult, null, 2)}`;
                
                result.className = 'result success';
                
            } catch (error) {
                result.textContent += `
❌ Form Simulation ERROR: ${error.message}
Stack: ${error.stack}`;
                result.className = 'result error';
            }
        }
        
        function simulateJoinForm() {
            const result = document.getElementById('form-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                if (!bridge) {
                    throw new Error('Bridge not available');
                }
                
                // Create a mock form data object
                const formData = {
                    roomId: 'FormTestRoom',
                    displayName: 'FormTestUser',
                    roomPassword: 'formSecret',
                    audioOnly: false,
                    startMuted: true
                };
                
                result.textContent = `📝 Simulating Join Room Form Submission...
Form Data: ${JSON.stringify(formData, null, 2)}

Calling modernJoinRoom(roomId, settings)...
`;
                
                // Override navigateToRoom to prevent actual navigation during test
                const originalNavigate = bridge.navigateToRoom;
                bridge.navigateToRoom = function(url) {
                    console.log('Navigation intercepted:', url);
                    return { navigated: true, url: url };
                };
                
                // Call the join room function directly
                const joinResult = bridge.modernJoinRoom(formData.roomId, formData);
                
                // Restore original function
                bridge.navigateToRoom = originalNavigate;
                
                result.textContent += `
✅ Form Simulation SUCCESS!
Result: ${JSON.stringify(joinResult, null, 2)}`;
                
                result.className = 'result success';
                
            } catch (error) {
                result.textContent += `
❌ Form Simulation ERROR: ${error.message}
Stack: ${error.stack}`;
                result.className = 'result error';
            }
        }
        
        // Auto-run status check when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkBridgeStatus, 1000);
        });
    </script>
</body>
</html>
