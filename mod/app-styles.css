/* VDO.Ninja Modern Interface Styles */
/* Mobile-first responsive design with dark theme and glassmorphism effects */

:root {
  /* Modern Color Palette */
  --primary-bg: #0a0e1a;
  --secondary-bg: #1a1f2e;
  --tertiary-bg: #2a2f3e;
  --glass-bg: rgba(26, 31, 46, 0.8);
  --glass-border: rgba(255, 255, 255, 0.1);
  
  /* Accent Colors */
  --accent-blue: #4a9eff;
  --accent-green: #00d4aa;
  --accent-orange: #ff8c42;
  --accent-red: #ff4757;
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b8c5d6;
  --text-muted: #7a8394;
  
  /* Interactive States */
  --hover-overlay: rgba(74, 158, 255, 0.1);
  --active-overlay: rgba(74, 158, 255, 0.2);
  --focus-ring: 0 0 0 3px rgba(74, 158, 255, 0.3);
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Touch Targets */
  --touch-target: 44px;
  --touch-target-lg: 48px;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--space-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--space-md);
  color: var(--text-secondary);
}

/* Glass Morphism Components */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-glass);
}

.glass-card {
  @extend .glass;
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.glass-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Layout Components */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  padding: var(--space-md) var(--space-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  position: sticky;
  top: 0;
  z-index: 100;
}

.app-main {
  flex: 1;
  padding: var(--space-lg);
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.app-footer {
  padding: var(--space-lg);
  text-align: center;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

/* Grid System */
.grid {
  display: grid;
  gap: var(--space-lg);
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.flex-1 { flex: 1; }
.flex-none { flex: none; }

/* Spacing Utilities */
.gap-xs { gap: var(--space-xs); }
.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.gap-xl { gap: var(--space-xl); }

.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }

.m-xs { margin: var(--space-xs); }
.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }
.m-xl { margin: var(--space-xl); }

/* Button Components */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-lg);
  min-height: var(--touch-target);
  border: none;
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, var(--accent-blue) 0%, #3b82f6 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #3b82f6 0%, var(--accent-blue) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--glass-bg);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(20px);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--hover-overlay);
  border-color: var(--accent-blue);
}

.btn-success {
  background: linear-gradient(135deg, var(--accent-green) 0%, #10b981 100%);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, var(--accent-red) 0%, #ef4444 100%);
  color: white;
}

.btn-lg {
  padding: var(--space-md) var(--space-xl);
  min-height: var(--touch-target-lg);
  font-size: var(--font-size-lg);
}

.btn-sm {
  padding: var(--space-xs) var(--space-md);
  min-height: 36px;
  font-size: var(--font-size-sm);
}

/* Form Components */
.form-group {
  margin-bottom: var(--space-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--space-sm);
  font-weight: 500;
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: var(--space-md);
  min-height: var(--touch-target);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-family: inherit;
  font-size: var(--font-size-base);
  transition: all var(--transition-fast);
  backdrop-filter: blur(20px);
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: var(--focus-ring);
}

.form-input::placeholder {
  color: var(--text-muted);
}

/* Card Components */
.card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid var(--glass-border);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--space-sm);
}

.card-description {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* Utility Classes */
.hidden { display: none !important; }
.visible { display: block !important; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

/* Animation Classes */
.fade-in {
  animation: fadeIn var(--transition-normal) ease-in-out;
}

.slide-up {
  animation: slideUp var(--transition-normal) ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (min-width: 768px) {
  .app-main {
    padding: var(--space-xl);
  }
  
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .md\:flex-row { flex-direction: row; }
  .md\:text-left { text-align: left; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

/* Hero Actions Layout */
.hero-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-md);
  max-width: 500px;
  margin: 0 auto;
}

@media (max-width: 640px) {
  .hero-actions {
    grid-template-columns: 1fr;
    max-width: 300px;
  }
}

/* Additional Components for Room Creation/Joining */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--glass-border);
  transition: var(--transition-fast);
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: var(--transition-fast);
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--accent-blue);
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

.settings-section {
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-md);
  cursor: pointer;
}

.settings-content {
  display: none;
}

.settings-content.expanded {
  display: block;
  animation: slideDown var(--transition-normal) ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
  }
}

.room-link-display {
  background: var(--tertiary-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  font-family: monospace;
  font-size: var(--font-size-sm);
  word-break: break-all;
  position: relative;
}

.copy-button {
  position: absolute;
  top: var(--space-sm);
  right: var(--space-sm);
  padding: var(--space-xs) var(--space-sm);
  background: var(--accent-blue);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: var(--transition-fast);
}

.copy-button:hover {
  background: #3b82f6;
}

.device-preview {
  background: var(--tertiary-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.device-preview video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-md);
}

.device-preview.no-video {
  background: linear-gradient(135deg, var(--tertiary-bg) 0%, var(--secondary-bg) 100%);
}

.device-preview.no-video::before {
  content: "📹";
  font-size: 3rem;
  opacity: 0.3;
}

.audio-meter {
  width: 100%;
  height: 8px;
  background: var(--tertiary-bg);
  border-radius: 4px;
  overflow: hidden;
  margin-top: var(--space-sm);
}

.audio-meter-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-green) 0%, var(--accent-orange) 70%, var(--accent-red) 100%);
  width: 0%;
  transition: width 0.1s ease-out;
}

.device-controls {
  display: flex;
  gap: var(--space-sm);
  justify-content: center;
  margin-top: var(--space-md);
}

.device-select {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-md);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  min-width: 150px;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--accent-red);
  animation: pulse 2s infinite;
}

.status-dot.connected {
  background: var(--accent-green);
}

.status-dot.connecting {
  background: var(--accent-orange);
}

/* Video Grid for Communications Room */
.video-grid {
  display: grid;
  gap: var(--space-md);
  width: 100%;
  height: 100%;
  grid-auto-rows: 1fr;
}

.video-grid.grid-1x1 {
  grid-template-columns: 1fr;
}

.video-grid.grid-2x2 {
  grid-template-columns: repeat(2, 1fr);
}

.video-grid.grid-3x3 {
  grid-template-columns: repeat(3, 1fr);
}

.video-tile {
  position: relative;
  background: var(--tertiary-bg);
  border-radius: var(--radius-lg);
  overflow: hidden;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-tile video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-tile-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: var(--space-md);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.participant-name {
  font-weight: 500;
  font-size: var(--font-size-sm);
}

.participant-controls {
  display: flex;
  gap: var(--space-xs);
}

.control-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.control-btn.muted {
  background: var(--accent-red);
}

.floating-controls {
  position: fixed;
  bottom: var(--space-lg);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--space-md);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-md);
  z-index: 50;
}

.floating-controls .btn {
  min-width: auto;
  width: var(--touch-target);
  height: var(--touch-target);
  border-radius: 50%;
  padding: 0;
}

@media (max-width: 767px) {
  .video-grid.grid-2x2,
  .video-grid.grid-3x3 {
    grid-template-columns: 1fr;
  }

  .floating-controls {
    bottom: var(--space-md);
    left: var(--space-md);
    right: var(--space-md);
    transform: none;
    justify-content: space-around;
  }
}
