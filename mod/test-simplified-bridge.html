<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simplified Bridge - VDO.Ninja Modern</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1f2e;
            color: white;
        }
        .test-section {
            background: #2a2f3e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #4a9eff;
        }
        button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3b82f6;
        }
        .result {
            background: #0a0e1a;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            border: 1px solid #333;
        }
        .success {
            border-color: #00d4aa;
            background: #0a1a15;
        }
        .info {
            background: #2a1f0a;
            border-color: #ff9500;
            color: #ffb84d;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 Simplified Bridge Test - VDO.Ninja Modern Interface</h1>
    
    <div class="info">
        <strong>📋 New Approach:</strong> The modern interface is now a beautiful front-end that always redirects to the classic VDO.Ninja interface. This ensures 100% compatibility and consistency with all VDO.Ninja features.
    </div>
    
    <div class="test-section">
        <h2>Test Create Room with Director Mode</h2>
        <button onclick="testCreateWithDirector()">Test Create Room + Director (Will Redirect)</button>
        <div id="create-result" class="result">Click button to test...</div>
    </div>
    
    <div class="test-section">
        <h2>Test Join as Director</h2>
        <button onclick="testJoinAsDirector()">Test Join as Director (Will Redirect)</button>
        <div id="director-result" class="result">Click button to test...</div>
    </div>
    
    <div class="test-section">
        <h2>Test Join Room</h2>
        <button onclick="testJoinRoom()">Test Join Room (Will Redirect)</button>
        <div id="join-result" class="result">Click button to test...</div>
    </div>
    
    <div class="test-section">
        <h2>URL Generation Test</h2>
        <button onclick="testUrlGeneration()">Test URL Generation (No Redirect)</button>
        <div id="url-result" class="result">Click button to test...</div>
    </div>

    <!-- Import VDO.Ninja scripts -->
    <script src="./thirdparty/adapter.js"></script>
    <script src="./main.js"></script>
    <script src="./webrtc.js"></script>
    
    <!-- Bridge Script -->
    <script src="./app-bridge.js?v=6"></script>
    
    <script>
        function testCreateWithDirector() {
            const result = document.getElementById('create-result');
            
            result.textContent = `🧪 Testing Create Room with Director Mode...

This will redirect to the classic VDO.Ninja director interface in 3 seconds...

Settings:
- Room Name: TestDirectorRoom
- Director Mode: Enabled
- Password: secret
- HD Video: Enabled

Redirecting to classic VDO.Ninja interface...`;
            
            setTimeout(() => {
                const settings = {
                    roomName: 'TestDirectorRoom',
                    roomPassword: 'secret',
                    directorMode: true,
                    hdVideo: true
                };
                
                window.vdoNinjaBridge.modernCreateRoom(settings);
            }, 3000);
        }
        
        function testJoinAsDirector() {
            const result = document.getElementById('director-result');
            
            result.textContent = `🧪 Testing Join as Director...

This will redirect to the classic VDO.Ninja director interface in 3 seconds...

Settings:
- Room Name: ExistingRoom
- Display Name: Director
- Password: secret
- Auto-add Guests: Enabled

Redirecting to classic VDO.Ninja interface...`;
            
            setTimeout(() => {
                const roomName = 'ExistingRoom';
                const settings = {
                    roomPassword: 'secret',
                    displayName: 'Director',
                    autoAddGuests: true,
                    startWithAudio: true,
                    startWithVideo: false
                };
                
                window.vdoNinjaBridge.modernJoinDirector(roomName, settings);
            }, 3000);
        }
        
        function testJoinRoom() {
            const result = document.getElementById('join-result');
            
            result.textContent = `🧪 Testing Join Room...

This will redirect to the classic VDO.Ninja interface in 3 seconds...

Settings:
- Room ID: TestRoom
- Display Name: TestUser
- Audio Only: No
- Start Muted: No

Redirecting to classic VDO.Ninja interface...`;
            
            setTimeout(() => {
                const roomId = 'TestRoom';
                const settings = {
                    displayName: 'TestUser',
                    roomPassword: '',
                    audioOnly: false,
                    startMuted: false
                };
                
                window.vdoNinjaBridge.modernJoinRoom(roomId, settings);
            }, 3000);
        }
        
        function testUrlGeneration() {
            const result = document.getElementById('url-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                // Test director URL generation
                const directorSettings = {
                    roomName: 'TestRoom',
                    roomPassword: 'secret',
                    directorMode: true,
                    hdVideo: true
                };
                
                const directorUrl = bridge.buildDirectorRoomUrl(directorSettings);
                
                // Test participant URL generation
                const participantSettings = {
                    displayName: 'TestUser',
                    roomPassword: 'secret',
                    audioOnly: false,
                    startMuted: true
                };
                
                const participantUrl = bridge.buildJoinRoomUrl('TestRoom', participantSettings);
                
                result.textContent = `🔗 URL Generation Test Results:

✅ Director URL:
${directorUrl}

✅ Participant URL:
${participantUrl}

✅ URL Analysis:
• Director URL contains ?director= parameter: ${directorUrl.includes('?director=')}
• Participant URL contains ?room= parameter: ${participantUrl.includes('?room=')}
• Both URLs point to classic VDO.Ninja interface
• All parameters properly encoded

✅ URL generation is working correctly!
The modern interface will redirect to these classic URLs.`;
                
                result.className = 'result success';
                
            } catch (error) {
                result.textContent = `❌ ERROR: ${error.message}`;
                result.className = 'result';
            }
        }
        
        // Auto-check bridge status when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const result = document.getElementById('url-result');
                if (window.vdoNinjaBridge && window.vdoNinjaBridge.version) {
                    result.textContent = `✅ Simplified Bridge loaded successfully!
Version: ${window.vdoNinjaBridge.version}

🎯 New Approach:
• Modern interface = Beautiful front-end for form collection
• Classic interface = Proven VDO.Ninja functionality
• Always redirect to classic interface after form submission
• 100% compatibility with all VDO.Ninja features

Available Functions:
• modernCreateRoom: ${typeof window.vdoNinjaBridge.modernCreateRoom === 'function'}
• modernJoinRoom: ${typeof window.vdoNinjaBridge.modernJoinRoom === 'function'}
• modernJoinDirector: ${typeof window.vdoNinjaBridge.modernJoinDirector === 'function'}
• buildDirectorRoomUrl: ${typeof window.vdoNinjaBridge.buildDirectorRoomUrl === 'function'}
• buildJoinRoomUrl: ${typeof window.vdoNinjaBridge.buildJoinRoomUrl === 'function'}

Ready to test simplified functionality...`;
                    result.className = 'result success';
                } else {
                    result.textContent = `❌ Bridge not loaded properly. Please refresh the page.`;
                }
            }, 1000);
        });
    </script>
</body>
</html>
