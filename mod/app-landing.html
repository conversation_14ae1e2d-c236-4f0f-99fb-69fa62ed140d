<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>VDO.Ninja - Professional Live Streaming Made Simple</title>
    
    <!-- Meta Tags -->
    <meta name="description" content="Bring live video from your smartphone, computer, or friends directly into your Studio. 100% free with professional quality.">
    <meta name="author" content="<PERSON>">
    <meta name="theme-color" content="#0a0e1a">
    
    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="./media/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./media/favicon-16x16.png">
    <link rel="shortcut icon" href="./media/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="./app-styles.css">
    
    <!-- Import ALL original VDO.Ninja scripts for functionality preservation -->
    <script src="./thirdparty/adapter.js"></script>
    <script src="./main.js"></script>
    <script src="./webrtc.js"></script>
    
    <style>
        /* Landing page specific styles */
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--tertiary-bg) 100%);
            z-index: -2;
        }
        
        .hero-animation {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.1;
            z-index: -1;
        }
        
        .hero-content {
            text-align: center;
            max-width: 800px;
            padding: var(--space-xl);
        }
        
        .hero-title {
            font-size: clamp(2rem, 5vw, 4rem);
            font-weight: 700;
            margin-bottom: var(--space-lg);
            background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-green) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero-subtitle {
            font-size: var(--font-size-xl);
            color: var(--text-secondary);
            margin-bottom: var(--space-2xl);
            line-height: 1.6;
        }
        
        .hero-actions {
            display: flex;
            flex-direction: column;
            gap: var(--space-md);
            align-items: center;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--space-lg);
            margin-top: var(--space-2xl);
        }
        
        .feature-card {
            text-align: center;
            padding: var(--space-xl);
        }
        
        .feature-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto var(--space-lg);
            background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-green) 100%);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .quick-start {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-xl);
            padding: var(--space-xl);
            backdrop-filter: blur(20px);
            margin-top: var(--space-2xl);
        }
        
        .connection-animation {
            width: 100%;
            height: 200px;
            background: var(--glass-bg);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-lg);
            position: relative;
            overflow: hidden;
        }
        
        .connection-node {
            position: absolute;
            width: 12px;
            height: 12px;
            background: var(--accent-blue);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        .connection-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-blue) 0%, var(--accent-green) 100%);
            animation: flow 3s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }
        
        @keyframes flow {
            0% { opacity: 0; transform: translateX(-100%); }
            50% { opacity: 1; }
            100% { opacity: 0; transform: translateX(100%); }
        }
        
        @media (min-width: 768px) {
            .hero-actions {
                flex-direction: row;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Hidden original functionality container -->
    <div id="original-functionality" style="display: none;">
        <!-- Original VDO.Ninja elements will be preserved here -->
    </div>

    <!-- Modern UI Container -->
    <div id="modern-app" class="app-container">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-background"></div>
            <div class="hero-animation">
                <!-- WebRTC Connection Animation -->
                <div class="connection-animation">
                    <div class="connection-node" style="top: 50%; left: 20%;"></div>
                    <div class="connection-node" style="top: 30%; left: 50%;"></div>
                    <div class="connection-node" style="top: 70%; left: 50%;"></div>
                    <div class="connection-node" style="top: 50%; right: 20%;"></div>
                    <div class="connection-line" style="top: 50%; left: 20%; width: 30%;"></div>
                    <div class="connection-line" style="top: 40%; left: 35%; width: 30%; transform: rotate(20deg);"></div>
                    <div class="connection-line" style="top: 60%; left: 35%; width: 30%; transform: rotate(-20deg);"></div>
                    <div class="connection-line" style="top: 50%; right: 20%; width: 30%;"></div>
                </div>
            </div>
            
            <div class="hero-content fade-in">
                <h1 class="hero-title">VDO.Ninja</h1>
                <p class="hero-subtitle">
                    Professional live streaming made simple.<br>
                    Bring live video from anywhere directly into your studio.
                </p>
                
                <div class="hero-actions">
                    <a href="./app-create-room.html" class="btn btn-primary btn-lg">
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                        </svg>
                        Create Room
                    </a>
                    <a href="./app-join-room.html" class="btn btn-secondary btn-lg">
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-2 0V5H5v10h10v-1a1 1 0 112 0v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm13.293 4.293a1 1 0 011.414 1.414L15.414 12l2.293 2.293a1 1 0 01-1.414 1.414L14 13.414l-2.293 2.293a1 1 0 01-1.414-1.414L12.586 12l-2.293-2.293a1 1 0 011.414-1.414L14 10.586l2.293-2.293z" clip-rule="evenodd"/>
                        </svg>
                        Join Room
                    </a>
                    <a href="./app-join-director.html" class="btn btn-accent btn-lg" style="grid-column: 1 / -1; justify-self: center; max-width: 240px;">
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                        </svg>
                        Join as Director
                    </a>
                </div>
                
    
                
               
            </div>
        </section>
        
        <!-- Footer -->
        <footer class="app-footer">
            <p>&copy; 2024 Steve Seguin. VDO.Ninja is open source and free to use.</p>
            <p class="text-xs mt-sm">
                <a href="https://github.com/steveseguin/vdo.ninja" class="text-accent-blue hover:text-accent-green transition-colors">
                    View Source Code
                </a>
                |
                <a href="./index.html" class="text-accent-blue hover:text-accent-green transition-colors">
                    Classic Interface
                </a>
            </p>
        </footer>
    </div>

    <!-- Bridge Script -->
    <script src="./app-bridge.js?v=6"></script>
    
    <script>
        // Landing page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth scrolling and animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in');
                    }
                });
            }, observerOptions);
            
            // Observe all cards for animation
            document.querySelectorAll('.feature-card, .quick-start').forEach(card => {
                observer.observe(card);
            });
            
            // Add keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && e.target.matches('a, button')) {
                    e.target.click();
                }
            });
            
            // Check for saved room preferences
            const savedRoom = localStorage.getItem('vdo-ninja-last-room');
            if (savedRoom) {
                const quickJoinBtn = document.createElement('a');
                quickJoinBtn.href = `./app-join-room.html?room=${encodeURIComponent(savedRoom)}`;
                quickJoinBtn.className = 'btn btn-secondary btn-sm';
                quickJoinBtn.innerHTML = `
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clip-rule="evenodd"/>
                    </svg>
                    Rejoin "${savedRoom}"
                `;
                
                const heroActions = document.querySelector('.hero-actions');
                heroActions.appendChild(quickJoinBtn);
            }
        });
    </script>
</body>
</html>
