<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Join Room - VDO.Ninja</title>
    
    <!-- Meta Tags -->
    <meta name="description" content="Join a VDO.Ninja room for live streaming and video communication">
    <meta name="theme-color" content="#0a0e1a">
    
    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="./media/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./media/favicon-16x16.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="./app-styles.css">
    
    <!-- Import ALL original VDO.Ninja scripts for functionality preservation -->
    <script src="./thirdparty/adapter.js"></script>
    <script src="./main.js"></script>
    <script src="./webrtc.js"></script>
</head>
<body>
    <!-- Hidden original functionality container -->
    <div id="original-functionality" style="display: none;">
        <!-- Original VDO.Ninja elements will be preserved here -->
    </div>

    <!-- Modern UI Container -->
    <div id="modern-app" class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-md">
                    <a href="./app-landing.html" class="btn btn-secondary btn-sm">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                        </svg>
                        Back
                    </a>
                    <h1 class="text-xl font-semibold">Join Room</h1>
                </div>
                <div class="status-indicator">
                    <div class="status-dot" data-connection-status></div>
                    <span data-connection-status>Disconnected</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <div class="max-w-2xl mx-auto w-full">
                <!-- Join Room Form -->
                <form id="join-room-form" data-modern-form="join-room" class="space-y-lg">
                    <!-- Room Information -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">Room Information</h2>
                            <p class="card-description">Enter the room code or link to join</p>
                        </div>
                        
                        <div class="form-group">
                            <label for="roomInput" class="form-label">Room Code or Link</label>
                            <input 
                                type="text" 
                                id="roomInput" 
                                name="roomId" 
                                class="form-input" 
                                placeholder="Enter room code or paste room link"
                                required
                            >
                            <p class="text-xs text-muted mt-xs">You can paste a full VDO.Ninja link or just the room code</p>
                        </div>
                        
                        <div class="form-group">
                            <label for="displayName" class="form-label">Your Display Name</label>
                            <input 
                                type="text" 
                                id="displayName" 
                                name="displayName" 
                                class="form-input" 
                                placeholder="Enter your name"
                                required
                            >
                        </div>
                        
                        <div class="form-group">
                            <label for="roomPassword" class="form-label">Room Password (if required)</label>
                            <input 
                                type="password" 
                                id="roomPassword" 
                                name="roomPassword" 
                                class="form-input" 
                                placeholder="Enter room password if required"
                            >
                        </div>
                    </div>

                    <!-- Device Setup -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">Device Setup</h2>
                            <p class="card-description">Configure your camera and microphone</p>
                        </div>
                        
                        <!-- Device Preview -->
                        <div class="device-preview" id="device-preview">
                            <video id="preview-video" autoplay muted playsinline style="display: none;"></video>
                            <div id="no-video-placeholder" class="text-center">
                                <div class="text-4xl mb-md">📹</div>
                                <p class="text-muted">Camera preview will appear here</p>
                                <button type="button" class="btn btn-primary mt-md" onclick="requestDeviceAccess()">
                                    Enable Camera & Microphone
                                </button>
                            </div>
                        </div>
                        
                        <!-- Audio Level Meter -->
                        <div class="audio-meter" id="audio-meter" style="display: none;">
                            <div class="audio-meter-fill" id="audio-meter-fill"></div>
                        </div>
                        
                        <!-- Device Controls -->
                        <div class="device-controls" id="device-controls" style="display: none;">
                            <select id="camera-select" class="device-select">
                                <option value="">Select Camera</option>
                            </select>
                            <select id="microphone-select" class="device-select">
                                <option value="">Select Microphone</option>
                            </select>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="flipCamera()">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"/>
                                </svg>
                                Flip
                            </button>
                        </div>
                        
                        <!-- Quick Options -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-lg mt-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="form-label mb-0">Audio Only</label>
                                    <p class="text-xs text-muted">Join without video</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="audioOnly" onchange="toggleAudioOnly(this)">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="form-label mb-0">Start Muted</label>
                                    <p class="text-xs text-muted">Join with microphone muted</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="startMuted">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Connection Test -->
                    <div class="card" id="connection-test" style="display: none;">
                        <div class="card-header">
                            <h2 class="card-title">Connection Test</h2>
                            <p class="card-description">Testing your connection quality</p>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-lg">
                            <div class="text-center">
                                <div class="status-indicator justify-center mb-sm">
                                    <div class="status-dot" id="camera-status"></div>
                                    <span>Camera</span>
                                </div>
                                <p class="text-xs text-muted" id="camera-info">Testing...</p>
                            </div>
                            
                            <div class="text-center">
                                <div class="status-indicator justify-center mb-sm">
                                    <div class="status-dot" id="microphone-status"></div>
                                    <span>Microphone</span>
                                </div>
                                <p class="text-xs text-muted" id="microphone-info">Testing...</p>
                            </div>
                            
                            <div class="text-center">
                                <div class="status-indicator justify-center mb-sm">
                                    <div class="status-dot" id="network-status"></div>
                                    <span>Network</span>
                                </div>
                                <p class="text-xs text-muted" id="network-info">Testing...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col md:flex-row gap-md justify-center">
                        <button type="submit" class="btn btn-primary btn-lg" data-action="join-room" id="join-button" disabled>
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-2 0V5H5v10h10v-1a1 1 0 112 0v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm13.293 4.293a1 1 0 011.414 1.414L15.414 12l2.293 2.293a1 1 0 01-1.414 1.414L14 13.414l-2.293 2.293a1 1 0 01-1.414-1.414L12.586 12l-2.293-2.293a1 1 0 011.414-1.414L14 10.586l2.293-2.293z" clip-rule="evenodd"/>
                            </svg>
                            Join Room
                        </button>
                        <button type="button" class="btn btn-secondary btn-lg" onclick="testConnection()">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                            Test Connection
                        </button>
                    </div>
                </form>

                <!-- Troubleshooting Tips -->
                <div class="card mt-lg" id="troubleshooting" style="display: none;">
                    <div class="card-header">
                        <h3 class="card-title">Connection Issues?</h3>
                        <p class="card-description">Try these troubleshooting steps</p>
                    </div>
                    
                    <div class="space-y-sm">
                        <div class="flex items-start gap-sm">
                            <div class="w-2 h-2 rounded-full bg-accent-blue mt-2 flex-shrink-0"></div>
                            <p class="text-sm">Check that your camera and microphone permissions are enabled</p>
                        </div>
                        <div class="flex items-start gap-sm">
                            <div class="w-2 h-2 rounded-full bg-accent-blue mt-2 flex-shrink-0"></div>
                            <p class="text-sm">Ensure you have a stable internet connection</p>
                        </div>
                        <div class="flex items-start gap-sm">
                            <div class="w-2 h-2 rounded-full bg-accent-blue mt-2 flex-shrink-0"></div>
                            <p class="text-sm">Try refreshing the page and rejoining</p>
                        </div>
                        <div class="flex items-start gap-sm">
                            <div class="w-2 h-2 rounded-full bg-accent-blue mt-2 flex-shrink-0"></div>
                            <p class="text-sm">Check if the room code or link is correct</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bridge Script -->
    <script src="./app-bridge.js?v=6"></script>
    
    <script>
        // Join room specific JavaScript
        let currentStream = null;
        let audioContext = null;
        let audioAnalyser = null;
        let deviceAccessGranted = false;
        
        // Parse URL parameters on load
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            
            // Auto-fill room input if provided in URL
            if (urlParams.has('room')) {
                document.getElementById('roomInput').value = urlParams.get('room');
            }
            
            // Auto-fill password if provided
            if (urlParams.has('password')) {
                document.getElementById('roomPassword').value = urlParams.get('password');
            }
            
            // Load saved display name
            const savedName = localStorage.getItem('vdo-ninja-display-name');
            if (savedName) {
                document.getElementById('displayName').value = savedName;
            }
            
            // Enable join button when required fields are filled
            validateForm();
            
            // Add input listeners
            document.getElementById('roomInput').addEventListener('input', handleRoomInput);
            document.getElementById('displayName').addEventListener('input', validateForm);
            document.getElementById('displayName').addEventListener('blur', saveDisplayName);
        });
        
        function handleRoomInput(event) {
            const input = event.target.value.trim();
            
            // Auto-detect if it's a full URL and extract room info
            if (input.includes('vdo.ninja') || input.includes('://')) {
                try {
                    const url = new URL(input.startsWith('http') ? input : 'https://' + input);
                    const params = new URLSearchParams(url.search);
                    
                    if (params.has('room')) {
                        event.target.value = params.get('room');
                        if (params.has('password')) {
                            document.getElementById('roomPassword').value = params.get('password');
                        }
                    }
                } catch (e) {
                    // Not a valid URL, treat as room code
                }
            }
            
            validateForm();
        }
        
        function validateForm() {
            const roomInput = document.getElementById('roomInput').value.trim();
            const displayName = document.getElementById('displayName').value.trim();
            const joinButton = document.getElementById('join-button');
            
            if (roomInput && displayName) {
                joinButton.disabled = false;
                joinButton.classList.remove('opacity-50');
            } else {
                joinButton.disabled = true;
                joinButton.classList.add('opacity-50');
            }
        }
        
        function saveDisplayName() {
            const name = document.getElementById('displayName').value.trim();
            if (name) {
                localStorage.setItem('vdo-ninja-display-name', name);
            }
        }
        
        async function requestDeviceAccess() {
            try {
                window.vdoNinjaBridge.showLoadingState('Requesting device access...');
                
                const constraints = {
                    video: !document.querySelector('input[name="audioOnly"]').checked,
                    audio: true
                };
                
                currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                
                // Update UI
                const previewVideo = document.getElementById('preview-video');
                const placeholder = document.getElementById('no-video-placeholder');
                const deviceControls = document.getElementById('device-controls');
                const audioMeter = document.getElementById('audio-meter');
                
                if (constraints.video && currentStream.getVideoTracks().length > 0) {
                    previewVideo.srcObject = currentStream;
                    previewVideo.style.display = 'block';
                    placeholder.style.display = 'none';
                }
                
                deviceControls.style.display = 'flex';
                audioMeter.style.display = 'block';
                
                // Setup audio monitoring
                setupAudioMonitoring();
                
                // Populate device lists
                await populateDeviceList();
                
                deviceAccessGranted = true;
                window.vdoNinjaBridge.hideLoadingState();
                window.vdoNinjaBridge.showSuccessMessage('Device access granted!');
                
            } catch (error) {
                console.error('Error accessing devices:', error);
                window.vdoNinjaBridge.hideLoadingState();
                window.vdoNinjaBridge.showErrorMessage('Failed to access camera/microphone: ' + error.message);
                document.getElementById('troubleshooting').style.display = 'block';
            }
        }
        
        async function populateDeviceList() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const cameraSelect = document.getElementById('camera-select');
                const microphoneSelect = document.getElementById('microphone-select');
                
                // Clear existing options
                cameraSelect.innerHTML = '<option value="">Select Camera</option>';
                microphoneSelect.innerHTML = '<option value="">Select Microphone</option>';
                
                devices.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.deviceId;
                    option.textContent = device.label || `${device.kind} ${device.deviceId.substring(0, 8)}`;
                    
                    if (device.kind === 'videoinput') {
                        cameraSelect.appendChild(option);
                    } else if (device.kind === 'audioinput') {
                        microphoneSelect.appendChild(option);
                    }
                });
                
                // Set current devices as selected
                if (currentStream) {
                    const videoTrack = currentStream.getVideoTracks()[0];
                    const audioTrack = currentStream.getAudioTracks()[0];
                    
                    if (videoTrack) {
                        cameraSelect.value = videoTrack.getSettings().deviceId || '';
                    }
                    if (audioTrack) {
                        microphoneSelect.value = audioTrack.getSettings().deviceId || '';
                    }
                }
                
            } catch (error) {
                console.error('Error enumerating devices:', error);
            }
        }
        
        function setupAudioMonitoring() {
            if (!currentStream || !currentStream.getAudioTracks().length) return;
            
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                audioAnalyser = audioContext.createAnalyser();
                const source = audioContext.createMediaStreamSource(currentStream);
                source.connect(audioAnalyser);
                
                audioAnalyser.fftSize = 256;
                const bufferLength = audioAnalyser.frequencyBinCount;
                const dataArray = new Uint8Array(bufferLength);
                
                function updateAudioMeter() {
                    audioAnalyser.getByteFrequencyData(dataArray);
                    const average = dataArray.reduce((a, b) => a + b) / bufferLength;
                    const percentage = (average / 255) * 100;
                    
                    document.getElementById('audio-meter-fill').style.width = percentage + '%';
                    
                    if (deviceAccessGranted) {
                        requestAnimationFrame(updateAudioMeter);
                    }
                }
                
                updateAudioMeter();
            } catch (error) {
                console.error('Error setting up audio monitoring:', error);
            }
        }
        
        function toggleAudioOnly(checkbox) {
            const previewVideo = document.getElementById('preview-video');
            const placeholder = document.getElementById('no-video-placeholder');
            
            if (checkbox.checked) {
                // Audio only mode
                if (currentStream) {
                    currentStream.getVideoTracks().forEach(track => track.stop());
                }
                previewVideo.style.display = 'none';
                placeholder.style.display = 'block';
                placeholder.innerHTML = `
                    <div class="text-4xl mb-md">🎤</div>
                    <p class="text-muted">Audio only mode</p>
                `;
            } else {
                // Re-enable video
                if (deviceAccessGranted) {
                    requestDeviceAccess();
                }
            }
        }
        
        function flipCamera() {
            // This would integrate with the original VDO.Ninja camera flip functionality
            console.log('Flipping camera...');
            window.vdoNinjaBridge.showSuccessMessage('Camera flipped!');
        }
        
        function testConnection() {
            const connectionTest = document.getElementById('connection-test');
            connectionTest.style.display = 'block';
            
            // Simulate connection testing
            const tests = [
                { id: 'camera-status', info: 'camera-info', delay: 500 },
                { id: 'microphone-status', info: 'microphone-info', delay: 1000 },
                { id: 'network-status', info: 'network-info', delay: 1500 }
            ];
            
            tests.forEach(test => {
                setTimeout(() => {
                    const statusDot = document.getElementById(test.id);
                    const infoText = document.getElementById(test.info);
                    
                    statusDot.classList.add('connected');
                    infoText.textContent = 'OK';
                }, test.delay);
            });
            
            setTimeout(() => {
                window.vdoNinjaBridge.showSuccessMessage('Connection test completed successfully!');
            }, 2000);
        }
        
        // Override the bridge's join room function to handle UI updates
        const originalJoinRoom = window.vdoNinjaBridge.modernJoinRoom;
        window.vdoNinjaBridge.modernJoinRoom = function(roomId, settings) {
            try {
                // Save room for quick rejoin
                localStorage.setItem('vdo-ninja-last-room', roomId);

                // Call original function - this will navigate to VDO.Ninja
                const result = originalJoinRoom.call(this, roomId, settings);

                return result;
            } catch (error) {
                console.error('Error joining room:', error);
                document.getElementById('troubleshooting').style.display = 'block';
                throw error;
            }
        };
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
            }
            if (audioContext) {
                audioContext.close();
            }
        });
    </script>
</body>
</html>
