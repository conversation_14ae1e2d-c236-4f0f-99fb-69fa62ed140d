<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Communications Room - VDO.Ninja</title>
    
    <!-- Meta Tags -->
    <meta name="description" content="VDO.Ninja communications room for live video streaming">
    <meta name="theme-color" content="#0a0e1a">
    
    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="./media/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./media/favicon-16x16.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="./app-styles.css">
    
    <!-- Import ALL original VDO.Ninja scripts for functionality preservation -->
    <script src="./thirdparty/adapter.js"></script>
    <script src="./main.js"></script>
    <script src="./webrtc.js"></script>
    
    <style>
        /* Communications room specific styles */
        .room-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--primary-bg);
        }
        
        .room-header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: var(--space-md) var(--space-lg);
            display: flex;
            align-items: center;
            justify-content: space-between;
            z-index: 100;
        }
        
        .room-main {
            flex: 1;
            display: flex;
            position: relative;
            overflow: hidden;
        }
        
        .video-area {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-lg);
            position: relative;
        }
        
        .chat-panel {
            width: 300px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-left: 1px solid var(--glass-border);
            display: flex;
            flex-direction: column;
            transform: translateX(100%);
            transition: transform var(--transition-normal);
        }

        .chat-panel.open {
            transform: translateX(0);
        }

        /* Responsive layout for communications room */
        @media (max-width: 768px) {
            .room-header {
                padding: var(--space-sm) var(--space-md);
                flex-wrap: wrap;
                gap: var(--space-sm);
            }

            .chat-panel {
                width: 100%;
                position: absolute;
                top: 0;
                right: 0;
                height: 100%;
                z-index: 200;
            }

            .video-area {
                padding: var(--space-md);
            }
        }

        @media (max-width: 480px) {
            .room-header {
                padding: var(--space-xs) var(--space-sm);
            }

            .video-area {
                padding: var(--space-sm);
            }
        }
        
        .chat-header {
            padding: var(--space-md);
            border-bottom: 1px solid var(--glass-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .chat-messages {
            flex: 1;
            padding: var(--space-md);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: var(--space-sm);
        }
        
        .chat-input-area {
            padding: var(--space-md);
            border-top: 1px solid var(--glass-border);
        }
        
        .chat-message {
            background: var(--tertiary-bg);
            border-radius: var(--radius-md);
            padding: var(--space-sm) var(--space-md);
            font-size: var(--font-size-sm);
        }
        
        .chat-message.own {
            background: var(--accent-blue);
            color: white;
            align-self: flex-end;
        }
        
        .participants-panel {
            position: absolute;
            top: var(--space-lg);
            right: var(--space-lg);
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-lg);
            padding: var(--space-md);
            min-width: 200px;
            transform: translateX(100%);
            transition: transform var(--transition-normal);
            z-index: 50;
        }
        
        .participants-panel.open {
            transform: translateX(0);
        }
        
        .participant-item {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            padding: var(--space-sm);
            border-radius: var(--radius-sm);
            margin-bottom: var(--space-xs);
        }
        
        .participant-item:hover {
            background: var(--hover-overlay);
        }
        
        .participant-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--accent-blue);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-sm);
            font-weight: 600;
        }
        
        .screen-share-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary-bg);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 40;
        }
        
        .screen-share-overlay.active {
            display: flex;
        }
        
        .screen-share-video {
            max-width: 100%;
            max-height: 100%;
            border-radius: var(--radius-lg);
        }
        
        .speaking-indicator {
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 3px solid var(--accent-green);
            border-radius: var(--radius-lg);
            opacity: 0;
            animation: speakingPulse 1s infinite;
        }
        
        .speaking-indicator.active {
            opacity: 1;
        }
        
        @keyframes speakingPulse {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.02); }
        }
        
        .connection-quality {
            position: absolute;
            top: var(--space-sm);
            right: var(--space-sm);
            display: flex;
            gap: 2px;
        }
        
        .quality-bar {
            width: 3px;
            height: 12px;
            background: var(--text-muted);
            border-radius: 1px;
        }
        
        .quality-bar.active {
            background: var(--accent-green);
        }
        
        .quality-bar.medium {
            background: var(--accent-orange);
        }
        
        .quality-bar.poor {
            background: var(--accent-red);
        }
        
        @media (max-width: 768px) {
            .chat-panel {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                width: 100%;
                z-index: 60;
            }
            
            .participants-panel {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                max-width: 90vw;
                max-height: 80vh;
                overflow-y: auto;
            }
            
            .participants-panel.open {
                transform: translate(-50%, -50%);
            }
            
            .floating-controls {
                bottom: var(--space-md);
                left: var(--space-md);
                right: var(--space-md);
                transform: none;
                justify-content: space-around;
            }
        }
    </style>
</head>
<body>
    <!-- Hidden original functionality container -->
    <div id="original-functionality" style="display: none;">
        <!-- Original VDO.Ninja elements will be preserved here -->
    </div>

    <!-- Modern UI Container -->
    <div id="modern-app" class="room-container">
        <!-- Room Header -->
        <header class="room-header">
            <div class="flex items-center gap-md">
                <h1 class="text-lg font-semibold" id="room-title">Room: Loading...</h1>
                <div class="status-indicator">
                    <div class="status-dot connected" data-connection-status></div>
                    <span class="text-sm" data-connection-status>Connected</span>
                </div>
            </div>
            
            <div class="flex items-center gap-sm">
                <button class="btn btn-secondary btn-sm" onclick="toggleParticipants()" id="participants-btn">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                    </svg>
                    <span id="participant-count">1</span>
                </button>
                
                <button class="btn btn-secondary btn-sm" onclick="toggleChat()" id="chat-btn">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"/>
                    </svg>
                    Chat
                </button>
                
                <button class="btn btn-danger btn-sm" onclick="leaveRoom()" id="leave-btn">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clip-rule="evenodd"/>
                    </svg>
                    Leave
                </button>
            </div>
        </header>

        <!-- Main Room Area -->
        <main class="room-main">
            <!-- Video Area -->
            <div class="video-area">
                <!-- Video Grid -->
                <div class="video-grid grid-1x1" id="video-grid">
                    <!-- Local Video Tile -->
                    <div class="video-tile" id="local-video-tile">
                        <video id="local-video" autoplay muted playsinline></video>
                        <div class="speaking-indicator" id="local-speaking"></div>
                        <div class="video-tile-overlay">
                            <span class="participant-name">You</span>
                            <div class="participant-controls">
                                <div class="control-btn" id="local-audio-status">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="control-btn" id="local-video-status">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="connection-quality">
                            <div class="quality-bar active"></div>
                            <div class="quality-bar active"></div>
                            <div class="quality-bar active"></div>
                            <div class="quality-bar"></div>
                        </div>
                    </div>
                    
                    <!-- Remote video tiles will be added dynamically -->
                </div>
                
                <!-- Screen Share Overlay -->
                <div class="screen-share-overlay" id="screen-share-overlay">
                    <video id="screen-share-video" class="screen-share-video" autoplay playsinline></video>
                </div>
            </div>

            <!-- Chat Panel -->
            <div class="chat-panel" id="chat-panel">
                <div class="chat-header">
                    <h3 class="text-md font-semibold">Chat</h3>
                    <button class="btn btn-secondary btn-sm" onclick="toggleChat()">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                </div>
                
                <div class="chat-messages" id="chat-messages">
                    <div class="chat-message">
                        <strong>System:</strong> Welcome to the room!
                    </div>
                </div>
                
                <div class="chat-input-area">
                    <form onsubmit="sendMessage(event)" class="flex gap-sm">
                        <input 
                            type="text" 
                            id="chat-input" 
                            class="form-input flex-1" 
                            placeholder="Type a message..."
                            maxlength="500"
                        >
                        <button type="submit" class="btn btn-primary btn-sm">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
                            </svg>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Participants Panel -->
            <div class="participants-panel" id="participants-panel">
                <div class="flex items-center justify-between mb-md">
                    <h3 class="text-md font-semibold">Participants</h3>
                    <button class="btn btn-secondary btn-sm" onclick="toggleParticipants()">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                </div>
                
                <div id="participants-list">
                    <div class="participant-item">
                        <div class="participant-avatar">Y</div>
                        <div class="flex-1">
                            <div class="text-sm font-medium">You</div>
                            <div class="text-xs text-muted">Host</div>
                        </div>
                        <div class="status-dot connected"></div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Floating Controls -->
        <div class="floating-controls">
            <button class="btn btn-secondary" onclick="toggleAudio()" id="audio-btn" title="Toggle Microphone">
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20" id="audio-icon">
                    <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"/>
                </svg>
            </button>
            
            <button class="btn btn-secondary" onclick="toggleVideo()" id="video-btn" title="Toggle Camera">
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20" id="video-icon">
                    <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                </svg>
            </button>
            
            <button class="btn btn-secondary" onclick="toggleScreenShare()" id="screenshare-btn" title="Share Screen">
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v8a1 1 0 01-1 1h-2l-1 2H7l-1-2H4a1 1 0 01-1-1V4zm2 4a1 1 0 000 2h.01a1 1 0 100-2H5zm3 0a1 1 0 000 2h.01a1 1 0 100-2H8zm3 0a1 1 0 000 2h.01a1 1 0 100-2H11z" clip-rule="evenodd"/>
                </svg>
            </button>
            
            <button class="btn btn-secondary" onclick="raiseHand()" id="hand-btn" title="Raise Hand">
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7 2a1 1 0 00-.707 1.707L7 4.414v3.758a1 1 0 01-.293.707l-4 4C.817 14.769 2.156 18 4.828 18h10.343c2.673 0 4.012-3.231 2.122-5.121l-4-4A1 1 0 0113 8.172V4.414l.707-.707A1 1 0 0013 2H7zm2 6.172V4h2v4.172a3 3 0 00.879 2.12l1.027 1.028a4 4 0 00-2.171.102l-.47.156a4 4 0 01-2.53 0l-.563-.187a1.993 1.993 0 00-.114-.035l1.063-1.063A3 3 0 009 8.172z" clip-rule="evenodd"/>
                </svg>
            </button>
            
            <button class="btn btn-secondary" onclick="openSettings()" id="settings-btn" title="Settings">
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                </svg>
            </button>
        </div>
    </div>

    <!-- Bridge Script -->
    <script src="./app-bridge.js?v=5"></script>
    
    <script>
        // Communications room specific JavaScript
        let isAudioMuted = false;
        let isVideoMuted = false;
        let isScreenSharing = false;
        let isChatOpen = false;
        let isParticipantsOpen = false;
        let handRaised = false;
        
        // Initialize room on load
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const roomId = urlParams.get('room');
            
            if (roomId) {
                document.getElementById('room-title').textContent = `Room: ${roomId}`;
                initializeRoom(roomId);
            } else {
                window.location.href = './app-join-room.html';
            }
        });
        
        function initializeRoom(roomId) {
            // This would integrate with the original VDO.Ninja room initialization
            console.log('Initializing room:', roomId);
            
            // Simulate joining room
            window.vdoNinjaBridge.updateConnectionState('connected');
            
            // Setup local video
            setupLocalVideo();
            
            // Setup event listeners
            setupEventListeners();
        }
        
        async function setupLocalVideo() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: true
                });
                
                const localVideo = document.getElementById('local-video');
                localVideo.srcObject = stream;
                
                // Store stream reference for controls
                window.localStream = stream;
                
            } catch (error) {
                console.error('Error setting up local video:', error);
                window.vdoNinjaBridge.showErrorMessage('Failed to access camera/microphone');
            }
        }
        
        function setupEventListeners() {
            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.target.tagName === 'INPUT') return;
                
                switch(e.key.toLowerCase()) {
                    case 'm':
                        toggleAudio();
                        break;
                    case 'v':
                        toggleVideo();
                        break;
                    case 's':
                        toggleScreenShare();
                        break;
                    case 'c':
                        toggleChat();
                        break;
                    case 'p':
                        toggleParticipants();
                        break;
                    case 'h':
                        raiseHand();
                        break;
                }
            });
            
            // Auto-hide controls on inactivity
            let inactivityTimer;
            function resetInactivityTimer() {
                clearTimeout(inactivityTimer);
                document.querySelector('.floating-controls').style.opacity = '1';
                inactivityTimer = setTimeout(() => {
                    document.querySelector('.floating-controls').style.opacity = '0.7';
                }, 3000);
            }
            
            document.addEventListener('mousemove', resetInactivityTimer);
            document.addEventListener('keydown', resetInactivityTimer);
            resetInactivityTimer();
        }
        
        function toggleAudio() {
            isAudioMuted = !isAudioMuted;
            const audioBtn = document.getElementById('audio-btn');
            const audioIcon = document.getElementById('audio-icon');
            const localAudioStatus = document.getElementById('local-audio-status');
            
            if (isAudioMuted) {
                audioBtn.classList.add('btn-danger');
                audioBtn.classList.remove('btn-secondary');
                localAudioStatus.classList.add('muted');
                audioIcon.innerHTML = `
                    <path d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z"/>
                `;
                
                // Mute audio track
                if (window.localStream) {
                    window.localStream.getAudioTracks().forEach(track => track.enabled = false);
                }
            } else {
                audioBtn.classList.remove('btn-danger');
                audioBtn.classList.add('btn-secondary');
                localAudioStatus.classList.remove('muted');
                audioIcon.innerHTML = `
                    <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"/>
                `;
                
                // Unmute audio track
                if (window.localStream) {
                    window.localStream.getAudioTracks().forEach(track => track.enabled = true);
                }
            }
            
            console.log('Audio toggled:', isAudioMuted ? 'muted' : 'unmuted');
        }
        
        function toggleVideo() {
            isVideoMuted = !isVideoMuted;
            const videoBtn = document.getElementById('video-btn');
            const videoIcon = document.getElementById('video-icon');
            const localVideo = document.getElementById('local-video');
            const localVideoStatus = document.getElementById('local-video-status');
            
            if (isVideoMuted) {
                videoBtn.classList.add('btn-danger');
                videoBtn.classList.remove('btn-secondary');
                localVideoStatus.classList.add('muted');
                localVideo.style.display = 'none';
                videoIcon.innerHTML = `
                    <path d="M3.28 2.22a.75.75 0 00-1.06 1.06l14.5 14.5a.75.75 0 101.06-1.06L3.28 2.22z"/>
                    <path d="M4 4.5V15a1 1 0 001 1h6a1 1 0 001-1v-1.5l2.5 1.5a.5.5 0 00.5-.5V6a.5.5 0 00-.5-.5L12 7V5.5a1 1 0 00-1-1H4z"/>
                `;
                
                // Disable video track
                if (window.localStream) {
                    window.localStream.getVideoTracks().forEach(track => track.enabled = false);
                }
            } else {
                videoBtn.classList.remove('btn-danger');
                videoBtn.classList.add('btn-secondary');
                localVideoStatus.classList.remove('muted');
                localVideo.style.display = 'block';
                videoIcon.innerHTML = `
                    <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                `;
                
                // Enable video track
                if (window.localStream) {
                    window.localStream.getVideoTracks().forEach(track => track.enabled = true);
                }
            }
            
            console.log('Video toggled:', isVideoMuted ? 'muted' : 'unmuted');
        }
        
        function toggleScreenShare() {
            // This would integrate with the original VDO.Ninja screen sharing functionality
            isScreenSharing = !isScreenSharing;
            const screenshareBtn = document.getElementById('screenshare-btn');
            const screenShareOverlay = document.getElementById('screen-share-overlay');
            
            if (isScreenSharing) {
                screenshareBtn.classList.add('btn-primary');
                screenshareBtn.classList.remove('btn-secondary');
                screenShareOverlay.classList.add('active');
                console.log('Screen sharing started');
            } else {
                screenshareBtn.classList.remove('btn-primary');
                screenshareBtn.classList.add('btn-secondary');
                screenShareOverlay.classList.remove('active');
                console.log('Screen sharing stopped');
            }
        }
        
        function toggleChat() {
            isChatOpen = !isChatOpen;
            const chatPanel = document.getElementById('chat-panel');
            
            if (isChatOpen) {
                chatPanel.classList.add('open');
            } else {
                chatPanel.classList.remove('open');
            }
        }
        
        function toggleParticipants() {
            isParticipantsOpen = !isParticipantsOpen;
            const participantsPanel = document.getElementById('participants-panel');
            
            if (isParticipantsOpen) {
                participantsPanel.classList.add('open');
            } else {
                participantsPanel.classList.remove('open');
            }
        }
        
        function raiseHand() {
            handRaised = !handRaised;
            const handBtn = document.getElementById('hand-btn');
            
            if (handRaised) {
                handBtn.classList.add('btn-warning');
                handBtn.classList.remove('btn-secondary');
                window.vdoNinjaBridge.showSuccessMessage('Hand raised!');
            } else {
                handBtn.classList.remove('btn-warning');
                handBtn.classList.add('btn-secondary');
                window.vdoNinjaBridge.showSuccessMessage('Hand lowered');
            }
            
            console.log('Hand raised:', handRaised);
        }
        
        function sendMessage(event) {
            event.preventDefault();
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            
            if (message) {
                const messagesContainer = document.getElementById('chat-messages');
                const messageElement = document.createElement('div');
                messageElement.className = 'chat-message own';
                messageElement.innerHTML = `<strong>You:</strong> ${message}`;
                
                messagesContainer.appendChild(messageElement);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
                
                input.value = '';
                
                // This would send the message via the original VDO.Ninja chat system
                console.log('Message sent:', message);
            }
        }
        
        function openSettings() {
            // This would open a settings modal or panel
            console.log('Opening settings...');
            window.vdoNinjaBridge.showSuccessMessage('Settings panel would open here');
        }
        
        function leaveRoom() {
            if (confirm('Are you sure you want to leave the room?')) {
                // Cleanup streams
                if (window.localStream) {
                    window.localStream.getTracks().forEach(track => track.stop());
                }
                
                // Call bridge hangup function
                window.vdoNinjaBridge.modernHangup();
                
                // Navigate back to landing page
                setTimeout(() => {
                    window.location.href = './app-landing.html';
                }, 1000);
            }
        }
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (window.localStream) {
                window.localStream.getTracks().forEach(track => track.stop());
            }
        });
    </script>
</body>
</html>
