:root {
	/* Discord Greys - Dark to Lighter */
	--discord-grey-0: #121212;
	--discord-grey-1: #1e1f22;
	--discord-grey-2: #232428;
	--discord-grey-3: #2c2c2d;
	--discord-grey-4: #2e3035;
	--discord-grey-5: #313338;
	--discord-grey-6: #383a40;
	--discord-grey-7: #404249; /* primary */
	--discord-grey-8: #5e6064;

	--discord-text: hsl( 210 calc(1 * 9.1%) 92% /1);

	--darktheme-red: rgb(161, 45, 45);
	--darktheme-blue: rgb(33, 69, 114);
	--darktheme-green: rgb(36, 88, 49);
	--darktheme-lightgreen: #008770;
	--darktheme-brown: rgb(76 58 41);
	--darktheme-yellow: rgb(84, 70, 9);
	
	/* Lightmode white - Darker to lighter */
	--lighttheme-1: #fff;
	--lighttheme-2: #f3f3f3;
	--lighttheme-3: #ddd;
	--lighttheme-4: #ccc; /* primary */
	--lighttheme-5: #bbb;
	--lighttheme-6: #aaa;
	--lighttheme-7: #7e7e7e;
	--lighttheme-8: #373737;
	--lighttheme-text: black;

	/* Director v2 - General colors */
	/* -- Links */
	--a-dark-link: #69aadc;
	--a-dark-visited: #69aadc;
	--a-dark-hover: #6da5dd;
	--a-dark-focus: #6da5dd;
	--a-dark-active: #3a80c6;

	--a-darker-link: #b9dff9;
	--a-darker-visited: #b9dff9;
	--a-darker-hover: #048ae8;
	--a-darker-focus: #d9e4eb;
	--a-darker-active: #d9e4eb;

	--a-lighter-link: #9ed0e1;
	--a-lighter-visited: #9ed0e1;
	--a-lighter-hover: #8acee4;
	--a-lighter-focus: #8acee4;
	--a-lighter-active: #89d5ee;

	--a-link:  #144267;
	--a-visited: #144267;
	--a-hover: #38668c;
	--a-focus: #38668c;
	--a-active: #0165b5;

	/* -- Box colors */
	--director-box: rgb(165 119 18);
	--codirector-box: rgb(67 122 213);

	--director-dark-box: rgb(165 119 18);
	--codirector-dark-box: rgb(129 127 127);
	
	--widget-width: 25%;
	
	/* Original colors */
	--background-color: #141926;
	--dark-background-color: #02050c;
	--container-color: #373737;
	--button-color: #2A2A2A;
	--blue-accent: #4a4c63;
	--red-accent: #553737;
	--light-grey: #ddd;
	--near-black: #02050c;
	--green-accent: #3f4f50;
	--olive-accent: #535D32;
	--regular-margin: 10px;
	--director-margin: 15px 20px 0 0;
	--fit-style: contain;
	--fadein-speed: 0;
	--video-margin: 0px;
	--video-rounded: 0px;
	--video-border: 0px;
	--video-border-color: #0000;
	--video-holder-color: #0000;
	--video-rounded: 0px;
	--button-radius: 2px;
	--myvideo-max-width: min(800px,100vw);
	--myvideo-width:unset;
	--myvideo-height:auto;
	--myvideo-background: #FFF1;
	--video-background-image: url("data:image/svg+xml,%3Csvg viewBox='-42 0 512 512.002' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m210.351562 246.632812c33.882813 0 63.222657-12.152343 87.195313-36.128906 23.972656-23.972656 36.125-53.304687 36.125-87.191406 0-33.875-12.152344-63.210938-36.128906-87.191406-23.976563-23.96875-53.3125-36.121094-87.191407-36.121094-33.886718 0-63.21875 12.152344-87.191406 36.125s-36.128906 53.308594-36.128906 87.1875c0 33.886719 12.15625 63.222656 36.132812 87.195312 23.976563 23.96875 53.3125 36.125 87.1875 36.125zm0 0'/%3E%3Cpath d='m426.128906 393.703125c-.691406-9.976563-2.089844-20.859375-4.148437-32.351563-2.078125-11.578124-4.753907-22.523437-7.957031-32.527343-3.308594-10.339844-7.808594-20.550781-13.371094-30.335938-5.773438-10.15625-12.554688-19-20.164063-26.277343-7.957031-7.613282-17.699219-13.734376-28.964843-18.199219-11.226563-4.441407-23.667969-6.691407-36.976563-6.691407-5.226563 0-10.28125 2.144532-20.042969 8.5-6.007812 3.917969-13.035156 8.449219-20.878906 13.460938-6.707031 4.273438-15.792969 8.277344-27.015625 11.902344-10.949219 3.542968-22.066406 5.339844-33.039063 5.339844-10.972656 0-22.085937-1.796876-33.046874-5.339844-11.210938-3.621094-20.296876-7.625-26.996094-11.898438-7.769532-4.964844-14.800782-9.496094-20.898438-13.46875-9.75-6.355468-14.808594-8.5-20.035156-8.5-13.3125 0-25.75 2.253906-36.972656 6.699219-11.257813 4.457031-21.003906 10.578125-28.96875 18.199219-7.605469 7.28125-14.390625 16.121094-20.15625 26.273437-5.558594 9.785157-10.058594 19.992188-13.371094 30.339844-3.199219 10.003906-5.875 20.945313-7.953125 32.523437-2.058594 11.476563-3.457031 22.363282-4.148437 32.363282-.679688 9.796875-1.023438 19.964844-1.023438 30.234375 0 26.726562 8.496094 48.363281 25.25 64.320312 16.546875 15.746094 38.441406 23.734375 65.066406 23.734375h246.53125c26.625 0 48.511719-7.984375 65.0625-23.734375 16.757813-15.945312 25.253906-37.585937 25.253906-64.324219-.003906-10.316406-.351562-20.492187-1.035156-30.242187zm0 0'/%3E%3C/svg%3E");
	--background-main-image: unset;
	--show-codirectors: inline-block;
	--full-screen-button: inherit;
	--color-mode: light;
	--video-background-image-size: auto 30%;
}

/* Changes color-mode based on what theme the browser states */
@media (prefers-color-scheme: dark) {
	:root {
		--color-mode: dark;
	}
}


* {
	padding: 0;
	margin: 0;
	box-sizing: border-box;
	border: 0;
}

::selection {
	background-color: #0447c888;
	color: #FFF;
}

button:hover,[role="button"]
:not(.column)
:not(.controlsGrid)
:not(#controlButtons)
:hover{
	filter: brightness(98%);
}

table {
	display: inline-block;
	padding:10px;
	margin:10px;
}

.drawingCanvas {
	position: absolute;
	top: 0;
	left: 0;
	width:100%;
	height:100%;
}
.buttonContainer {
	position: absolute;
	bottom:0;
	left:0;
	margin:5px;
}
.buttonContainer button {
	margin:5px 2px;
}

.promptModalLabel{
	cursor: pointer;
	font-weight: normal;
	font-size: 1.0em;
	display:block;
	margin: 17px 20px 15px 20px;
}

#bigPlayButton {
	margin:0 auto;
	background-color: #0000;
	cursor:pointer;
	font-family: Cousine, monospace;
	font-size: 4em;
	line-height: 1.5em;
	letter-spacing: 0.0em;
	text-shadow: 0.05em 0.05em 0px rgba(0,0,0,1);
	width:100%;
	height:100vh;
	z-index: 1;
	vertical-align: top;
	text-align: center;
	top: 0;
	position: fixed;
	overflow-wrap: anywhere;
	padding:3%;
}

.playButton {
	border-radius: 50vh;
	width: min(30vw, 30vh);
	cursor: pointer;
	opacity: 100%;
	background-color: #bbb;
	background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='utf-8'%3F%3E%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 122.88 122.88' style='enable-background:new 0 0 122.88 122.88' xml:space='preserve'%3E%3Cstyle type='text/css'%3E.st0%7Bfill-rule:evenodd;clip-rule:evenodd;%7D%3C/style%3E%3Cg%3E%3Cpath class='st0' d='M61.44,0c33.93,0,61.44,27.51,61.44,61.44s-27.51,61.44-61.44,61.44S0,95.37,0,61.44S27.51,0,61.44,0L61.44,0z M83.31,65.24c3.13-2.02,3.12-4.27,0-6.06L50.98,40.6c-2.55-1.6-5.21-0.66-5.14,2.67l0.1,37.55c0.22,3.61,2.28,4.6,5.32,2.93 L83.31,65.24L83.31,65.24z M61.44,12.48c27.04,0,48.96,21.92,48.96,48.96c0,27.04-21.92,48.96-48.96,48.96S12.48,88.48,12.48,61.44 C12.48,34.4,34.4,12.48,61.44,12.48L61.44,12.48z'/%3E%3C/g%3E%3C/svg%3E");
	display: inline-block;
	height: min(30vw, 30vh);
	background-repeat: no-repeat;
	border: #bbb 3vh solid;
	position: absolute;
}

#bigPlayButton>.playButton {
	width: min(50vw, 50vh);
	margin-top: 10vh;
	background-color: #646262;
	height: min(50vw, 50vh);
	border: #646262 7vh solid;
	position: unset;
	position: static;
}

#progressContainer {
    width: 100%;
    background-color: #ddd9;
	position: absolute;
	right: 0;
	bottom: 0;
}
#progressBar {
    width: 0%;
    height: 18px;
    background-color: #4CAF5099;
    text-align: left;
    color: black;
}

select#audioSource {
	max-height: 80px; /* Initial height */
	overflow-y: auto; /* Enable scrolling */
	transition: max-height 0.3s ease; /* Smooth transition for expanding and collapsing */
	width:100%;
	margin-top: 7px;
	padding: 3px 4px;
	min-height: 24px; 
	user-select: none;
}
select#audioSource.expanded {
	max-height: none;
}
select#audioSource.expanded option {
    display: block;
}
select#audioSource option:checked {
    display: block;
	background-color: #1967D2!important;
    color: white;
}
select#audioSource option {
    display: none;
}
select#audioSource option:hover {
    background-color: #8dbdd4!important;
    color: black;
}
select#audioSource[size='1'] option {
    background-color: var(--lighttheme-1)!important;
	color:black;
}
.darktheme select#audioSource[size='1'] option {
    background-color: var(--light-grey)!important;
	color:black;
}
.paused {
	cursor: pointer;
}

tr {
	padding:4px;
}
th {
	padding:4px;
}

.popupSelector_constraints .preSelectbutton {
	display: inline-block;
	margin: 4px 0 4px 3px;
	padding: 2px 8px 1px 8px;
}
.advancedVideoSettings .preSelectButton {
	display: inline-block;
	margin: 4px 0 4px 3px;
	padding: 2px 8px 1px 8px;
}
.meter {
	display: inline-block;
	width: 0px;
	height: 10px;
	background: green;
	transition: all 100ms linear;
}
.meter2 {
	display: inline-block;
	width: 0px;
	height: 10px;
	background: yellow;
	transition: all 50ms linear;
}
.meter3 {
	display: inline-block;
	width: 0px;
	height: 10px;
	background: red;
	transition: all 25ms linear;
}
.meter4 {
	display: inline-block;
	width: 2px;
	height: 10px;
	background: black;
	position:relative;
	float:left;
}
#mynetwork {
	width: 600px;
	height: 400px;
	border: 1px solid lightgray;
}

.email {
	unicode-bidi: bidi-override;
	direction: rtl;
	user-select: none;
}

a:link {
	color: var(--a-link);
}
a:visited {
	color: var(--a-visited);
}
a:hover {
	color: var(--a-hover);
}
a:focus {
	color: var(--a-focus);
}
a:active {
	color: var(--a-active);
}

a.soloLink:link {
	cursor: grab;
	font-size: 1.2em;
	font-weight: 700;
	padding: 4px 0 2px 0;
	border-radius: 5px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	color: #b4d7f6;
}
a.soloLink:visited{
	color: #b4d7f6;
}

/* Links */
a {
	-webkit-app-region: no-drag;
	text-decoration: none;
}
.darktheme a:link {
	color: var(--a-dark-link);
}
.darktheme a:visited {
	color: var(--a-dark-visited);
}
.darktheme a:hover {
	color: var(--a-dark-hover);
}
.darktheme a:focus {
	color: var(--a-dark-focus);
}
.darktheme a:active {
	color: var(--a-dark-active);
}

.directorContainer a:link {
	color: var(--a-lighter-link);
}
.directorContainer a:visited {
	color: var(--a-lighter-visited);
}
.directorContainer a:hover {
	color: var(--a-lighter-hover);
}
.directorContainer a:focus {
	color: var(--a-lighter-focus);
}
.directorContainer a:active {
	color: var(--a-lighter-active);
}

.infoblob a:link {
	color: var(--a-lighter-link);
}
.infoblob a:visited {
	color: var(--a-lighter-visited);
}
.infoblob a:hover {
	color: var(--a-lighter-hover);
}
.infoblob a:focus {
	color: var(--a-lighter-focus);
}
.infoblob a:active {
	color: var(--a-lighter-active);
}

.darktheme .infoblob a:link {
	color: var(--a-darker-link);
}
.darktheme .infoblob a:visited {
	color: var(--a-darker-visited);
}
.darktheme .infoblob a:hover {
	color: var(--a-darker-hover);
}
.darktheme .infoblob a:focus {
	color: var(--a-darker-focus);
}
.darktheme .infoblob a:active {
	color: var(--a-darker-active);
}

input {
	border-radius: 4px;
	padding: 2px;
	-webkit-app-region: no-drag;
}

button.grey {
	-webkit-app-region: no-drag;
	padding: 10px;
	margin: 10px 0px;
	cursor: pointer;
	border-radius: 2px;
	background-color: var(--button-color);
	color: white;
}

button.hint {
	-webkit-box-shadow: inset 0px 0px 25px #0004;
	-moz-box-shadow: inset 0px 0px 25px #0004;
	box-shadow: inset 0px 0px 25px #0004;
}

#miniPerformer > video, #miniPerformer > canvas{
	width: 80px;
	height: 45px;
	margin: 5px;
	background-color: #464749 !important;
	background-size: 50%;
}

#popOutChat{
	cursor:pointer;
	text-align:right;
	color:#B3C7F9;
	margin: 0 5px;
	cursor: pointer;
	padding:3px;
	background-color: black;
    border-radius: 50%;
	border: solid #B3C7F9 1px;
}

#closeChat {
	cursor: pointer;
	text-align: right;
	color: #B3C7F9;
	margin: 0 5px;
	cursor: pointer;
	padding: 3px 8px;
	background-color: black;
	border-radius: 50%;
	border: solid #B3C7F9 1px;
}

/* Clicked buttons overwrite */
.red {
	background-color: #840000 !important;
}
.red:hover {
	background-color: #b30c0c !important;
}

.green {
	background-color: #64c04d !important;
}

.green:hover {
	background-color: #76c762 !important;
}

.blue {
	background-color: #161699 !important;
}

.blue:hover {
	background-color: #2727bb !important;
}

.brown {
	background-color: #8d6418 !important;
}

.brown:hover {
	background-color: #a06d10 !important;
}

/* ///////////////////// */

.orange {
	background-color: #673100 !important;
}

#meshcastMenu{
	display: inline-block;
	color: #e0dfdf;
}
#header {
	width: 100%;
	padding: 1px;
	background-color: #0F131D;
	color: #FFF;
	-webkit-app-region: drag;
	min-height: 20px;
}
#head1{
	display: inline-block;
	padding:1px;
	position: relative;
	-webkit-app-region: no-drag;
}
#head4{
	-webkit-app-region: no-drag;
}
#head5 {
	-webkit-app-region: no-drag;
	display: inline-block;
	text-decoration: none;
	color: white;
	text-align: right;
	margin-right: 10px;
	cursor: help;
	float: right;
	font-size: 90%;
	line-height:100%;
	margin-top:2px;
}
#head6 {
	display: inline-block;
	text-decoration: none;
	color: white;
	text-align: left;
	margin-left: 10px;
	pointer-events: none;
	font-weight: 700;
}

#head7 {
	display: inline-block;
	text-decoration: none;
	color: white;
	text-align: left;
	margin-left: 10px;
	pointer-events: none;
	font-weight: 700;
}
#overlayClockContainer{
	margin: 0 auto;
	background-color: #0000;
	color: white;
	font-family: Cousine, monospace;
	font-size: calc(6vh + 6vw / 2);
	letter-spacing: 0.0em;
	text-shadow: 0.05em 0.05em 0px rgb(0 0 0);
	z-index: 6;
	vertical-align: top;
	text-align: right;
	right:0;
	bottom:0;
	position: fixed;
	overflow-wrap: anywhere;
	cursor: pointer;
	user-select: none;
}
#overlayClockContainer.top {
	top:0%;
	bottom:unset;
}
#overlayClockContainer.vmiddle {
	bottom:48%;
	top:unset;
}
#overlayClockContainer.bottom {
	bottom:0%;
	top:unset;
}
#overlayClockContainer.left {
	right:unset;
	left:0;
}
#overlayClockContainer.hmiddle {
	right:45%;
	left:unset;
}
#overlayClockContainer.right {
	right:0;
	left:unset;
}

#overlayClock{
	padding:2px 20px;
	background-color: #0009;
}
#overlayClock video {
	width: calc(22vh + 22vw / 2);
	max-width: 100%;
	max-height:25%;
}
#overlayClock:empty{
	display:none;
}
#overlayClockContainer2{
	margin: 0 auto;
	background-color: #0000;
	color: white;
	font-family: Cousine, monospace;
	font-size: calc(3vh + 3vw / 2);
	letter-spacing: 0.0em;
	text-shadow: 0.05em 0.05em 0px rgb(0 0 0);
	z-index: 6;
	vertical-align: top;
	text-align: right;
	position: fixed;
	right:0;
	bottom:0;
	overflow-wrap: anywhere;
	cursor: pointer;
	user-select: none;
}
#overlayClockContainer2.top {
	top:0%;
	bottom:unset;
}
#overlayClockContainer2.vmiddle {
	bottom:48%;
	top:unset;
}
#overlayClockContainer2.bottom {
	bottom:0%;
	top:unset;
}
#overlayClockContainer2.left {
	right:unset;
	left:0;
}
#overlayClockContainer2.hmiddle {
	right:45%;
	left:unset;
}
#overlayClockContainer2.right {
	right:0;
	left:unset;
}

#overlayClock2{
	padding:0 5px;
	background-color: #0009;
}
#overlayClock2:empty{
	display:none;
}
#stickyMsgs{
	margin:0 auto;
	background-color: #0000;
	color: white;
	font-family: Cousine, monospace;
	font-size: 6vh;
	line-height: 8vh;
	letter-spacing: 0.0em;
	text-shadow: 0.05em 0.05em 0px rgba(0,0,0,1);
	width:100%;
	z-index: 7;
	vertical-align: top;
	text-align: center;
	position: fixed;
	overflow-wrap: anywhere;
	padding:2% 3%;
}
.avatarSelection{
	vertical-align: top;
	margin: 10px 0;
	width:130px;
	display:inline-block;
	margin:0 1px;
	text-align: center;
	cursor:pointer;
}
.overlayCloseBtn{
    padding: 0;
    width: 16px;
    height: 16px;
    position: relative;
    bottom: 7px;
    padding: 18px 18px 20px 18px;
    font-size: 22px;
	margin-left: 20px;
}

#overlayMsgs{
	margin:0 auto;
	background-color: #0000;
	color: white;
	font-family: Cousine, monospace;
	font-size: 6vh;
	line-height: 8vh;
	letter-spacing: 0.0em;
	text-shadow: 0.05em 0.05em 0px rgba(0,0,0,1);
	width:100%;
	height:100vh;
	z-index: 7;
	vertical-align: top;
	text-align: center;
	position: fixed;
	overflow-wrap: anywhere;
	padding:2% 3%;
	pointer-events: none
}
#overlayMsgs span{
	background-color: #000B;
	padding: 2px;
	margin: 0.5vh;
	text-align: center;
	width:100%;
	pointer-events: none
}

.credits {
	color: #101020;
	position: fixed;
	bottom: 0;
	right: 0;
	z-index: -1;
	font-size: 80%;
	margin-right:100px;
}

.credits>a {
	color: #101020;
}

.credits>a:visited {
	color: #101020;
}

body.darktheme .credits {
	color: #707a93;
}

body.darktheme .credits>a {
	color: #707a93;
}

body.darktheme .credits>a:visited {
	color: #707a93;
}

.label {
	float: left;
	font-size: 1.2em;
	color: white;
	display: inline-block;
	position: absolute;
	bottom: 0;
	align-self: center;
	z-index: 1000;
	margin: 5% 20%;
	padding: 1%;
	background-color: black;
}

.advancedAudioSettings, .advancedVideoSettings {
	display: flex;
	max-height: 300px;
	overflow-y: auto;
	width: 100%;
	font-size: 14px;
}

.advancedAudioSettings div {
	display: flex;
	width: 100%;
	align-items: center;
	gap: 4px;
}
.advancedAudioSettings div button {
	padding: 4px;
	height: 24px;
	margin: unset;
	flex: 1;
}

.advancedAudioSettings div select {
	width: 100%;
	border-radius: 4px;
	flex: 2;
	height: 24px;
	box-shadow: 1px 1px 3px rgba(0,0,0,0.75);
	font-size: 14px;
}

.advancedAudioSettings div select[data-chosen='false'], .advancedVideoSettings div select[data-chosen='false'] {
	border: 1px solid red;
}
.advancedAudioSettings div select[data-chosen='true'], .advancedVideoSettings div select[data-chosen='true'] {
	border: 1px solid green;
}

.advancedAudioSettings > div:nth-child(1) {
	flex-direction: column;
	align-items: flex-start;
	width: 100%;
	padding: unset;
}

.darktheme .advancedAudioSettings .settingsLabel, .darktheme .advancedVideoSettings .settingsLabel {
	color: var(--lighttheme-3);
}

.advancedAudioSettings .settingsLabel, .advancedVideoSettings .settingsLabel {
	display: block;
    color: #fff;
    font-family: system-ui,-apple-system,BlinkMacSystemFont,segoe ui,Roboto,Oxygen,Ubuntu,Cantarell,open sans,helvetica neue,sans-serif;
    font-size: 12px;
    margin-top: 15px;
    border-top: 3px solid #4f4c4c;
    width: calc(100% - 4px);
    padding: 5px 0;
}

.advancedVideoSettings div:nth-child(2) {
	display: flex;
	width: 100%;
	align-items: center;
	gap: 4px;
}

.advancedVideoSettings div:nth-child(2) select {
	width: 100%;
	flex: 2;
	height: 24px;
	border-radius: 4px;
	box-shadow: 1px 1px 3px rgba(0,0,0,0.75);
	font-size: 14px;
}

.advancedVideoSettings div:nth-child(2) button {
	width: 100%;
	flex: 1;
	height: 24px;
	margin: unset;
}
.advancedAudioSettings label, .advancedVideoSettings label {
	color: #000;
}
.darktheme .advancedAudioSettings label, .darktheme .advancedVideoSettings label {
	color: #FFF;
}

.pressed.altpress, .altpress {
	background: #673100 !important;
	-webkit-box-shadow: inset 0px 0px 1px #b90000;
	-moz-box-shadow: inset 0px 0px 1px #b90000;
	box-shadow: inset 0px 0px 1px #b90000;
	outline: none;
	color: white;
}
.pressed.armed, .armed {
	background: #BF3F3F !important;
}
#mainmenu.row {
	
	text-align: center;
	margin-top: 10px;
}
#mainmenu.row:after {
	content: "";
	display: table;
	clear: both;
}

hr {
	height: 2px;
	border-width: 0;
	color: gray;
	background-color: gray;
}

.vidcon {
	max-width: 100%;
	border: 0;
}

.tile {
	object-fit: var(--fit-style);
	width: 100%;
	height: 100%;
	overflow: hidden;
	border-radius: var(--video-rounded);
}

#gridlayout, #directorlayout {
	padding: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
	justify-items: stretch;
	border: 0;
	margin: 0;
}
#gridlayout{
	z-index:-1;
}
.directorsgrid {
	justify-items: normal;
	display: block ! important;
	overflow-y: auto !important;
}

.directorsgrid .vidcon video {
	margin: 0 5px;
	padding:0;
	width: 100%;
	max-height: 148px;
	height:unset;
	max-width: 260px;
	min-height: 80px;
}

.directorsgrid .vidcon {
	display: inline-block;
	width: 269.7px!important;
	background: var(--lighttheme-7);
	color: var(--lighttheme-text);
	vertical-align: top;
}

.directorsgrid .vidcon>.las {
	color: black;
	background: #999999;
	width: 90%;
}
#activeShares>div{
	font-weight: normal;
	font-size: 12px;
	margin: 10px 0 0 0;
}

.minimized {
	float: left;
	position: absolute;
	bottom: 0;
	height: 24px;
	overflow:hidden;
	box-shadow: inset -1px 1px white
}

.minimized:nth-child(1) {
	left:0px;
	z-index:10;
}
.minimized:nth-child(2) {
	left:max(100px, calc(10% - 260px));
	z-index:9;
}
.minimized:nth-child(3) {
	left:max(200px, calc(20% - 260px));
	z-index:8;
}
.minimized:nth-child(4) {
	left:max(250px, calc(30% - 260px));
	z-index:7;
}
.minimized:nth-child(5) {
	left:max(300px, calc(40% - 260px));
	z-index:6;
}
.minimized:nth-child(6) {
	left:max(463px, calc(50% - 260px));
	z-index:5;
}
.minimized:nth-child(7) {
	left:max(500px, calc(60% - 260px));
	z-index:4;
}
.minimized:nth-child(8) {
	left:max(650px, calc(70% - 260px));
	z-index:3;
}
.minimized:nth-child(9) {
	left:max(700px, calc(80% - 260px));
	z-index:2;
}
.minimized:nth-child(10) {
	left:max(850px, calc(90% - 260px));
	z-index:1;
}
.minimized:nth-child(11) {
	left:max(900px, calc(100% - 260px));
	z-index:0;
}
.battery {
	border: 3px solid #4192c5;
	width: 11px;
	height: 19px;
	border-radius: 4px;
	position: absolute;
	left: 27px;
	top: 3px;
	background-color: #FFF2;
	font-size: 1.5em;
	z-index: 2;
	cursor: help;
	display:block;
}

.battery-charging{
	margin: 0;
	left: -1px;
	padding: 0;
	position: absolute;
	font-size: 0.54em;
	display: none;
}

.battery[data-plugged="1"] {
	display:none;
}

.battery.warn {
	border: 3px solid #EFAF13;
	animation: blink-warn 2s infinite; 
}
.battery.alert {
	border: 3px solid #e81309;
	animation: blink-alert 1s infinite; 
}
.battery-level {
	background: #30b455;
	position: absolute;
	bottom: 0px;
	right: 0;
	left: 0;
	font-size: 0.7em;
	margin: 0;
	padding: 0;
}

.hasMedia > .battery {
	display:block;
}
.slotsbar {
	border-radius: 6px;
	margin: 3.8px 3.8px 0 3.8px;
	padding: 0 6px;
	box-shadow: 0 0 1px #111;
	cursor:grab;
	color: white;
	text-shadow: 0 0 1px black;
	text-align: center;
}
.slotsbar>button{
	margin: 0;
	padding: 0 10px;
}
.slotsbar:active {
	cursor:grabbing;
}
[data-slot='0'] {
	background: linear-gradient(145deg, #dadada, #b8b8b8);
}
[data-slot='1'] {
	background-color: #00AAAA
}
[data-slot='2'] {
	background-color: #FF0000
}
[data-slot='3'] {
	background-color: #0000FF
}
[data-slot='4'] {
	background-color: #AA00AA
}
[data-slot='5'] {
	background-color: #00FF00
}
[data-slot='6'] {
	background-color: #AAAA00
}
[data-slot='7'] {
	background-color: #AACC44
}
[data-slot='8'] {
	background-color: #CCAA44
}
[data-slot='9'] {
	background-color: #CC44AA
}
[data-slot='10'] {
	background-color: #44AACC
}
#slotpicker{
	box-shadow: 0 0 1px #908080;
	width: 180px;
	margin: 3px;
	background-color: white;
	text-shadow: 0px 0px 1px white;
	vertical-align: middle;
	align-content: center;
	text-align: center;
	position: absolute;
	z-index: 5;
	border-radius: 3px;
	border: 1px solid black;
	outline: 6px solid #444;
}
#slotPicker [data-slot]{
	margin: 2px;
	width: 50px;
	height: 34px;
	border-radius: 3px;
	display: inline-block;
	cursor: pointer;
	font-size:12px;
}
#alertModalMessage [data-slot]{
	margin: 2px;
	cursor: pointer;
	text-shadow: 0px 0px 10px white;
}
.rem-con-count{
	position: absolute;
	left: 49px;
	top: 0px;
	color: white;
	background-color: #0006;
	font-size: 1em;
	z-index: 2;
	cursor: help;
	border-radius: 4px;
	padding: 1px 4px 1px 0px;
}
.signal-meter{
	width: 22px;
	height: 22px;
	position: absolute;
	left: 5px;
	top: 1px;
	background-color: #FFF2;
	font-size: 1.5em;
	z-index: 2;
	cursor: help;
}
.hasMedia > .signal-meter {
	display:block;
}
.signal-meter[data-cpu="0"]>.la-signal {
	display:block;
}
.signal-meter[data-cpu="0"]>.la-fire-alt {
	display:none;
}
.signal-meter[data-cpu="1"]>.la-signal {
	display:none;
}
.signal-meter[data-cpu="1"]>.la-fire-alt {
	display:block;
}
.signal-meter[data-cpu="1"] {
	display:block!important;
}
.signal-meter[data-level="0"] {
	color:#000F;
	display:none;
}
.signal-meter[data-level="1"] {
	color:#FF1B01;
}
.signal-meter[data-level="2"] {
	color:#FF8D01;
}
.signal-meter[data-level="3"] {
	color:#FFD201;
}
.signal-meter[data-level="4"] {
	color:#C6FF01;
}
.signal-meter[data-level="5"] {
	color:#00FF00;
}

.volume-control {
	height: 44px;
	position: absolute;
	left: 0px;
	bottom: 0px;
	font-size: 1.5em;
	z-index: 2;
	cursor: help;
	padding: 18px 5% !important;
	background: #555A;
	border-radius: 0 !important;
	margin: 0!important;
}

.togglePreview{
	border-radius: 11px;
	background-color: #00000044;
	top: calc(19px + 2vh);
	right: 2vw;
	cursor: pointer;
	width: 22px;
	display: flex;
	margin: 5px;
	position: absolute;
	color: white;
	font-size: 22px;
	z-Index:35;
	height: 22px;
}
.togglePreview > .la-eye-slash{
	display:block;
}

.togglePreview > .la-eye{
	display:none;
}
.togglePreview.blinded > .la-eye-slash{
	display:none!important;
}
.togglePreview.blinded > .la-eye{
	display:block!important;
}
.rounded{
	border-radius: 5px;
}

.mirror{
	transform: scaleX(-1);
}

.notification {
	position: absolute;
	top: -4px;
	right: -4px;
	padding: 2px 0;
	border-radius: 50%;
	color: white;
	width: 11px;
	height: 11px;
	margin: 0;
}
.queueNotification {

	padding: 2px 0;
	border-radius: 50%;
	background: #335c3a;
	color: white;
	width: 23px;
	height: 23px;
	margin: 0;
}

button.glyphicon-button:focus,
button.glyphicon-button:active:focus,
button.glyphicon-button.active:focus,
button.glyphicon-button.focus,
button.glyphicon-button:active.focus,
button.glyphicon-button.active.focus {
	outline: none !important;
}

.main {
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0) !important;
	-webkit-tap-highlight-color: transparent !important;
	outline: 0px !important;
	height:100%;
	animation: fadeIn 0.2s; 
	background-size: cover;
	background-image: var(--background-main-image);
	background-repeat: no-repeat;
	background-attachment: fixed;
	background-position: center;
	overflow-x: hidden;
	position: absolute;
	z-index: 0;
}

#controlButtons {
	display: flex;
	position: fixed;
	z-index: 995;
	padding: 0px 10px;
	bottom: 10px;
	padding: 0 10px;
	border: 0;
	min-height: 0; /* Must have a min-height or drag-drop doesn't work */
	pointer-events: none;
	width: 100%;
	justify-content: center;
	align-items: center;
	transform-origin: bottom; /* Keeps it at bottom even if scaled */
}
#controlButtons:empty {
	display: none;
}

.controlPositioning {
	display: flex;
	flex-direction: column;
	justify-content: center;
	position: relative;
	width: 100%;
	height: 100%;
	align-items: center;
}

.resizable-div {
  border: 1px solid #ccc;
  overflow: auto;
  position: relative;
}

#subControlButtons {
	display: flex;
	position: absolute;
	background-color: var(--discord-grey-0);
	box-shadow: 0px 0px 10px rgba(0,0,0,1);
	pointer-events: auto;
	border: #cccccc22 1px solid;
	border-radius: 10px;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
	bottom: 0px;
	min-width: 230px;
	cursor: grab;
}

#subControlButtons:empty{
	display:none;
}

#subControlButtons div, #subControlButtons span button {
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: var(--discord-grey-1);
	opacity: unset;
	border-radius: 8px;
	transition: border-radius 200ms ease-in-out;
	box-shadow: 1px 1px 3px rgba(0,0,0,0.75);
}

#subControlButtons div:hover {
	background-color: var(--discord-grey-3);
	border-radius: 4px;
}
button#press2talk{
	border: 0;
}
#press2talk:hover {
	box-shadow: inset 0px 0px 1px 1px #346324;
}
#press2talk[data-enabled="true"] {
	background: #1e0000;
	-webkit-box-shadow: inset 0px 0px 1px #b90000;
	-moz-box-shadow: inset 0px 0px 1px #b90000;
	outline: none;
}
/* Set for the notification button to use as offset */
#chatbutton {
	position: relative;
}

button.btnArmTransferRoom{
	width:auto;
	margin-left: 2px;
	height:38px;
	border-radius: 15px;
}
button.btnArmTransferRoom i{
	margin-right:3px;
}
button.btnArmTransferRoom:hover{
	background-color: var(--green-accent)!important;
}

button.btnArmTransferRoom.selected{
	background-color: #840000!important;
}

#container.vidcon {
	height:100%;
}

.nocontrolbar #container.vidcon {
	height:100%!important;
}

.labelSmall {
	display:none;
}

@media only screen and (max-width: 640px){
	.labelSmall {
		display:inherit;
		padding:5px;
	}

	#guestTips{
		max-width: 100%;
		font-size: 90%;
	}

	.labelLarge {
		display:none!important;
	}

	.gobutton{
		width: 100vh;
		margin-left: 10px;
		margin-right: 10px;
		max-width: 87%;
	}

	.roomnotes{
		display:none!important;
	}

	#head5 {
		margin-right: 1px;
	}
}
@media only screen and (max-width: 480px){
	#guestTips{
		max-width: 100%;
		font-size: 80%;
	}
}

@media only screen and (max-height: 540px){
	#gridlayout>#container.vidcon {
		height:88%
	}
	#copythisurl {
		font-size:80%;
	}
}
@media only screen and (max-height: 500px){
	#gridlayout>#container.vidcon {
		height:87%
	}
}
@media only screen and (max-height: 400px){
	#logoname{
		display:none;
	}
	#head4{
		display:none;
	}
	#head2{
		display:none;
	}
	#gridlayout>#container.vidcon {
		height:85%
	}
	#header{
		min-height:0px;
	}
}
@media only screen and (max-height: 300px){
	#gridlayout>#container.vidcon {
		height:81%
	}
	#head2 {
		display:none !important;
	}

}
@media only screen and (max-height: 240px){
	#gridlayout>#container.vidcon {
		height:78%
	}
}
@media only screen and (max-height: 190px){
	#gridlayout>#container.vidcon {
		height:75%
	}
}
@media only screen and (max-height: 160px){
	#gridlayout>#container.vidcon {
		height:70%
	}
}
@media only screen and (max-height: 120px){
	#gridlayout>#container.vidcon {
		height:70%
	}
}

#header:empty{
	display:none;
}

.la-sliders-h {
	cursor:pointer;
}

.icn-spinner {
	animation: spin-animation 3s infinite; 
	display: inline-block;
	z-index: 10;
}

#recordLocalbutton.la-spinner {
	animation: spin-animation 3s infinite; 
	display: inline-block;
}

.retry-spinner {
	border: 1vh solid #7f838666;
	border-top: 1vh solid #f0f0f066;
	border-radius: 50%;
	width: 10vh;
	height: 10vh;
	animation: spin-animation 3s infinite linear, fadeIn 5s; 
	margin: 44vh auto;
	cursor: help;
}
#retryimage{
	display: block;
	margin: auto;
	max-width: 100%;
	max-height: 100%;
	width: 100%;
	height: 100%;
	animation: fadeIn 2s; 
	object-fit: contain;
}
#retrymessage{
	display: block;
	margin: 80vh auto;
	animation: fadeIn 2s; 
	color: white;
	position: absolute;
	left: 0;
	top: 0;
	float: left;
	width: 100%;
	height: 100%;
	text-align: center;
	font-size: 2em;
}

html {
	border: 0;
	margin: 0;
	outline: 0;
}

body {
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	color: var(--gray90);
	padding: 0 0px;
	height: 100%;
	width: 100%;
	background-color: var(--background-color);
	font-family: Helvetica, Arial, sans-serif;
	border: 0;
	margin: 0;
	opacity: 1;
	transition: opacity .1s linear;
	scrollbar-color:#666 #201c29;
	display:flex;
	flex-direction: column;
	position: fixed;
}
body.darktheme{
	background-color: var(--dark-background-color);
}

::-webkit-scrollbar {
	width: 15px;
	height: 15px;
}

::-webkit-scrollbar-track {
	-webkit-box-shadow: inset 0 0 13px rgb(0 0 0 / 50%);
	border-radius: 3px;
}

::-webkit-scrollbar-thumb {
	border-radius: 5px;
	-webkit-box-shadow: inset 0 0 16px rgb(150 150 150 / 100%);
	border: solid 3px transparent;
}

.previewWebcam {
	max-width: 640px;
	max-width: 83vw;
	height: 30vh;
	opacity:1;
	animation: fadeIn 0.2s;
}

#getPermissions{
	font-size: 110%;
	border: 3px solid #99A;
	cursor: pointer;
	background-color: #cce0ff;
	margin: 20px;
	padding: 10px 50px;
	text-align:center;
}

.gowebcam {
	font-size: 110%;
	border: 3px solid #ddd;
	background-color: #f0f0f0;
	color: black;
	cursor: pointer;
	margin: 20px;
	padding: 10px 50px;
}
.gowebcam:enabled {
	background-color: #26e726 !important;
	background: radial-gradient(#26e726, #2EeF2E);
	color: black!important;
	font-weight: bold !important;
	box-shadow: 0 0 31px 0px #244e1c44;
	animation: pulsate 2s ease-out infinite; 
}
#mainmenu {
	height:100vh;
}
.mainmenuclass {
	display: inline-block;
	width:100%;
}
.welcomeOverlay{
	object-fit: cover;
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    left: 0;
    z-index: 500;
    top: 0;
	animation: fadeIn 0.1s;
	-webkit-animation: fadeIn  0.3s;
	-moz-animation: fadeIn  0.3s;
	-o-animation: fadeIn  0.3s;
	-ms-animation: fadeIn  0.3s;
	animation-iteration-count: 1;
}
div[data-action-type='toggle-group'] {
	padding: 0 10px;
}
.infoblob {
	color: white;
	width: 100%;
	padding: 20px;
	max-width: 1280px;
}

.outer {
	position: relative;
	margin: auto;
	width: 70px;
	margin-top: 0px;
	cursor: pointer;
}

.close {
	position: absolute;
	right: 20px;
	top: 20px;
	cursor: pointer;
	display: none;
	-webkit-app-region: no-drag;
}
.testtonebutton{
	margin: 0 0 0 15px;
	padding: 0px 10px 0px 10px !important;
	font-size: 84%;
	border-radius: 5px!important;
	box-shadow: 10px 8px 32px -10px #8883;
}
.testtonebutton:hover{
	background-color:#DDD
}
.testtonebutton:active{
	background-color:#AAA
}
select{
	-webkit-app-region: no-drag;
}

.highlight {
	background-color:#ddeeff;
}

#effectSelector{
	display: inline-block;
	padding:2px 0;
	min-height: 24px;
}

#passwordBasicInput{
	min-height: 24px;
}

/*https://css-tricks.com/styling-cross-browser-compatible-range-inputs-css/*/

input[type=range] {
	-webkit-appearance: none;
	margin: 18px 0;
	width: 100%;
	background-color:#0000;
}

input[type=range]:focus {
	outline: none
}

input[type=range]::-webkit-slider-runnable-track {
	width: 100%;
	height: 8.4px;
	cursor: pointer;
	box-shadow: 1px 1px 1px #000,0 0 1px #0d0d0d;
	background: #3071a9;
	border-radius: 1.3px;
	border: .2px solid #010101
}

input[type=range]:focus::-webkit-slider-runnable-track {
	background: #367ebd
}

input[type=range]::-moz-range-track {
	width: 100%;
	height: 8.4px;
	cursor: pointer;
	box-shadow: 1px 1px 1px #000,0 0 1px #0d0d0d;
	background: #3071a9;
	border-radius: 1.3px;
	border: .2px solid #010101
}

input[type=range]::-ms-track {
	width: 100%;
	height: 8.4px;
	cursor: pointer;
	background: 0 0;
	border-color: transparent;
	border-width: 16px 0;
	color: transparent
}

input[type=range]::-webkit-slider-thumb {
	box-shadow: 1px 1px 1px #000,0 0 1px #0d0d0d;
	border: 1px solid #000;
	height: 24px;
	width: 24px;
	border-radius: 3px;
	cursor: pointer;
	-webkit-appearance: none;
	margin-top: -9px;
	background-color: var(--lighttheme-2);
}

input[type=range]::-moz-range-thumb {
	box-shadow: 1px 1px 1px #000,0 0 1px #0d0d0d;
	border: 1px solid #000;
	height: 24px;
	width: 24px;
	border-radius: 3px;
	cursor: pointer;
	background-color: var(--lighttheme-2);
}

input[type=range]::-ms-thumb {
	box-shadow: 1px 1px 1px #000,0 0 1px #0d0d0d;
	border: 1px solid #000;
	height: 24px;
	width: 24px;
	border-radius: 3px;
	cursor: pointer;
	background-color: var(--lighttheme-2);
}

input[type=range]::-ms-fill-lower {
	background: #2a6495;
	border: .2px solid #010101;
	border-radius: 2.6px;
	box-shadow: 1px 1px 1px #000,0 0 1px #0d0d0d;
}

input[type=range]::-ms-fill-upper {
	background: #3071a9;
	border: .2px solid #010101;
	border-radius: 2.6px;
	box-shadow: 1px 1px 1px #000,0 0 1px #0d0d0d;
}

input[type=range]:focus::-ms-fill-lower {
	background: #3071a9
}

input[type=range]:focus::-ms-fill-upper {
	background: #367ebd
}

input[type=range].inputConstraint {
	display:block;
	width:100%;
	margin: 5px 0px 10px 0px;
}
/* ////// Icon resizing for mobile subControl bar ////// */

input[type=range].thinSlider {
	margin: 0;
	padding: 0;
	height: 10px;
	top: 5px;
	overflow: hidden;
	position: relative;
	display: flex;
}
.actionMessage {
	color: #000;
	margin: 3px;
	border-radius: 3px;
	background: #FFF;
	padding: 5px;
	text-align: left;
	margin: 10px 3px;
	border: 1px solid black;
}
.inMessage, .outMessage {
    margin-bottom: 10px;
    padding: 5px;
    border-radius: 5px;
}
.inMessage {
    background-color: #e6e6e6;
}

.outMessage {
    background-color: #c5d3ff;
    text-align: right;
}
#chatBody {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
    background-color: #4d4d4d;
    min-height: 50px;
	border-color: #6b6767;
	height: 300px;
}

#chatBody::-webkit-scrollbar {
	width: 0px;
	background: transparent; /* make scrollbar transparent */
}
.chat-input-area {
    display: flex;
    padding: 10px 10px 4px 10px;
	background-color: #2a2a2a;
}
.resizer {
    height: 5px;
    background: #2a2a2a;
    cursor: ns-resize;
    position: relative;
    overflow: hidden;
    cursor: ns-resize;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.resizer::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 2px;
    background: linear-gradient(to right, #666 20%, transparent 20%, transparent 40%, #666 40%, #666 60%, transparent 60%, transparent 80%, #666 80%);
}

.resizer:hover::before {
    background: linear-gradient(to right, #999 20%, transparent 20%, transparent 40%, #999 40%, #999 60%, transparent 60%, transparent 80%, #999 80%);
}
div#chatBody a {
	color: blue;
	text-decoration: underline;
	background-color: #c9c9c9;
	border: 2px solid black;
	padding: 2px 10px;
	border-radius: 6px;
	cursor: pointer;
}

#chatModule {
    position: fixed;
    bottom: 10px;
    right: 10px;
    width: 400px;
	max-width: 100%;
	min-width: 100px;
    background-color: #fff;
    border: 1px solid #4f4e4e;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    max-height: 100vh;
    z-index: 1000;
}
.chat-header {
    padding: 10px;
    background-color: #2a2a2a;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
	color:white;
}

#chatInput {
    flex-grow: 1;
    margin-right: 5px;
}
.chatBarInputButton {
    width: 50px;
    margin: 0 5px;
}
#callerMenu {
	position: absolute;
    left: 0;
    bottom: 200px;
}
#callerMenu button {
	font-size: 300%;
}
.foregroundMedia {
	width:100%;
	height:100%;
	position: relative;
}
.section {
    display: block;
    padding: 20px 5px;
    margin: 20px auto;
    border-radius: 20px;
    border: 2px solid #6e6e6e;
    max-width: 680px;
}

@media only screen and (max-width: 500px) {
	#subControlButtons {
		position: unset;
		min-width: unset;
	}
	#subControlButtons > div {
		min-width: 40px;
		min-height: 40px;
		margin: 4px;
	}
	#SubControlButtons > div i {
		font-size: 28px;
	}
	#hangupbutton {
		margin-left: 15px;
	}
}

@media only screen and (max-height: 500px) {

	#subControlButtons > div {
		min-width: 40px;
		min-height: 40px;
		margin: 4px;
	}
	#SubControlButtons > div i {
		font-size: 28px;
	}
	#chatModule{
		margin-bottom: 50px;
	}
}

@media only screen and (max-width: 410px) {
	#subControlButtons > div {
		min-width: 35px;
		min-height: 35px;
		border-radius: 6px;
		margin: 3px;
	}
	#SubControlButtons > div i {
		font-size: 26px;
	}
	#hangupbutton {
		margin-left: 15px!important;
	}
}

@media only screen and (max-height: 410px) {
	#subControlButtons > div {
		min-width: 35px;
		min-height: 35px;
		border-radius: 6px;
		margin: 3px;
	}
	#SubControlButtons > div i {
		font-size: 26px;
	}
	#chatModule{
		margin-bottom: 40px;
	}

}

@media only screen and (max-width: 360px) {
	#subControlButtons > div {
		min-width: 30px;
		min-height: 30px;
		border-radius: 4px;
		margin: 3px;
	}
	#SubControlButtons > div i {
		font-size: 20px;
	}
	#hangupbutton {
		margin-left: unset;
	}
}

@media only screen and (max-height: 360px) {
	#subControlButtons > div {
		min-width: 30px;
		min-height: 30px;
		border-radius: 4px;
		margin: 3px;
	}
	#SubControlButtons > div i {
		font-size: 20px;
	}
	#chatModule {
		margin-bottom: 37px;
	}
}

@media only screen and (max-width: 320px) {
	#subControlButtons > div {
		min-width: 28px;
		min-height: 28px;
		border-radius: 4px;
		margin: 3px;
	}
	#SubControlButtons > div i {
		font-size: 18px;
	}
}

@media only screen and (max-width: 280px) {
	#subControlButtons > div {
		min-width: 25px;
		min-height: 25px;
		border-radius: 4px;
		margin: 3px;
	}
	#SubControlButtons > div i {
		font-size: 16px;
	}
}

/* //////////////////////////////////// */

@media only screen and (max-height: 650px) {
	body {
		font-size: 0.9em;
	}
	.gowebcam {
		padding: 5px;
		margin: 5px;
	}
	.infoblob {
		color: white;
		width: 100%;
		padding: 20px;
		max-width: 1280px;
	}
	#qrcode img {
		max-height: 150px;
	}
	.outer {
		width: 50px;
	}
	.close {
		top: 0px;
		right: 0px;
	}
}

@media only screen and (max-width: 1220px) {
	#fakeguest4{
		display: none!important;
	}
	#fakeguestinfo{
		display: none!important;
	}
}
@media only screen and (max-width: 933px) {
	#fakeguest3{
		display: none!important;
	}
}
@media only screen and (max-width: 700px) {
	#fakeguest2{
		display: none!important;
	}
	#chatModule {
		margin-bottom: 5px !important;
	}
}
@media only screen and (max-width: 292px) {
	#fakeguest1{
		display: none!important;
	}
}

@media screen and (max-width: 768px) {
	#popOutChat{
		display: none;
	}
}

@media only screen and (max-width: 650px) {

	.mainmenuclass {
		display: inline-block;
	}
	.outer {
		width: 50px;
	}
	.close {
		top: 0;
		right: 0;
	}
	select {
		font-size: 120%;
	}
	#reshare {
		max-width: 650px !important;
		font-size: 96% !important;
		width: 300px !important;
	}
	.fa-paperclip {
		display: none;
	}
	#copythisurl {
		color: #DDD;
		display: inline-block;
		font-size: 75% !important;
	}
	#logoname {
		font-size: 100%;
	}
	.column {
		float: none !important;
		padding: 15px 10px 1px 10px !important;
	}
	div.multiselect, .videoMenu, #videoSettings {
		max-width: 100% !important;
		min-width: 100% !important;
	}
	#addPasswordBasic, #headphonesDiv, #effectsDiv, #effectsDiv3, #headphonesDiv3  {
		max-width: 100% !important;
		min-width: 100% !important;
		overflow: hidden !important;
	}
	#outputSource {
		width: 100% !important;
	}
	#outputSource3 {
		width: 100% !important;
	}
	#audioSourceScreenshare, #videoSettings2 {
		max-width: 90% !important;
		min-width: 90% !important;
		overflow: hidden !important;
	}
	.popupSelector_constraints{
		margin:25px 15% 0 1%;
	}
	.mobileHide{
		display:none !important;
	}
	#effectSelector {
		max-width: 100%;
		width: 100%;
		margin: 4px 0 0 0;
	}
}

@media only screen and (max-height: 355px) {

	.popupSelector_constraints{
		margin:20px 12% 0 2%;
	}
}
#popupSelector_user_settings{
	margin-top: 10px;
}

.tooltip {
	position: relative;
	display: inline-block;
	border-bottom: 1px dotted black;
}
.tooltip .tooltiptext {
	visibility: hidden;
	width: 10em;
	background-color: #9d5050;
	color: #fff;
	text-align: center;
	border-radius: 10px;
	position: absolute;
	z-index: 1;
	top: -10px;
	font-family: "Lato", sans-serif;
}
.tooltip:hover .tooltiptext {
	visibility: visible;
}

#previewWebcam.miconly {
	display:none;
}

.notmain > .mainonly {
	display:none!important;
}

#audioSourceScreenshare {
	overflow: auto;
	resize: both;
	width:100%;
}

#outputSourceScreenshare {
	width:100%;
}
#outputSource {
	margin-top: 7px;
	padding:2px 0;
	min-height: 24px;
}
/* #audioSourceScreenshare {
	display:block;
	height: 60px;
	width: 100%;
	overflow: auto;
	padding: 5px;
	resize: both;
	border: solid 1px #AAA;
	border-radius: 4px;
} */

/* #headphonesDiv2{
	min-width: 350px;
	display: none;
	padding: 4px 10px 10px 10px;
	vertical-align: middle;
	margin: 10px 0;
	text-align: left;
} */
/* #audioScreenShare1 > i {
	display: inline-block;
}

#audioScreenShare1 > span {
	margin: 7px 0px;
	text-align: left;
	display: inline-block;
} */

h2 {
	color: white;
	-webkit-user-select: none;
	/* Safari */
	-moz-user-select: none;
	/* Firefox */
	-ms-user-select: none;
	/* IE10+/Edge */
	user-select: none;
	/* Standard */
}

.inner {
	width: inherit;
	text-align: center;
}

.labelclass {
	opacity: 0;
	font-size: 1.1em;
	line-height: 4em;
	text-transform: uppercase;
	transition: all .3s ease-in;
	cursor: pointer;
}

label {
	color: #000;
	-webkit-user-select: none; /* Safari */
	-ms-user-select: none; /* IE 10 and IE 11 */
	user-select: none;
}

.darktheme .inner:before,
.darktheme .inner:after {
	background: var(--discord-text);
}

.inner:before,
.inner:after {
	position: absolute;
	content: '';
	height: 7px;
	width: inherit;
	background: #000;
	left: 0;
	font-weight: bold;
	transition: all .3s ease-in;
}

.inner:before {
	top: 50%;
	transform: rotate(45deg);
}

.inner:after {
	bottom: 50%;
	transform: rotate(-45deg);
}

.outer:hover .labelclass {
	opacity: 1;
}

.outer:hover .inner:before,
.outer:hover .inner:after {
	transform: rotate(0);
}

.outer:hover .inner:before {
	top: 0;
}

.outer:hover .inner:after {
	bottom: 0;
}
.microphoneBackground{
	background-image: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M 13 4 C 11.90625 4 11 4.90625 11 6 L 11 18 C 11 19.09375 11.90625 20 13 20 L 19 20 C 20.09375 20 21 19.09375 21 18 L 21 6 C 21 4.90625 20.09375 4 19 4 Z M 13 6 L 19 6 L 19 18 L 13 18 Z M 7 14 L 7 18 C 7 21.300781 9.699219 24 13 24 L 15 24 L 15 26 L 11 26 L 11 28 L 21 28 L 21 26 L 17 26 L 17 24 L 19 24 C 22.300781 24 25 21.300781 25 18 L 25 14 L 23 14 L 23 18 C 23 20.21875 21.21875 22 19 22 L 13 22 C 10.78125 22 9 20.21875 9 18 L 9 14 Z'/%3E%3C/svg%3E")!important;
}

#dropButton{
	font-size: 2em;
	display: block;
	margin: auto;
	background-color: #5555;
	border-radius: 30px;
	cursor:pointer;
	color: #636363;
	padding: 3px;
	width: 100px;
}
.fullcolumn {
	float: left;
	display: inline-block;
	margin: 0 auto;
	width: 100%;
	text-align: center;
}

/* .card {
	box-shadow: 0 4px 8px 0 rgba(0, 0, 0, .1);
	background-color: #ddd;
} */

.column {
	display: inline-block;
	margin: 1.8%;
	min-width: 300px;
	width: 20%;
	padding: 25px;
	height: 200px;
	text-align: center;
	font-size: 100%;
	transition: box-shadow 0.1s ease-in-out;
	border-radius: 12px;
}

.column:hover {
	box-shadow: 0 8px 16px 0 rgba(0, 0, 0, .3);
	transform: translateY(-1px);
}

.column:active {
	box-shadow: 0 8px 16px 0 rgba(0, 0, 0, .5);
}

.column>h2 {
	color: black;
}

.graphSection {
	display: flex;
	flex-direction: column;
	max-width: 50%;
	gap: 5px;
}
.darktheme .graphSection > span {
	color: var(--discord-text)
}
.graphSection > span {
	display: block;
	font-size: 10px;
	max-height: 50px;
	min-height: 20px;
}
.graphSection>span:last-child{
	margin-bottom: 0px;
}
span[data-action-type="stats-graphs-details-container"]>span{
	padding: 1px;
	display:block;
}

#empty-container {
	display: inline-block;
	width: 20%;
	min-width: 300px;
	padding: 28px;
	height: 200px;
	margin: 1.8%;
	text-align: center;
}
#container-1 {
	background-repeat: no-repeat;
	background-size: 80px;
	background-position: 50% 65%;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='342.728' height='325.878' viewBox='0 0 90.68 86.222' fill='none' stroke='%23000' stroke-width='5.6' stroke-linejoin='round' stroke-dashoffset='22.7149601' xmlns:v='https://vecta.io/nano'%3E%3Cpath d='M3.15 3.15h37.378v35.24H3.15zm47.002 0H87.53v35.24H50.152zM3.15 47.832h37.378v35.24H3.15zm47.002 0H87.53v35.24H50.152z'/%3E%3C/svg%3E");
}
#container-2 {
	background-repeat: no-repeat;
	background-size: 80px;
	background-position: 50% 65%;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='347.569' height='278.797' viewBox='0 0 91.961 73.765' fill='none' stroke='%23000' xmlns:v='https://vecta.io/nano'%3E%3Cpath d='M3.02 3.02h85.921v54.399H3.02z' stroke-width='6.04' stroke-linejoin='round' stroke-dashoffset='22.7149601'/%3E%3Cg stroke-width='6.3'%3E%3Cpath d='M35.607 70.527l21.839.071' stroke-linecap='round' paint-order='markers fill stroke'/%3E%3Cpath d='M46.404 73.517l.142-15.596' paint-order='markers fill stroke'/%3E%3C/g%3E%3C/svg%3E");
}
.mainScreenShareButton {
	padding: 18px;
    font-size: 120%;
    margin: 16px;
    animation: pulsate 2s ease-out infinite;
	background-color: #26e726 !important;
    background: radial-gradient(#26e726, #2EeF2E);
    box-shadow: 1px 1px 1px;
	color: black !important;
	background-color: #26e726 !important;
    background: radial-gradient(#26e726, #2EeF2E);
	font-weight: bold !important;
    box-shadow: 0 0 31px 0px #244e1c44;
    animation: pulsate 2s ease-out infinite;
}
.screenshare-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    color: #fff;
}

.screenshare-header {
    text-align: center;
    margin-bottom: 30px;
}

.screenshare-icon {
    width: 240px;
    height: 160px;
    margin: 0 auto 20px;
    display: block;
	box-shadow: 0 0 0 0 !important;
}

.select-screen-btn {
    background: #2a2a2a;
    color: #fff;
    border: 1px solid #666;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    margin-bottom: 15px;
    cursor: pointer;
    transition: background 0.2s;
}

.select-screen-btn:hover {
    background: #3a3a3a;
}

.quality-toggle {
    background: transparent;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.2s;
}

.quality-toggle:hover {
    background: rgba(255,255,255,0.1);
}

.audio-section {
    margin-top: 20px;
    padding: 15px;
    background: rgba(0,0,0,0.2);
    border-radius: 8px;
}

.info-box {
    margin-top: 20px;
    padding: 15px;
    background: rgba(255,255,255,0.05);
    border-radius: 8px;
    font-size: 14px;
	display: inline-block;
	text-align: left;
}

.info-box h3 {
    margin-top: 0;
    color: #ccc;
    font-size: 16px;
}

.info-box ul {
    margin: 10px 0;
    padding-left: 20px;
    color: #aaa;
}

.info-box li {
    margin: 8px 0;
}
.info-box li:empty {
    display:none;
}
.audio-device-list {
    margin-top: 15px;
    padding: 10px;
    background: rgba(0,0,0,0.2);
    border-radius: 6px;
}

.audio-device-item {
    padding: 5px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.audio-device-item:hover {
    background: rgba(255,255,255,0.05);
    border-radius: 4px;
}

.audio-device-item label {
	margin-left: 8px;
	cursor: pointer;
}

.audio-select-btn {
	width: 100%;
	padding: 8px;
	margin: 10px 0;
	background: none;
	border: 1px solid currentColor;
	border-radius: 4px;
	cursor: pointer;
}

.audio-select-btn:hover {
	background: rgba(255, 255, 255, 0.1);
}

.error-message {
    margin-top: 10px;
    padding: 10px;
    background: rgba(255,0,0,0.1);
    border: 1px solid rgba(255,0,0,0.2);
    border-radius: 4px;
    color: #ff9999;
}

#container-3 {
	background-repeat: no-repeat;
	background-size: 90px;
	transition: background-image 0.3s ease-in-out;
	-webkit-transition: background-image 0.3s ease-in-out;
	background-position: 50% 65%;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='347.184' height='187.007' viewBox='0 0 91.859 49.479' fill='none' stroke='%23000' stroke-width='5' stroke-linejoin='round' xmlns:v='https://vecta.io/nano'%3E%3Cpath d='M3.15 3.15h65.569v43.179H3.15z' stroke-dashoffset='22.7149601' paint-order='markers fill stroke'/%3E%3Cpath d='M68.919 28.837L88.709 44.73V7.148L69.019 22.341z'/%3E%3C/svg%3E");
}
#container-3a {
	background-repeat: no-repeat;
	background-size: 90px;
	transition: background-image 0.3s ease-in-out;
	-webkit-transition: background-image 0.3s ease-in-out;
	background-position: 50% 65%;
}
#container-4 {
	background-repeat: no-repeat;
	background-size: 80px;
	background-position: 50% 65%;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='303.594' height='274.946' viewBox='0 0 80.326 72.746' fill='none' stroke='%23000' stroke-width='4.6' stroke-linejoin='round' stroke-dashoffset='6.01000023' xmlns:v='https://vecta.io/nano'%3E%3Cpath d='M2 51.27L78.326 2l-8.03 63.359-37.093-12.414z'/%3E%3Cpath d='M33.047 70.746l.157-17.802L78.326 2 33.203 52.944l10.314 3.39z'/%3E%3C/svg%3E");
}
#container-5 {
	background-repeat: no-repeat;
	background-size: 80px;
	background-position: 50% 65%;
	background-image: url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCAxMjkgMTI5IiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCAxMjkgMTI5IiB3aWR0aD0iNTEycHgiIGhlaWdodD0iNTEycHgiPgogIDxnPgogICAgPGc+CiAgICAgIDxwYXRoIGQ9Im0xOC43LDEyMi41aDkxLjZjMi4zLDAgNC4xLTEuOCA0LjEtNC4xdi0xMDcuOWMwLTIuMy0xLjgtNC4xLTQuMS00LjFoLTY4LjdjLTAuMywwLTAuNywwLTEsMC4xLTAuMSwwLTAuMiwwLjEtMC4yLDAuMS0wLjMsMC4xLTAuNSwwLjItMC44LDAuMy0wLjEsMC4xLTAuMiwwLjEtMC4zLDAuMi0wLjMsMC4yLTAuNiwwLjQtMC44LDAuN2wtMjIuOSwyN2MtMC4zLDAuMy0wLjUsMC43LTAuNywxLjEtMC4xLDAuMS0wLjEsMC4zLTAuMSwwLjQtMC4xLDAuMy0wLjEsMC42LTAuMiwwLjkgMCwwLjEgMCwwLjEgMCwwLjJ2ODAuOWMtMS4wNjU4MWUtMTQsMi40IDEuOSw0LjIgNC4xLDQuMnptMTguOC0xMDAuOHYxMS44aC0xMGwxMC0xMS44em0tMTQuNywxOS45aDE4LjhjMi4zLDAgNC4xLTEuOCA0LjEtNC4xdi0yMi45aDYwLjV2OTkuN2gtODMuNHYtNzIuN3oiIGZpbGw9IiMwMDAwMDAiLz4KICAgICAgPHBhdGggZD0ibTk0LDUwLjVoLTU5Yy0yLjMsMC00LjEsMS44LTQuMSw0LjEgMCwyLjMgMS44LDQuMSA0LjEsNC4xaDU5YzIuMywwIDQuMS0xLjggNC4xLTQuMSAwLTIuMy0xLjgtNC4xLTQuMS00LjF6IiBmaWxsPSIjMDAwMDAwIi8+CiAgICAgIDxwYXRoIGQ9Im05NCw3MC4zaC01OWMtMi4zLDAtNC4xLDEuOC00LjEsNC4xIDAsMi4zIDEuOCw0LjEgNC4xLDQuMWg1OWMyLjMsMCA0LjEtMS44IDQuMS00LjEgMC0yLjItMS44LTQuMS00LjEtNC4xeiIgZmlsbD0iIzAwMDAwMCIvPgogICAgPC9nPgogIDwvZz4KPC9zdmc+Cg==)
}
#container-5 input[type="file"] {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  position: absolute;
  z-index: -1;
}

#container-5 input[type="file"] + label {
  display: inline-block;
  padding: 12px 24px;
  background: var(--accent-color, #4a90e2);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  margin: 1rem 0;
  font-weight: 500;
  transition: background 0.2s ease;
}

#container-5 input[type="file"] + label:hover {
  background: var(--accent-hover-color, #357abd);
}

 input[type="file"]#fileselector4 + label:hover {
  background: var(--accent-hover-color, #357abd);
}
input[type="file"]#fileselector4  {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  position: absolute;
  z-index: -1;
}

input[type="file"]#fileselector4 + label {
  display: inline-block;
  padding: 12px 24px;
  background: var(--accent-color, #4a90e2);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  margin: 1rem 0;
  font-weight: 500;
  transition: background 0.2s ease;
}


.file-manager {
    max-width: 400px;
    background: #ddda;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 16px;
    margin: 10px;
    transition: all 0.3s ease;
}

.file-manager.minimized {
    max-width: 300px;
    padding: 8px;
	min-height: 40px;
}

.file-manager.minimized .file-list,
.file-manager.minimized .file-upload-zone {
    display: none;
}

#activeShares .file-list {
    max-height: 400px;
    overflow-y: auto;
}

#activeShares .header-controls {
    display: flex;
    gap: 8px;
}

#activeShares .file-manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
	gap: 12px;
}

#activeShares .file-upload-zone {
    border: 2px dashed #ccc;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    margin-bottom: 16px;
    cursor: pointer;
    transition: border-color 0.3s;
}

#activeShares .file-upload-zone:hover {
    border-color: #666;
}

#activeShares .file-list {
    max-height: 400px;
    overflow-y: auto;
}

#activeShares .file-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 12px;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #eee;
}

#activeShares .file-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

#activeShares .file-name {
    font-weight: 500;
}

#activeShares  .file-size {
    color: #666;
    font-size: 0.9em;
}

#activeShares .download-info {
    font-size: 0.85em;
    color: #666;
    margin-top: 4px;
}

#activeShares .file-size {
    color: #666;
    font-size: 0.9em;
}

#activeShares .file-progress {
    position: relative;
    height: 4px;
    background: #eee;
    border-radius: 2px;
    overflow: hidden;
    margin: 4px 0;
}

#activeShares .progress-bar {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background: #4CAF50;
    transition: width 0.3s;
}

#activeShares .progress-text {
    font-size: 0.9em;
    color: #666;
}

#activeShares .transfer-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

#activeShares .no-files {
    text-align: center;
    color: #666;
    padding: 20px;
}

#activeShares .transfer-item {
    background: #f5f5f5;
    padding: 8px;
    border-radius: 4px;
    margin-top: 4px;
}

#activeShares .transfer-speed {
    font-size: 0.9em;
    color: #666;
}

#activeShares .button-primary {
    background: #4CAF50;
    color: white;
}

#activeShares .button {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background 0.3s;
}

#activeShares .button-primary {
    background: #4CAF50;
    color: white;
}

#activeShares .button-secondary {
    background: #666;
    color: white;
}

#activeShares .button-danger {
    background: #f44336;
    color: white;
}

#activeShares .button:hover {
    opacity: 0.9;
}

.controlVideoBox {
	position:relative;
}

.canvasStats{
	background-color: black;
	width: 100%;
	height: 50px;
	border-radius: 4px;
}
.manualInput{
	width: 75px;
	display: inline-block;
	overflow: scroll;
	margin: 4px 0 4px 4px;
	padding: 1px 4px;
	color:black;
}

#add_screen {
	padding-bottom: 20px;
}
.soloButton{
	display: flex;
	flex-wrap: wrap;
	font-size: 0.7em;
}
.soloButton button {
	margin: 5px 0px 0px 0px;
	box-shadow: 1px 1px 3px rgba(0,0,0,0.75);
	background-color: #ecfaff;
	border-radius: 2px;
	width:100%;
}

.lowerRaisedHand{
	margin: auto;
	margin-top: 5px;
	margin-left: 5px;
	margin-bottom: 5px;
	background-color: yellow!important;
	width: 100%;
	color:black!important;
}

.removeFromQueue{
	margin: auto;
	margin-top: 5px;
	margin-left: 5px;
	margin-bottom: 5px;
	background-color: #ff00b8!important;
	width: 100%
}

.float {
	opacity: 0.8;
	min-width: 45px;
	min-height: 45px;
	height: 100%;
	box-shadow: 1px 1px 3px rgba(0,0,0,0.75);
	color: #FFF;
	border-radius: 8px;
	text-align: center;
	margin: 5px;
	pointer-events: auto;
	outline:none;
	padding: 5px 5px;
}
.float2 {
	opacity: 0.8;
	min-width: 45px;
	min-height: 45px;
	height: 100%;
	background-color: #8888;
	box-shadow: 1px 1px 3px rgba(0,0,0,0.75);
	color: #FFF;
	border-radius: 38px;
	text-align: center;
	z-index: 10;
	margin: 5px;
	pointer-events: auto;
	outline:none;
	padding: 5px 5px;
}

.rotate225 {
	transform: rotate(135deg);
	position: relative;
	top: 1px;
}

.controlVideoBox video[data-rotated='90']{
	transform: rotate(90deg) translate(9px,-80px)  !important;
	height: 260px;
	width:80px;
}
.controlVideoBox video[data-rotated='180']{
	transform: rotate(180deg) !important;
}
.controlVideoBox video[data-rotated='270']{
	transform: rotate(270deg) translate(-9px,80px) !important;
	height: 260px;
	width:80px;
}

#previewWebcam.rotate{
	max-width: 30vh;
}
#previewWebcam.rotate{
	max-width: 30vh;
}

.myVideo {
	box-shadow: rgb(88, 88, 88) 0px 0px 5px 1px;
	width: var(--myvideo-width);
	max-width: 800px !important;
	max-height: 100% !important;
	height: var(--myvideo-height) !important;
	display: block !important;
	margin: auto auto !important;
	position: relative !important;
	top: 50% !important;
	background-color: var(--myvideo-background);
	object-fit: var(--fit-style);
	max-width: var(--myvideo-max-width) !important;
}
#calendarButton {
	cursor: pointer;
	z-index: 1;
	display:none;
}
#calendar a {
	display: block;
	margin:10px;
}
#translateButton {
	cursor: pointer;
	z-index: 1;
}
#helpButton {
	cursor: pointer;
	z-index: 1;
}
.controlsGrid button[data-action-type="solo-video"]>i{
	color: #b3b300;
}
iframe {
	z-index: 2;
}

#mutebutton.bigbutton {
	bottom: 100px;
	padding: 100px; 
	position: fixed;
	display: block;
	box-sizing: unset;
}
.bigbutton #mutetoggle {
	bottom: 20px;
	right: 0;
	top: unset;
}
.bigbutton .bigbuttontext {
	border-radius: 0;
	margin: 3px;
	display:block;
	font-size:200%;
}

@media only screen and (max-height: 600px) {
	.bigbutton {
		padding: 80px;
	}
}
@media only screen and (max-width: 600px) {
	.bigbutton {
		padding: 80px;
	}
}
@media only screen and (max-height: 500px) {
	.bigbutton {
		bottom: 80px;
		padding: 70px;
	}
}
@media only screen and (max-width: 500px) {
	.bigbutton {
		padding: 70px;
	}
}
@media only screen and (max-height: 400px) {
	.bigbutton {
		padding: 60px;
		bottom: 50px;
	}
}
@media only screen and (max-width: 300px) {
	.bigbutton {
		padding: 60px;
	}
}
@media only screen and (max-height: 300px) {
	.bigbutton {
		bottom: 40px;
		padding: 50px;
	}
}

@media only screen and (max-width: 390px) {
	#translateButton {
		display:none;
	}
	#helpButton {
		display:none;
	}
}

.popup .menu { margin: 2px; }

.toggleSize {
	font-size: 32px;
	color: white;
}

img {
	max-width: 100%;
}

/* In-animation, out-animation, and skip-animation are used for the card/buttons on the home page */
.in-animation {
	animation: inlightbox 0.5s forwards; /* @keyframes found in animations.css */
	position: fixed !important;
	margin: 0 !important;
}
.out-animation {
	animation: outlightbox .5s forwards; /* the @keyframes for this animation are dynamically created in lib.js */
}

.skip-animation {
	position: fixed !important;
	margin: 0 !important;
	height: 100%;
	width: 100%;
	top: 0;
	left: 0;
}
.skip-animation .container-inner{
	display:block;
}


.pointer {
	cursor: pointer;
}
.modal {
	display: none;
	position: fixed;
	padding-top: 50px;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: rgb(0, 0, 0);
	background-color: rgba(0, 0, 0, 0.5);
}
.modal-content {
	position: relative;
	background-color: white;
	padding: 20px;
	margin: auto;
	max-width: 400px;
}
.close-btn {
	float: right;
	color: lightgray;
	font-size: 24px;
	font-weight: bold;
}
.close-btn:hover {
	color: darkgray;
}
#chattoggle{
	top: 0.5px;
	position: relative;
}

.la-phone {
	color: red;
	top:0.5;
}

#obsState {
	border:#888 solid 2px;
	padding:2px 5px;
	color: white;
	z-index:2;
	margin:0 auto;
	background-color: #222D;
	display: inline-block;
	opacity: 0.8;
	border-radius: 4px;
	text-align: center;
	
    position: absolute;
    top: 0;
	left: 50%;
    transform: translateX(-50%);
	transform-origin: top center;
	
}
#obsState.larger {
	padding:2px 10px;
	font-size: 30px;
}

@media only screen and (max-height: 400px){
	#obsState {
		transform: translateX(-50%) scale(0.8);
	}
}
@media only screen and (max-height: 300px){
	#obsState {
		transform: translateX(-50%) scale(0.7);
	}
}
@media only screen and (max-width: 620px){
	#obsState {
		top:20px;
		transform: translateX(-50%) scale(0.7);
	}
}
@media only screen and (max-height: 200px){
	#obsState {
		transform: translateX(-50%) scale(0.6);
	}
}

@media only screen and (max-width: 400px){
	#obsState {
		top:30px;
		transform: translateX(-50%) scale(0.6);
		display:none!important;
		opacity:0;
	}
}
@media only screen and (max-width: 300px){
	#obsState {
		display:none!important;
		opacity:0;
	}
}

#obsState.noheader{
	top:0px;
}
.onair {
	box-shadow: inset 0 0 max(10vw, 10vh) green;
}

.ondeck {
	display: block !important;
	box-shadow: inset 0 0 max(10vw, 10vh) yellow;
	color: black!important;
}

.recording{
	box-shadow: inset 0 0 max(10vw, 10vh) red
}

.raisedHand{
	background-color: #DD1A !important;
}
#request_info_prompt{
	z-index: 20;
	color: white;
	font-size: 30px;
	font-size: 3.5vw;
	top: 0;
	align-self: center;
	margin: 25vh 0;
	position: absolute;
}

.holder {
	position: relative;
	width: 100%;
	height: 100%;
	max-width: 100%;
	max-height: 100%;
	object-fit: contain;
	overflow:hidden;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: var(--video-margin);
	border-radius: var(--video-rounded);
	border-width: var(--video-border);
	border-color: var(--video-border-color);
	background-color: var(--video-holder-color);
	border-style: solid;
}

video {
	transition: opacity .25s ease-in-out;
	-moz-transition: opacity .25s ease-in-out;
	-webkit-transition: opacity .25s ease-in-out;
	pointer-events: auto;
	background-color: transparent;
	border: 0;
	margin: 0;
	user-select:none;
	-webkit-user-select: none; /* Chrome/Safari */
	-moz-user-select: none; /* Firefox */
	-ms-user-select: none; /* IE10+ */
	-webkit-tap-highlight-color:transparent;
	outline-style:none;
	border-style:solid;
	background-size: var(--video-background-image-size);
	background-repeat: no-repeat;
	background-position: center;
	background-image: var(--video-background-image);

}
video[data-speaking="0"] {
	transition: opacity .25s ease-in-out, background-size 0.5s ease;
}
video[data-speaking="1"] {
	background-image: var(--video-background-image-talking, var(--video-background-image));
	background-size: var(--video-background-image-size-talking, var(--video-background-image-size));
	transition: background-size 0.5s ease;
}
video[data-speaking="2"] {
	background-image: var(--video-background-image-screaming, var(--video-background-image));
	background-size: var(--video-background-image-size-screaming, var(--video-background-image-size));
	transition: background-size 0.5s ease;
}

.nogb { background-image: unset !important }

video::-webkit-media-controls-timeline {
	display: none;
}

video::-webkit-media-controls-fullscreen-button {
	display: var(--full-screen-button);
}

video::-webkit-media-controls-timeline-container {
	display: none;
}

audio::-webkit-media-controls-overlay-play-button, video::-webkit-media-controls-overlay-play-button {
	display: none;
}

audio::-webkit-media-controls-play-button, video::-webkit-media-controls-play-button {
	display: none;
}

video::-webkit-media-controls-toggle-closed-captions-button {
	display: none;
}

video.clean::-webkit-media-controls-current-time-display {
	display: inherit;
}

video.clean::-webkit-media-controls-time-remaining-display {
	display: inherit;
}

video.clean::-webkit-media-controls-timeline {
	display: inherit;
}

video.clean::-webkit-media-controls-timeline-container {
	display: inherit;
}

audio.fileshare::-webkit-media-controls-overlay-play-button, video.fileshare::-webkit-media-controls-overlay-play-button {
	display: inherit;
}

audio.fileshare::-webkit-media-controls-play-button, video.fileshare::-webkit-media-controls-play-button {
	display: inherit;
}

video::-webkit-media-controls-panel {
	background-color: #0000;
}

#main.forcecontrols video::-webkit-media-controls-panel {
	opacity: 1 !important;
    visibility: visible !important;
	display: flex !important;
}

.mirrorControl::-webkit-media-controls-enclosure {
	padding: 0px;
	height: 30px;
	transform: scaleX(-1);
	-webkit-transform: scaleX(-1);
}
.popup-screen {
	text-align: center;
	position: absolute;
	display:none;
	top:0;
	left:0;
	z-index: 7 !important;
	padding: 20px;
	margin:15px 15px 80px 15px;
	width: 80vh !important;
	height: 80vh !important;
	background-color: #ccc !important;
	border: solid 1px #dfdfdf !important;
	box-shadow: 1px 1px 2px #cfcfcf !important;
}
.context-menu {
	display: none;
	position: absolute;
	z-index: 996 !important;
	padding: 12px 0 !important;
	width: 240px !important;
	background-color: #fff !important;
	border: solid 1px #dfdfdf !important;
	box-shadow: 1px 1px 2px #cfcfcf !important;
}

.darktheme .popup-message {
	background-color: var(--discord-grey-1);
	border-color: var(--discord-grey-3);
	color: var(--discord-text);
	box-shadow: 1px 1px 3px black;
}

.popup-message {
	display: none;
	text-align: center;
	position: absolute;
	z-index: 35;
	padding: 5px;
	border-radius: 4px;
	min-width: 180px;
	background-color: #fff;
	border: solid 1px #dfdfdf;
	box-shadow: 1px 1px 2px #cfcfcf;
}

.darktheme .popup-message ul {
	list-style: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAALCAYAAACtWacbAAAAr0lEQVQYlY3QMUoDUBAE0KciNtpI+hQ5gdhZ5QIewwN4EisJpM0RhJRiZSt4A8EynWlCig0j+wttdODzYXZndhj/QlX9frOqeq6q96paZ3aMNW7b8AorTPCBRcijKJrI4gsuscXNSBKnDaZ4xQX2OGtBnL+X7lp5jhNc9x/HR8xybmR5aKeBXQs3cQreMO/BKb46U7JOxlJCP7Uyp++bX+JzdJQ+0kv6SU8/uvsbOACH0VkbmsdQwQAAAABJRU5ErkJggg==);
}

.popup-message ul {
	list-style: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAJCAYAAADgkQYQAAAA/ElEQVQYlTWPwUrEMBCGJ2mUNKZxIbRQ2oPeRT34OH2HPfQ5eugzeRG8rOLdvRVagm5Jm+DSILO0c5kZZub/vyF1XcMWTdNgda2UeqSUSmvt736/P7C2bZ9DCN8hhBMAxEqpJ601H4bhJ4RwxCsqpdwJIe6wieP4Icsy3nWdt9Z+rIdAlFIvWmuJAynlVRRFsCwLTNPknXNfAODoOI6fxhhfliUnhNCqql4xF0Vxg9bISFbmGK2EEJwxdlGy1p7zPOfGGEvX75xz7n2apnOSJBFaee/fEB6/ZBd6Sm8RPk1T3vf9xgLzPB8ppWRbumeM7VAaGQHgb3U4AcDhH7U6eiFtegoRAAAAAElFTkSuQmCC');
	margin-left: 14px;
	padding: 5px;
}

.popup-message li {
	text-align: left;
	padding-left: unset;
	line-height: unset;
	margin: 0 0 0 18px;
}

.context-menu--active {
	display: block !important;
}
.context-menu__items {
	list-style: none !important;
	margin: 0;
	padding: 0;
}
.context-menu__item {
	display: block;
	margin-bottom: 4px !important;
}
.context-menu__item:last-child {
	margin-bottom: 0 !important;
}
.context-menu__link {
	display: block;
	padding: 4px 12px;
	color: #0066aa !important;
	text-decoration: none;
}
.context-menu__link:hover {
	color: #fff !important;
	background-color: #0066aa !important;
}
.context-menu__tip {
	margin-left: 15px;
	color: #777;
	margin-top: 10px;
	padding-top: 10px;
	position: relative;
	top: 7px;
}
#bufferSliderValue{
	margin-left:10px;
}
.context-menu__link:hover > #bufferSliderValue {
	color: #fff;
}
.selectedContentEffectsImage{
	box-shadow: 0 0 10px #2c3554;
	outline: 2px solid black;
}

.multiselect .multiselect-trigger:hover {
	cursor: pointer;
	cursor: hand;
	text-decoration: none;
}
/* .multiselect .multiselect-trigger.open {
	border-bottom: 0;
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0;
}
.multiselect .multiselect-trigger.closed {
	border-bottom: 1px solid #ccc;
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
} */

span[data-resolution]:hover, span[data-bitrate]:hover {
	color:lightblue;
	text-decoration: underline;
}
.gear_microphone{
	user-select: none;
	float: right;
	height: 0;
	padding: 0;
	top: 6px;
	position: relative;
}
.gear_microphone.gearflat{
	top: -1px;
}
.gear_microphone>input{
	top: 1px;
	position: relative;
}
#micStereoMonoInput3{
	width: 10px;
	height: 11px;
}
#headphonesDiv3 {
	text-align: left;
	margin: 17px 0 0 0;
	width: 463px;
	padding: 10px 10px;
	vertical-align: middle;
}
#headphonesDiv {
	width: 463px;
	padding: 4px;
}
.selected {
	border: solid 3px black;
	padding: 4px;
}
#audioMenu {
	margin: 15px 0 0 0;
}

#minipreview > #videosource {
	height:auto!important;
	width:auto!important;
	max-height:100%!important;
	max-width:100%!important;
	border-radius: 0!important;
}

#videoSource3 {
	width: calc(100% - 50px);
}

#videoSourceSelect {
	padding:2px 0;
	min-height: 24px;
}
#gear_webcam{
	cursor: pointer;
	display: inline-block;
	padding: 0 0 0 3px;
}
.gone {
	position:absolute;
	top: -150px;
}
.grabLinks {
	display: inline-flex;
	cursor: grab;
	font-weight: bold;
	font-size: 1em;
	padding: 10px;
	margin: 5px 0;
	word-break: break-all;
}
.grabLinks a:hover {
	color: black !important;
}
.grabLinks a:active {
	color: black !important;
}
.grabLinks a:link {
	color: black !important;
}

.hidden {
	display: none !important;
	visibility: hidden;
	width: 0px;
	height: 0px;
	opacity: 0;
}
.hidden2 {
	display: none !important;
	visibility: hidden;
	width: 0px;
	height: 0px;
	opacity: 0;
}

.grabLinks a:visited {
	color: black !important;
}

.permahide {
	display: none!important;
	visibility: hidden;
	width:0px;
	height:0px;
	opacity: 0;
	background: #0000;
	color: #0000;
	font-size: 0em;
	pointer-events:none;
}

.multiselect .multiselect-contents {
	display: block;
	margin: 0;
	font-size: 95%;
	padding: 2px 3px 0px;
	border-top: 0;
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
	text-align: left;
}

.multiselect .multiselect-contents li {
	list-style: none;
	overflow: hidden;
}
.select .select-trigger:hover {
	cursor: pointer;
	cursor: hand;
	text-decoration: none;
}
.select .select-trigger.open {
	border-bottom: 0;
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0;
}
.select .select-trigger.closed {
	border-bottom: 1px solid #ccc;
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
}
.select .select-contents {
	display: none;
	margin: 0;
	padding: 0 24px 24px;
	border: 1px solid #ccc;
	border-top: 0;
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
}
.select .select-contents li {
	list-style: none;
}
::-webkit-input-placeholder {
	color: #555 !important;
}
::-moz-placeholder {
	color: #555 !important;
}
:-ms-input-placeholder {
	color: #555 !important;
}
:-moz-placeholder {
	color: #555 !important;
}
label {
	font-weight: 400;
}

#screenshare {
	height: 300px;
	display: inline-block;
	max-height: 50vh;
	max-width: 50vh;
	border: 0;
	margin: 0;
	margin-bottom:15px;
	padding: 0;
	text-shadow: unset;
	box-shadow: unset;
	text-decoration: none;
	border-image-width: 0;
	background-size: contain;
	background-color: rgba(0, 0, 0, 0);
}

.debugStats {
	font-size: 0.8rem;
	list-style-type: none;
	left: 50px;
	top: 0px;
	width: 300px;
	min-height: 200px;
	max-height: 99vh;
	overflow-y: auto;
	background-color: rgba(0, 0, 0, 0.95);
	position: absolute;
	z-index: 20;
	color: white;
	padding: 20px;
	border: 2px solid #1d1d1d;
	padding-bottom: 100px!important;
	margin-bottom: 100px!important;
}
.debugStats::-webkit-scrollbar {
	width: 0.5em;
}
.debugStats::-webkit-scrollbar-track {
	background: black;
	border-radius: 10px;
}

.debugStats::-webkit-scrollbar-thumb {
	background: rgb(119, 119, 119);
	border-radius: 10px;
}

.debugStats::-webkit-scrollbar-thumb:hover {
	background: rgb(158, 158, 158);
	;
}
.debugStats h1 {
	font-size: 1rem;
	text-align: left;
	text-transform: uppercase;
	margin-bottom: 10px;
	margin-top: -5px;
}
.debugStats h2 {
	font-size: 0.8rem;
	text-align: left;
	text-transform: uppercase;
	margin-top: 10px;
	white-space: nowrap;
	text-overflow: ellipsis;
	display: block;
	overflow: hidden;
}
.viewstats::-webkit-scrollbar-track {
	box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}
.debugStats li {
	display: flex;
	margin: 5px 0px;
}
.debugStats li:nth-child(even) {
	background: rgba(33, 33, 33, 0.8);
	padding: 2px 0px;
}
.debugStats li span:first-child {
	flex: 1;
	white-space: nowrap;
}
.debugStats li span:last-child {
	flex: 1;
	text-align: right;
	max-height: 49px;
	overflow: auto;
}
.debugStats .close {
	font-weight: bold;
	color: white;
	display: block;
	background: none;
	padding: 0;
	margin: 0;
	font-size: 1.5rem;
	border: none;
	top: 10px;
	right: 10px;
}
.debugStats button:not(.close) {
	margin: 10px 0px;
	padding: 10px 0px;
	background: #263250;
	color: white;
	border-radius: 0;
	width: 100%;
	font-weight: bold;
	border-bottom: 2px solid #364c84;
}

.directorMargins {
	margin: var(--director-margin);
}

.hideLinksClass {
	background-color: var(--container-color);
	width:1191px;
	padding: 10px;
	margin: 5px 10px 10px 10px;
	max-width:100%;
}
.directorContainer {
	background-color: var(--container-color);
	margin: 10px 0px 10px 10px;
	padding: 10px;
	max-width: min(100%, 1191px);
}

#directorLinksButton{
	cursor:pointer;
}
.directorContainer.half {
	background-color: var(--container-color);
	padding: 10px 10px;
	width: min(100%, 591px);
}
.directorBlock {
	padding: 10px 10px 5px 10px;
	margin: var(--regular-margin);
	color: white;
	position:relative;
	max-width: 100%;
	overflow: hidden;
	display:block;
	min-height: 174px;
}
.directorBlock:nth-child(1) {
	background-color: var(--blue-accent);
}
.directorBlock:nth-child(2) {
	background-color: var(--green-accent);
}
.directorBlock:nth-child(3) {
	background-color: var(--olive-accent);
}
.directorBlock:nth-child(4) {
	background-color: var(--red-accent);
}
.directorBlock button {
	margin: 10px;
	box-shadow: unset;
	border:0;
	border-radius:0;
	font-size: 1.1em;
	padding: 6px 9px 4px 9px;
	background-color: #2a2a2a;
	color:white;
	max-width:25%;
}
.directorBlock button i {
	margin-right: 5px;
}
a.task {
	width: 100%;
}

.directorBlock h2 {
	text-transform: uppercase;
	margin-bottom: 10px;
	margin-left: 5px;
	font-size:1.2em;
}
.shift {
	display: inline-block;
	position: relative;
	margin: 0 0 0 4px;
	padding: 0;
	min-width: 25px;
	font-size: 0.8em;
	top: -4.9px;
	color: white;
}
.shift>i {
	cursor:pointer;
	width: 10px;
	margin: 0 auto;
	left: -1.1px;
	position: relative;
}
.shift.locked>i{
	display:none;
}
.shift.locked>span{
	margin-left: 7px;
}
div#roomnotes2 {
	background: var(--container-color);
	padding: 10px !important;
	margin: 0 var(--regular-margin) 10px var(--regular-margin);
	width: 100%;
}

#yourDirectorStatus {
	color: var(--discord-text);
}
.directorBlue{
	background-color: #5c7785 !important;
	display: var(--show-codirectors) !important;

}
.directorBox{
	background-color: #606383 !important;
	display: var(--show-codirectors) !important;
}

/* ---- DIRECTORS PAGE - Guest Controls Box ---- */
.controlsGrid {
	display: flex;
	flex-wrap: wrap;
	gap: 5px;
}

.controlsGrid .group {
	width: 100%;
	display: flex;
	flex-direction: column;
	gap: 5px;
}

.controlsGrid .row {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	gap: 5px;
}
.controlsGrid .row > .row {
	margin: 0;
}
.controlsGrid .row.two > * {
	flex: 1 33%;
}
.controlsGrid .row.three > * {
	flex: 1 25%;
}
.controlsGrid .row.four > * {
	flex: 1 20%;
}
.controlsGrid .row.six > * {
	flex: 1 13%;
}

.controlsGrid .row.eight > * {
	flex: 1 10%;
}


.controlsGrid button {
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0;
	padding: 4px;
	border-radius: 2px;
	gap: 2px;
	box-shadow: 1px 1px 3px rgba(0,0,0,0.25);

	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;

	text-transform: lowercase;
}

.controlsGrid button span {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.controlsGrid button i {
	font-size: 15px;
}

.controlsGrid button[data-action-type="mute-guest"] {
	min-width: calc(33.3% - 5px);
	box-shadow: inset 0px 0px 1px 0px #ff0000b5, 1px 1px 3px rgba(0,0,0,0.25);
}
/* Button icons: Red  */
.controlsGrid button[data-action-type="mute-scene"] i,
.controlsGrid button[data-action-type="mute-guest"] i,
.controlsGrid button[data-action-type="hangup"] i,
.controlsGrid button[data-action-type="remote-global-record"] i,
.controlsGrid button[data-action-type="local-global-record"] i,
.controlsGrid button[data-action-type="recorder-local"] i,
.controlsGrid button[data-action-type="recorder-google-drive-remote"] i,
.controlsGrid button[data-action-type="recorder-remote"] i {
	color: #e30000;
}
/* Button icons: Green  */
.controlsGrid button[data-action-type="addToScene"] i,
.controlsGrid button[data-action-type="solo-chat"] i {
	color: #03a303;
}
/* Button icons: Blue  */
.controlsGrid button[data-action-type=""] i {
	color: #00f;
}

/* Specitic CSS for different elements inside the guest control-buttons */
.darktheme .controlsGrid .director-message-box {
	background-color: var(--discord-grey-3);
	border: 1px solid var(--discord-grey-8);
}

.darktheme .controlsGrid .director-message-box button {
	background-color: var(--discord-grey-6);
}

.controlsGrid .director-message-box {
	display: flex;
	flex-wrap: wrap;
	gap: 5px;
	flex: 1 100% !important;
	padding: 5px;
	background: rgba(0, 0, 0, .15);
	border-radius: 4px;
	max-width: 100%;
}

.darktheme .controlsGrid .director-message-box textarea {
	background-color: var(--discord-grey-6);
	border: 1px solid var(--discord-grey-7);
	color: var(--discord-text);
}

.controlsGrid .director-message-box textarea {
	flex: 1 100%;
	padding: 5px;
	border-radius: 4px;
	outline: none;
}

.controlsGrid .director-message-box .message-close {
	flex: 1;
}
.controlsGrid .director-message-box .message-send {
	flex: 1 33%;
}

.controlsGrid .tooltip {
	flex: 1 calc(50% - 10px);
	display: flex;
	align-items: center;
}
.controlsGrid .tooltip input[type=range] {
	margin: 0;
}
.controlsGrid .tooltip .tooltiptext {
	height: 18px;
	line-height: 1.2;
	top: 3px !important;
	left: 100% !important;
	background-color: #e6a0a0;
	border: 1px solid rgba(0,0,0,1);
	border-radius: 4px;
	font-size: 12px;
}

.controlsGrid .hideDropMenu{
	justify-content: left;
	width: 100%;
	box-shadow:unset;
	background-color: #0000;
	border:0;
	color: #fcfcfc;
}

.darktheme .controlsGrid .orderspan {
	color: var(--discord-text);
}

.controlsGrid .orderspan {
	font-size: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;

	text-align: center;
	position: relative;
	user-select: none;

	color: #fcfcfc;
}
.controlsGrid .orderspan .order-label {
	width: max-content;
}

/* Hightlights for buttons in the guest control-buttons */
/* Dark theme */
.darktheme .controlsGrid .btn-HL-yellow {
	background-color: var(--darktheme-yellow);
}
.darktheme .controlsGrid .btn-HL-peach {
	background-color: var(--darktheme-brown);
}
.darktheme .controlsGrid .btn-HL-green {
	background-color: var(--darktheme-green);
}
.darktheme .controlsGrid .btn-HL-blue {
	background-color: var(--darktheme-blue);
}
.darktheme .controlsGrid .btn-HL-paleblue {
	background-color: rgb(41 54 72);
}
/* Light theme */
.controlsGrid .btn-HL-yellow {
	background-color: rgb(255, 229, 127);
}
.controlsGrid .btn-HL-peach {
	background-color: rgb(228, 203, 189);
}
.controlsGrid .btn-HL-green {
	background-color: rgb(189, 228, 199);
}
.controlsGrid .btn-HL-blue {
	background-color: rgb(170, 204, 248);
}
.controlsGrid .btn-HL-paleblue {
	background-color: rgb(170, 204, 248);
}

/* Hides buttons that are supposed to be hidden when &novice is added to URL */
.controlsGrid .advanced.hide {
	display: none;
}

.appmode #head1 , .appmode #head1a {
	display:flex;
}

.appmode #head1 input, .appmode #head1a input{
	width:100%;
	padding: 10px;
	margin: 5px;
	font-size: 125%;
}
#widget {
	position: absolute;
	width: var(--widget-width);
	max-width: 75%!important;
	height: 100%;
	right: 0;
	top: 0;
}
#widget.left{
	right: unset!important;
	left: 0!important;
}
#directorlayout.widget {
	position: absolute;
	width: 75%;
	left: 0;
}
#directorlayout.widget.left {
	left: unset!important;
	right: 0!important;
}
#localMuteElement{
	top: 1vh;
	right: 1vh;
}
#localVoiceMeter{
	width: 10px;
	height: 10px;
	top: 8px;
	right: 10px;
}
.controlCenterBox{
	display: flex;
	flex-direction: column;
	gap: 2.5px;
	padding: 5px;
}

.darktheme .controlCenterBox .flexBreak {
	border-bottom: 1px double var(--discord-grey-7);
}

.controlCenterBox .flexBreak {
	width: 100%;
	border-bottom: 1px #ccc double;
	margin: 0;
	position: relative;
	user-select: none;
	filter: blur(2px);
}

.darktheme .controlCenterBox .flexBreak span {
	text-shadow: 0px 0px 1px var(--discord-text);
	background-color: var(--discord-grey-3);
	color: var(--discord-text);
}
#hangupContainer {
	font-size: 500%;
    text-align: center;
    margin: auto auto;
    display: flex;
    height: 100%;
    width: 100%;
    vertical-align: middle;
    flex-wrap: wrap;
    align-content: center;
    flex-direction: column;
    justify-content: center;
    align-items: stretch;
}
.controlCenterBox .flexBreak span {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-transform: uppercase;
	font-size: 10px;
	text-shadow: 0px 0px 1px var(--discord-text);
	color: var(--discord-text);
	background-color: #7E7E7E;
	padding: 0px 4px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.contolboxLabel {
	float: left;
	top: 2px;
	margin-left: 5px;
	position: relative;
	cursor: pointer;
}
.fullwindowButton {
	transition: opacity 0.3s;
	width: 4vh;
	height: 4vh;
	max-width: 30px;
	max-height: 30px;
	min-width: 15px;
	min-height: 15px;
	position: absolute;
	display: none;
	z-index: 6;
	right: 4vh;
	top : 4vh;
	color: white;
	cursor: pointer;
}
.fullwindowButtonimg {
	user-select: none;
	background-color:#0007;
	width:4vh;
}


.pull-right {
	float: right;
	right: 0;
}
.pull-left {
	float: left;
	left: 0;
}
.streamID {
	text-align: right;
	margin: 5px 5px 2px 0px;
	font-size: 0.7em;
	text-overflow: ellipsis;
	overflow: hidden;
	position: relative;
	width: 227px;
	display: inline-block;
	color: white;
}
.streamID i {
	margin-left: 1px;
	font-size: 1.3em;
	position: relative;
	top: 1px;
}

.vidcon>h2 {
	font-size: 1em;
	margin-top: 20px;
}
#pptbackbutton {
	margin-left: 20;
}
#pptnextbutton {
	background-color: #007900;
}
#pptbackbutton:active {
	-webkit-box-shadow: inset 0px 0px 17px #4b4b4b;
	-moz-box-shadow: inset 0px 0px 17px #4b4b4b;
	box-shadow: inset 0px 0px 17px #4b4b4b;
	outline: none;
}
#pptnextbutton:active {
	-webkit-box-shadow: inset 0px 0px 17px #4b4b4b;
	-moz-box-shadow: inset 0px 0px 17px #4b4b4b;
	box-shadow: inset 0px 0px 17px #4b4b4b;
	outline: none;
}

.darktheme div#guestFeeds {
	background-color: var(--container-color)
}

div#guestFeeds {
	background-color: var(--container-color);
	padding: 5px 0 15px 20px;
	display: inline-block;
	margin: 0px var(--regular-margin) 80px var(--regular-margin);
}

div#guestFeeds:empty {
	display:none;
}
#hiddenElements{
	visibility:hidden;
	left:-9999;
	top:-9999;
	width:0px;
	height:0px;
	display: block;
}

a#reshare {
	white-space: nowrap;
	margin: 0;
	padding: 0;
	display: inline;
}
#copythisurl+i {
	display: inline;
	font-size: 130%;
}

#joinroomID {
	border-radius: 0;
	padding: 5px;
}
#joinroomID+button {
	margin: 0px var(--regular-margin);
}

#joinbyURL {
	border-radius: 0;
	padding: 5px;
}
#joinbyURL+button {
	margin: 0px var(--regular-margin);
}

.appmode #joinroomID+button {
	width: 110px;
}

.appmode #joinbyURL+button {
	width: 110px;
}

#guestTips {
	margin: 0 auto 15px auto;
	width: 463px;
	display: flex;
	flex-direction: column;
	text-align: left;
	background: #f3f3f3;
	border: 1px solid #cccccc;
	padding: 10px;
}

#guestTips p {
	font-weight: bold;
	margin-bottom: 10px;
}

#guestTips span:nth-child(1) {
	color: red;
}

#guestTips > span > i {
	font-size: 2.5em;
	margin-right: 10px;
}

#guestTips > span > span {
	line-height: 2.5em;
	vertical-align: bottom;
}

.video-label {
	position: absolute;
	bottom: 0.6vh;
	left: 0.5vh;
	margin: 0px;
	color: white;
	padding: 5px 10px;
	background: rgba(0, 0, 0, .5);
	font-size: 1em;
	pointer-events:none;
}
.video-label:empty {
	display:none;
}

.video-label>span:nth-child(2) {
    font-size: 50%;
    display: block;
    text-align: center;
}
.video-label>span:nth-child(3) {
    font-size: 25%;
    display: block;
    text-align: center;
	line-height: 200%;
}
.video-label>span:nth-child(4) {
    font-size: 25%;
    display: block;
    text-align: center;
	line-height: 200%;
}
.video-label>span:nth-child(5) {
    font-size: 25%;
    display: block;
    text-align: center;
	line-height: 200%;
}

.video-label.zoom {
	bottom: 0;
	left: 0;
	pointer-events:none;
}

.video-label.teams {
	background: rgba(0, 0, 0, .4);
	pointer-events:none;
	border-radius: 5px;
}

.video-label.skype {
	bottom: 2vh;
	left: 50%;
	transform: translateX(-50%);
	background: rgba(0, 0, 0, .8);
	pointer-events:none;
	border-radius: 5px;
	font-size: 0.8em;
}

.video-label.ninjablue {
	bottom: 5%;
	left: 0;
	background: #141926;
	padding: 10px 5%;
}

.video-label.toprounded {
	top: 0;
	bottom: unset;
	background: rgb(0 0 0 / 70%);
	padding: 10px 5%;
	left: 50%;
	transform: translateX(-50%);
	width: 50%;
	text-align: center;
	border-bottom-left-radius: 50px;
	border-bottom-right-radius: 50px;
	text-transform: uppercase;
	letter-spacing: 3;
	box-shadow: 0px 0px 10px #00000059;
	font-size: 0.7em
}

.video-label.rounded {
    bottom: 0;
    background: rgb(0 0 0 / 70%);
    padding: 0px 27px;
    left: 0;
    border-top-right-radius: 20px;
    box-shadow: 0px 0px 10px #00000059;
    font-size: 0.7em;
    font-weight: bold;
}

.video-label.fire {
	text-shadow: 0 -1px 4px #FFF, 0 -2px 10px #ff0, 0 -10px 20px #ff8000, 0 -18px 40px #F00;
	font-weight: bold;
	bottom: 2vh;
	left: 0;
	width: 100%;
	text-align: center;
}

.video-meter {
	padding:0.5vh;
	display:block;
	width:0.5vh;
	height:0.5vh;
	min-width:10px;
	min-height:10px;
	top: 2vh;
	right: 2vh;
	background-color:green;
	position:absolute;
	border-radius: 2vh;
	pointer-events:none;
	border: 1px black solid;
	z-Index: 2;
}

.video-meter-2 {
	padding:0;
	display:block;
	width:100%;
	height:100%;
	min-width:10px;
	min-height:10px;
	top: 0;
	right: 0;
	background-color:unset;
	position:absolute;
	border-radius: 5px;
	pointer-events:none;
	border: 5px green solid;
	z-Index: 2;
}

.video-meter-director {
	width: 10px;
	height: 10px;
	top: 8px;
	right: 10px;
}
.video-meter2 {
	display:block;
	padding:0;
	width: 4px;
	height:0%;
	min-width:2px;
	bottom: 1px;
	right: 7px;
	background-color:#0000;
	position:absolute;
	border-radius: 2vh;
	pointer-events:none;
	border: 1px black solid;
	transition: height 0.1s ease, background-color 0.1s ease;
	z-Index: 2;
}

.hasMedia > .video-meter2 {
	display:block;
}
.hasMedia > .video-meter-2 {
	display:block;
}
.hasMedia > .video-meter {
	display:block;
}

#voiceMeterTemplate{
	display:none;
}
#voiceMeterTemplate2{
	display:none;
}

#userList{
	line-height: 1.3em;
}

#userList > div > .video-meter {
	padding: 5px;
	margin-left: 5px;
	top: 0;
	right: 0;
	position: relative;
	display: inline-block;
}
.PPTActive {
	box-shadow: 0px 0px 10px green;
}
.video-mute-state {
	top: 2vh;
	right: 2vh;
	position: absolute;
	color:white;
	border-radius: 2vh;
	background-color:#b11313;
	padding: 2px 2px 2px 1px;
	z-index: 2;
}

.video-mute-state-userlist {
	display:inline-block;
	color:white;
	border-radius: 2vh;
	background-color:#b11313;
	padding: 2.2px 1.5px 2px 2px;
	margin: 0 0 0 5px;
}
.volume-control-userlist {
	display:inline-block;
	color:white;
	border-radius: 2vh;
	background-color:#262c3e;
	padding: 2.2px 1.5px 2px 2px;
	margin: 0 0 0 5px;
	cursor: pointer;
	border: 1px solid #9a9393;
}

.volume-control-userlist input[type=range][orient=vertical] {
	display: block;
	color: white;
	border-radius: 2vh;
	-webkit-appearance: slider-vertical;
	background-color: #262c3e;
	cursor: pointer;
	width: 13px;
	margin: 3px 0;
}
#closedList_connectUsers{

	cursor: pointer;
	color: #d9e4eb;
	padding: 2px;
	margin: 2px 2px 0 0;
	font-size: 140%;
}

.screenshareNotActive{
	opacity: 0.5;
	box-shadow: inset 0 0 50px #290f07;
}
#help_directors_room{
	cursor:pointer;
}

.iframeblob{
	padding-top:18px;
	text-align: left;
	width: 600px;
	display: block;
	margin: auto;
}
#shareScreenGear{
	display:none;
}

div.message-card {
	padding: 10px;
	display: block;
	padding-left: 1em;
	align-items: center;
	width: 600px;
	margin: 0 auto;
	position: relative;
	padding-left: 60px;
	margin: 20px auto;
	box-shadow: 0px 5px 10px -5px #a9a9a9;
}

div.message-card a {
	color: rgb(0 77 218);
	font-weight: bold;
	text-decoration: underline;
}

.message-card ul {
	border: unset !important;
	background-color: unset !important;
	color: unset !important;
	list-style: outside;
}

.warning.message-card {
	border-left: 4px solid #eff150;
	background: #fffded;
}
.info.message-card {
	border-left: 4px solid #aacefd;
	background: #e6e8f0;
}
.darktheme #guestTips {
	background-color: var(--discord-grey-7);
}
.darktheme #guestTips .las {
	color: #FFF;
}
.darktheme .message-card {
	background-color: #000;
}
.darktheme input[type='file'] {
	background-color: #000;
}
.message-card h1 {
	display: block;
	font-size: 110%;
	text-align: left;
}

.message-card p {
	display: block;
	text-align: left;
	margin-top: 10px;
}

div.message-card:before {
	font-family: 'Line Awesome Free';
	font-weight: 900;
	font-size: 2em;
	margin-right: 0.5em;
	position: absolute;
	top: 6px;
	left: 10px;
}

div.message-card.warning:before {
	content: "\f071";
}

div.message-card.info:before {
	content: "\f05a";
}


.video-label.floating3d {
	text-transform: uppercase;
	display: block;
	color: #FFFFFF;
	text-shadow: 0 1px 0 #CCCCCC, 0 2px 0 #c9c9c9, 0 3px 0 #bbb, 0 4px 0 #b9b9b9, 0 5px 0 #aaa, 0 6px 1px rgba(0,0,0,.1), 0 0 5px rgba(0,0,0,.1), 0 1px 3px rgba(0,0,0,.3), 0 3px 5px rgba(0,0,0,.2), 0 5px 10px rgba(0,0,0,.25), 0 10px 10px rgba(0,0,0,.2), 0 20px 20px rgba(0,0,0,.15);
	color: #FFFFFF;
	animation-name: floating; 
	animation-duration: 5s;
	animation-iteration-count: infinite;
	animation-timing-function: ease-in-out;
	width: 100%;
	font-size: 5em;
	font-weight:bold;
	text-align: center;
	bottom: 4vh;
	position: absolute;
}

.director-link-icons {
	font-size: 1.5em;
	float: left;
	bottom: 4px;
	position: relative;
	margin-right: 9px;
}

.switch {
	position: relative;
	margin:5px 5px 2px 5px;
	width: 40px;
	height: 24px;
	bottom:20px;
	border-radius: 2px;
	display: inline-block;
}

.switch input {
	width: 0;
	height: 0;
	opacity: 0;
}

.slider {
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	-webkit-transition: .3s;
	transition: .3s;
	position: absolute;
}

.slider:before {
	content: "";
	height: 17px;
	width: 17px;
	left: 3px;
	bottom: 3px;
	background-color: white;
	-webkit-transition: .3s;
	transition: .3s;
	position: absolute;
}

input:checked + .slider {
	background-color: #86b98f;
}

input:focus + .slider {
	box-shadow: 0 0 1px #86b98f;
}

input:checked + .slider:before {
	-webkit-transform: translateX(16px);
	-ms-transform: translateX(16px);
	transform: translateX(16px);
}
#remoteOBSControl {
	user-select: none;
	z-index: 25;
	max-height: calc(100vh - 116px);
	overflow-y: auto;
}

#remoteOBSControl  button {
	margin:5px;
	padding:10px;
}

.fullscreenOBSControl #remoteOBSControl {
	width: 100%;
    height: 100%;
    flex-direction: row;
    flex-wrap: nowrap;
    align-content: center;
    justify-content: center;
    align-items: center;
	max-height: 100%;
}

.fullscreenOBSControl #remoteOBSControl .promptModalInner {
	width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: center;
    justify-content: center;
    align-items: center;
}

.fullscreenOBSControl #remoteOBSControl .modalClose {
	display:none;
}

.fullscreenOBSControl #header {
	display:none;
}


.darktheme #promptModal, .darktheme .customModelPopup, .darktheme .promptModal {
	background-color: var(--discord-grey-5);
	color: var(--discord-text);
}

#promptModal, .customModelPopup, .promptModal {
	position: absolute;
	background-color: rgb(221 221 221);
	box-shadow: 0 0 30px 10px #0000005c;
	color: black;
	font-size: 1.0em;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border-radius: 10px;
	font-weight: bold;
	z-index:31;
	width:550px;
	max-width:100%;
	overflow: hidden;
	overflow-wrap: break-word;
}

#bufferSettings{
	position: absolute;
	background-color: #ddddddee;
	box-shadow: 0 0 30px 10px #0000005c;
	color: black;
	font-size: 1.0em;
	bottom: 5%;
	left: 50%;
	transform: translate(-50%, 0%);
	border-radius: 10px;
	font-weight: bold;
	z-index:31;
	width:550px;
	max-width:100%;
	overflow: hidden;
	overflow-wrap: break-word;
}
#publishSettings{
	position: absolute;
	background-color: #ddddddee;
	box-shadow: 0 0 30px 10px #0000005c;
	color: black;
	font-size: 1.0em;
	bottom: calc(50% - 130px);
	left: 50%;
	transform: translate(-50%, 0%);
	border-radius: 10px;
	font-weight: bold;
	z-index:31;
	width:680px;
	max-width:100%;
	overflow: hidden;
	overflow-wrap: break-word;
}
.largeTextEntry {
	width: 90%;
	margin: 10px 5%;
	font-size: .8em;
	padding: 0.4em;
	display: block;

}
.promptModalInner {
	position: relative;
	padding: 1em;
	width: 100%;
}

.promptModalMessage {
	position: relative;
	display: block;
	width: 93%;
	margin: 0 5%;
}

#iframe_source{
	width: 100%;
	height: 100%;
	margin: auto;
	border: 10px dashed rgb(64 65 62)
}

iframe.insecure {
	border: 10px dashed rgb(64 65 62)!important;
}
.effects-controls {
    margin: 1rem 0;
}

.zoom-control-group {
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.zoom-slider,
.effect-slider {
    flex: 1;
    min-width: 200px;
    max-width: 350px;
}

.zoom-value {
    min-width: 3rem;
    text-align: right;
}

.effect-selector-group {
    margin: 1rem 0;
}
.startupWarning{
	max-width:100%;
	display:block;
	width: 463px;
	border-left: 4px solid #eff150;
	background: #fffded;
	padding: 10px;
	align-items: center;
	position: relative;
	margin: 17px auto 20px auto;
	box-shadow: 0px 5px 10px -5px #a9a9a9;
	text-align: left;
}
.startupWarning > p {
	text-align: left;
	display:inline-block;
	padding-left: 38px;

}
.startupWarning > i {
	position: absolute;
	font-size: 2em;
	padding: 2px 0 0 0;
}

.darktheme .startupWarning{
	background: black!important;
	box-shadow: 0px 5px 10px -5px #a5a566;
	color: white!important;
}

.container-inner>div, .container-inner>span>div, .container-inner button:not(.gowebcam), .message-card {
	border-radius: 5px;
	box-shadow: 10px 8px 32px -10px #8883;
}

.cameraTip {
	width: 100%;
	display: block;
	border-left: 4px solid #50cef1;
	background: #dbf0ff;
	padding: 10px;
	align-items: center;
	position: relative;
	margin: 8px auto 0px auto;
	box-shadow: 0px 5px 10px -5px #a9a9a9;
	text-align: left;
	font-size: 97%;
	white-space:normal;
	min-height: 44px;
	border-radius: 5px;
}
.cameraTip > p {
	text-align: left;
	display:inline-block;
	padding-left: 32px;
	vertical-align: middle;

}
.cameraTip > i {
	position: absolute;
	font-size: 1.5em;
	padding: 2px 0 0 0;
}

#consentWarning{
	margin: 0 auto 20px auto;
}

#consentWarning2{
	margin: 0px auto 10px auto;
}

#alertModal {
	position: absolute;
	background-color: rgb(221 221 221);
	box-shadow: 0 0 30px 10px #0000005c;
	color: black;
	font-size: 1.2em;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border-radius: 10px;
	font-weight: bold;
	z-index:32;
	overflow-wrap: break-word;
	min-width: min(90vw, 320px);
}

#connectUsers{
	float: right;
	display: none;
	position: absolute;
	max-width: 400px;
	min-width: 150px;
	max-height: 80%;
	background-color: #08090e;
	z-index: 5;
	padding: 10px;
	right: 20px;
	bottom: 120px;
	box-shadow: 2px 2px #313131;
	border-radius: 5px;
	border: 1px solid #252525;
	opacity: 0.7;
	color: white;
}

#alertModal a:link {
	color: blue;
}

#alertModal a:visited {
	color: blue;
}

#alertModal a:hover {
	color: blue;
}

 #alertModal a:active {
	color: blue;
}

.alertModalInner {
	position: relative;
	padding: 2em;
	user-select: none;
}

.modalClose {
	position: absolute;
	top: -4px;
	right: 4px;
	cursor: pointer;
	font-weight: bolder;
	font-size: 1.8em;
}

#modalBackdrop {
	background: var(--background-color);
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 0;
	opacity: 0.8;
}
#modalBackdrop.darktheme{
	background-color: var(--dark-background-color);
}

.modalBackdrop {
	background: var(--background-color);
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 0;
	opacity: 0.8;
}

.modalBackdrop.darktheme{
	background-color: var(--dark-background-color);
}

.opaqueBackdrop{
	background: var(--background-color);
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 0;
	opacity: 1.0;
}
.opaqueBackdrop.darktheme{
	background-color: var(--dark-background-color);
}

.alertModalMessage>select{
	font-size: 100%;
}

.iframeDetails {
	margin: 10px;
	position: relative;
	word-break: break-all;
	max-height: 500px;
	overflow: hidden;
}

.desktop-capturer-selection {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100vh;
	background: rgba(30,30,30,.75);
	color: #fff;
	z-index: 10000000;
	display: flex;
	align-items: center;
	justify-content: center;
}
.desktop-capturer-selection__scroller {
	width: 100%;
	max-height: 100vh;
	overflow-y: auto;
}
.desktop-capturer-selection__list {
	max-width: calc(100% - 100px);
	margin: 50px;
	padding: 0;
	display: flex;
	flex-wrap: wrap;
	list-style: none;
	overflow: hidden;
	justify-content: center;
}
.desktop-capturer-selection__item {
	display: flex;
	margin: 4px;
}
.desktop-capturer-selection__btn {
	display: flex;
	flex-direction: column;
	align-items: stretch;
	width: 145px;
	margin: 0;
	border: 0;
	border-radius: 3px;
	padding: 4px;
	background: #252626;
	text-align: left;
	transition: background-color .15s, box-shadow .15s;
}
.desktop-capturer-selection__btn:hover,
.desktop-capturer-selection__btn:focus {
	background: rgba(98,100,167,.8);
}
.desktop-capturer-selection__thumbnail {
	width: 100%;
	height: 81px;
	object-fit: cover;
}
.desktop-capturer-selection__name {
	margin: 6px 0 6px;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}
.makeSmallerDirectorRoom{
	max-width: calc(100% - 415px);
	min-width: min(calc(100% - 395px), 75%);
}
.pinToSide{
	top: unset;
	left: unset;
	right: 0;
	transform: unset;
	border-radius: unset;
	height: 100%;
	max-width: min(25%, 415px);
	min-width: 395px;
	overflow-y: auto;
	position: fixed;
}
#roomSettings{
	max-height: 100%;
	overflow: auto;
}

body.darktheme {
	color: white;
	scrollbar-color: #000 #333;
}
body.darktheme form>label{
	color: white;
}
body.darktheme .column>h2{
	color: #b6b6b6;
}
body.darktheme .directorsgrid .vidcon > .las {
	background-color: #424242;
}
body.darktheme h2 {
	color: #DDD;
}
body.darktheme .column .las {
	color: var(--discord-text);
}
body.darktheme label {
	color: var(--discord-text);
}
body.darktheme #roomHeader{
	color: var(--discord-text);
}
body.darktheme div.multiselect {
	background-color: var(--discord-grey-7);
}
body.darktheme .audioMenu{
	background-color: var(--discord-grey-7);
}
body.darktheme #avatarDiv{
	background-color: var(--discord-grey-7);
}
body.darktheme .selected {
	border: solid 3px #f8f7f7;
}
body.darktheme #selectAvatarImage label {
	color: #f8f7f7;
}
body.darktheme .avatarLabel {
	color: #f8f7f7;
}
body.darktheme .directorsgrid .vidcon {
	background-color: var(--discord-grey-3);
}
body.darktheme .promptModal {
	background-color: var(--discord-grey-5);
	color: var(--discord-text);
}
body.darktheme .infoblob{
	color: #CCC;
}
body.darktheme .outMessage{
	background-color: #7f89a7;
}
body.darktheme .inMessage {
	background-color: #7e7d7d;
}
body.darktheme .actionMessage {
	background-color: #b1b1b1;
}
body.darktheme #chatInput{
	background-color: var(--discord-grey-7);
	color: var(--discord-text)
}
body.darktheme .alertModal{
	background-color: #ccc;
	filter:brightness(0.85);
}
body.darktheme .directorContainer{
	filter: brightness(0.85);
}
body.darktheme .cameraTip{
	background-color: #27354b;
	color: #e5dbdb;
}
.containerGreen{
	background-color: #649166!important;
}
body.darktheme .containerGreen{
	background-color: #243824!important;
}
body.darktheme .startupWarning>.las  {
	color:white!important;
}

/*
////////////////////////////////////////////
// INTERMEDIATE STYLING - PRE FLEX-SWITCH //
////////////////////////////////////////////
*/

button {
	background-color: var(--lighttheme-2);
	color: var(--lighttheme-text);
	filter: brightness(1);

	display: inline-flex;
	align-items: center;
	justify-content: center;
	-webkit-app-region: no-drag;
	user-select: none;
	cursor: pointer;
	padding: 4px 6px;
	border-radius: 4px;

	border: 1px solid var(--lighttheme-6);
}
button:hover {
	filter: brightness(0.98);
}
button i {
	font-size: 130%;
}
.darktheme :not(.promptModalInner) > button {
	background-color: var(--discord-grey-5);
	border: 1px solid var(--discord-grey-8);
	color: var(--discord-text);
}
.darktheme :not(.promptModalInner) > button:hover {
	filter: brightness(1.05);
}

.gobutton {
	font-size: 110%;
	padding: 10px;
	border: 3px solid #ddd;
	cursor: pointer;
	background-color: #99bfd9;
	color: #000;
	font-weight: 700;
}
.darktheme .gobutton {
	background-color: #6B8698;
	color: #000;
	border: 3px solid #9B9B9B;
}

.chevron::before {
	border-style: solid;
	border-width: 0.14em 0.14em 0 0;
	content: '';
	display: inline-block;
	height: 0.32em;
	transform: rotate(-45deg);
	width: 0.32em;
	margin: 0 7px 0 3px;
}

.chevron.bottom::before {
	transform: rotate(135deg);
	bottom: 3px;
	position: relative;
}

.chevron.right::before {
	transform: rotate(45deg);
}
#chevarrow4::before ,#chevarrow3::before {
	position:relative;
	bottom:1px;
}
#chevarrow4.bottom::before ,#chevarrow3.bottom::before {
	bottom:2px;
}

button.white {
	padding: 6px 10px 4px 9px;
	cursor: pointer;
	border-radius: 2px;
	background-color: #fff;
	color: #000;
	border: 1px solid #000;
}

select {
	background-color: var(--lighttheme-1);
	color: var(--lighttheme-text);
	border-radius: 4px;
	font-size: 16px;
	padding: 1px 2px;
	outline: 0;
	cursor:pointer;
	font-size:95%;
	text-overflow: ellipsis;
	max-width: 100%;
}
.darktheme select {
	background-color: var(--light-grey);
	border: 1px solid var(--discord-grey-8);
	color: var(--near-black);
	border-radius: 4px;
}
.darktheme ul {
	/*  background-color: var(--discord-grey-3);
	border: 1px solid var(--discord-grey-8);
	color: var(--discord-text);*/
}
ul {
	/*	 background-color: var(--lighttheme-1);
	border: 1px solid var(--lighttheme-6);
	color: var(--lighttheme-text);;*/
	border-radius: 4px;
}

li {
	margin: 0.1em 0;
	padding-left: 0.1em;
	line-height: 1.3em;
}

input[type='text'], input[type='password']  {
	background-color: var(--lighttheme-1);
	border: 1px solid var(--lighttheme-6);
	color: var(--lighttheme-text);
	border-radius: 4px;
	padding: 4px;
	height: 25px;
}
input::placeholder {
	color: var(--lighttheme-4) !important;
}
input[type='checkbox'], input[type='radio'] {
	-ms-transform: scale(1.4);
	/* IE */
	-moz-transform: scale(1.4);
	/* FF */
	-webkit-transform: scale(1.4);
	/* Safari and Chrome */
	-o-transform: scale(1.4);
	/* Opera */
	transform: scale(1.4);
	padding: 5px;
	margin: 3px;

	cursor:pointer;
}

.darktheme :not(.promptModalInner) > input {
	border: 1px solid var(--discord-grey-8);
	color: var(--near-black);
	border-radius: 4px;
}

.darktheme :not(.promptModalInner) > input::placeholder {
	color: var(--discord-grey-8) !important;
}

.darktheme .card {
	background-color: var(--discord-grey-7);
}

.card {
	background-color: var(--lighttheme-3);
}

.darktheme .container-inner {
	background-color: var(--discord-grey-7);
}

.container-inner {
	display: none;
	background-color: var(--lighttheme-3);
	min-height: calc(100% - 100px);
	margin-bottom:30px;
}

.title {
	text-align: left;
	user-select: none;
	display: inline-block;
}

.darktheme #addPasswordBasic,
.darktheme #avatarDiv,
.darktheme #avatarDiv2,
.darktheme #avatarDiv3,
.darktheme #audioScreenShare1,
.darktheme #audioMenu,
.darktheme #audioMenu2,
.darktheme #effectsDiv,
.darktheme #effectsDiv2,
.darktheme #effectsDiv3,
.darktheme #grabDirectorSoloLinkParent,
.darktheme #videoMenu,
.darktheme #videoMenu2,
.darktheme #videoMenu3,
.darktheme #headphonesDiv,
.darktheme #headphonesDiv2,
.darktheme #headphonesDiv3,
.darktheme #videoSettings,
.darktheme #videoSettings2,
.darktheme #popupSelector_user_settings,
.darktheme .invite_setting_group {
	background-color: var(--discord-grey-5);
	border: 1px solid var(--discord-grey-8);
}
.darktheme .largeDarkIcon{
	color: var(--dark-background-color)!important;
}
#addPasswordBasic,
#avatarDiv,
#avatarDiv2,
#avatarDiv3,
#audioScreenShare1,
#audioMenu,
#audioMenu2,
#effectsDiv,
#effectsDiv2,
#effectsDiv3,
#grabDirectorSoloLinkParent,
#videoMenu,
#videoMenu2,
#videoMenu3,
#headphonesDiv,
#headphonesDiv2,
#headphonesDiv3,
#videoSettings,
#videoSettings2,
#popupSelector_user_settings,
.invite_setting_group {
	display: inline-block;
	margin-top: 15px;
	width: 463px;
	max-width: 100%;
	padding: 10px;
	border-radius: 2px;
	text-align: left;
}
#addPasswordBasic,
#avatarDiv,
#avatarDiv2,
#avatarDiv3,
#audioScreenShare1,
#audioMenu,
#audioMenu2,
#effectsDiv,
#effectsDiv2,
#effectsDiv3,
#grabDirectorSoloLinkParent,
#videoMenu,
#videoMenu2,
#videoMenu3,
#headphonesDiv,
#headphonesDiv2,
#headphonesDiv3,
#videoSettings,
#videoSettings2,
.invite_setting_group {
	background-color: var(--lighttheme-2);
	border: 1px solid var(--lighttheme-4);
}

#videoSettings, #videoSettings2 {
	width: 463px;
	padding: 10px;
	margin-top: -1px;
	border-radius: 0px 0px 4px 4px;
	text-align: center;
}
#videoSettings2{
	margin-top: 10px;
	font-size: 95%;
}
#videoMenu .title{
	display:inline-block;
	padding:0;
}
#videoMenu {
	padding: 5px 10px 9px 10px;
	vertical-align: middle;
	text-align: left;
	margin-top: 0px;
}

.disable {
	opacity: 0.5;
	cursor: not-allowed;
}

.darktheme .invite_setting_group {
	color: var(--discord-text);
}

.invite_setting_group {
	color: var(--lighttheme-text);
}

.invite_setting_item {
	margin: 10px 0px 0px 0;
}

.invite_setting_group_links {
	margin-top: 10px;
}

#popupSelector {
	background: linear-gradient(6deg, rgba(221, 221, 221, 0) 4%, rgb(0,0,0, 0.5) 30%, #646878A0 100%);
	transition: all 0.2s linear 0s;
	padding: 10px;
	position: fixed;
	top: 0px;
	height: 100%;
	width: 505px;
	max-width: 100%;
	right: -500px;
	overflow: auto;
	z-index: 4;
	-webkit-app-region: no-drag;
	padding-bottom: 80px;
}
#popupSelector label {
	color: black;
}
.darktheme #popupSelector label {
	color: white;
}
#popupSelector .multiselect-contents label{
	color: black;
}
#popupSelector select {
	padding: 4px;
}
.darktheme #popupSelector{
	background-color: #0004;
}
.popupSelector_constraints{
	margin-top: 10px;
}

.popupSelector_constraints input[type='range'] {
	margin-bottom: 10px;
}

#popupSelector_constraints_video > div, #popupSelector_constraints_audio > div {
	padding: 10px 10px 10px 0;
}
#popupSelector_constraints_audio label {
	color:white;
}
#popupSelector_constraints_video label {
	color:white;
}
#popupSelector_user_settings label {
	color:white;
}
ul#audioSource{
	background-color: var(--lighttheme-1);
	margin-top: 7px;
	min-height: 24px;
}
ul#audioSource label{
	background-color: var(--lighttheme-1);
	border: 0;
}
ul#audioSource label{
	color:black;
}
ul#audioSource3{
	background-color: var(--lighttheme-1);
	border: 1px solid var(--lighttheme-6);
}
.darktheme ul#audioSource{
	background-color: var(--light-grey);
	margin-top: 7px;
}
.darktheme ul#audioSource label{
	background-color: var(--light-grey);
	border: 0;
}
.darktheme ul#audioSource3{
	background-color: var(--light-grey);
	border: 1px solid var(--discord-grey-8);
}

.darktheme #grabDirectorSoloLink {
	background-color: var(--discord-grey-3);
	border: 1px solid var(--discord-grey-8);
}
#refreshVideoButton > i{
	cursor: pointer;
	font-size: 120%;
	position: relative;
	top: 3px;
}
#webcamquality3{
	margin-top: 10px;
}
#grabDirectorSoloLink {
	display: inline-block;
	font-size: 16px;
	padding: 2px 6px;
	width: 100%;
	outline: 0;
	border-radius: 4px;
	background-color: var(--lighttheme-1);
	border: 1px solid var(--lighttheme-6);
}

#grabDirectorSoloLinkParent {
	margin-bottom: 10px;
}

/* INITIALLY HIDDEN */
#advancedOptionsAudio,
#advancedOptionsCamera,
#effectsDiv {
	display: none;
}
#advancedOptionsAudio,
#advancedOptionsCamera,
#advancedOptionsGeneral,
button.toggleSettings,
#effectsDiv {
	padding: 10px;
}
#videoname1, #passwordRoom {
	height:unset;
}

#audioMenu ul {
	list-style:none;
}

.generalButton{
	border-radius: 3px;
	padding: 5px;
	max-width: 216px;
	margin: 5px;
}

.roomnotes {
	margin-top: 10px;
}
.randomRoomName{
	position: absolute;
	margin: 3px;
}
.randomRoomName:active{
	animation: shake 0.2s; 
	animation-iteration-count: once;
}
.randomRoomName:hover{
	-webkit-box-shadow: 0px 0px 4px #000;
}

.pressed {
	background: #1e0000 !important;
	-webkit-box-shadow: inset 0px 0px 1px #b90000;
	-moz-box-shadow: inset 0px 0px 1px #b90000;
	box-shadow: inset 0px 0px 1px #b90000;
	outline: none;
	color: white;
}
/* ANIMATIONS */

/* Shake animation */
.shake {
	animation: shake 0.5s;
	animation-iteration-count: once;
}
@keyframes shake {
	0% { transform: translate(1px, 1px) rotate(0deg); }
	10% { transform: translate(-1px, -2px) rotate(-1deg); }
	20% { transform: translate(-3px, 0px) rotate(1deg); }
	30% { transform: translate(3px, 2px) rotate(0deg); }
	40% { transform: translate(1px, -1px) rotate(1deg); }
	50% { transform: translate(-1px, 2px) rotate(-1deg); }
	60% { transform: translate(-3px, 1px) rotate(0deg); }
	70% { transform: translate(3px, 1px) rotate(-1deg); }
	80% { transform: translate(-1px, -1px) rotate(1deg); }
	90% { transform: translate(1px, 2px) rotate(0deg); }
	100% { transform: translate(1px, -2px) rotate(-1deg); }
}


/* Spin animation */
@keyframes spin-animation {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(359deg);
	}
}


/* Flip 180 animations */
/* NOTE: At least used by the flip camera button in control-bar on mobile */
.flip {
	animation: flip180 2s;
	animation-iteration-count: 1;
}
@keyframes flip180 {
	0% {transform: rotate(0);}
	100% {transform: rotate(180deg);}
}

.flip2 {
	animation: flip1802 2s;
	animation-iteration-count: 1;
}
@keyframes flip1802 {
	0% {transform: rotate(180deg)}
	100% {transform: rotate(360deg);}
}


/* Blink-warn / Blink-alert animations */
/* NOTE: At least used by .battery */
@keyframes blink-warn {
	0% { opacity: 0; }
	50% { opacity: 1; }
	100% { opacity: 0; }
}
@keyframes blink-alert {
	0% { opacity: 0; }
	50% { opacity: 1; }
	100% { opacity: 0; }
}


/* Floating animation */
/* NOTE: At least used by .video-label */
@keyframes floating {
	0% { transform: translate(0, 0px); }
	50% { transform: translate(0, 15%); }
	100% { transform: translate(0, -0px); }
}


/* Pulsating animations */
/* NOTE: .pulsate is at least used by #mutetoggle */
.pulsate {
	box-shadow: 0 0 0 0 rgba(14, 19, 26, 1);
	transform: scale(1);
	animation: pulse 2s infinite;
}
@keyframes pulse {
	0% {
		transform: scale(1);
		box-shadow: 0 0 0 0 rgba(14, 19, 26, 0.7);
	}
	15% {
		transform: scale(1.2);
		box-shadow: 0 0 0 10px rgba(2, 3, 4, 0);
	}
	50% {
		transform: scale(1.0);
		box-shadow: 0 0 0 0 rgba(14, 19, 26, 0);
	}
	85% {
		transform: scale(0.95);
		box-shadow: 0 0 0 0 rgba(14, 19, 26, 0);
	}
	100% {
		transform: scale(1);
		box-shadow: 0 0 0 0 rgba(14, 19, 26, 0);
	}
}
@keyframes pulsate {
	0%	{ box-shadow: 0 0 31px #244e1c44; transform: scale(1.0);}
	50%  { box-shadow: 0 0 17px #0004; transform: scale(0.99);}
	100% { box-shadow: 0 0 31px #244e1c44; transform: scale(1.0);}
}
@-webkit-keyframes pulsate {
	0%	{ box-shadow: 0 0 31px #244e1c44; transform: scale(1.0);}
	50%  { box-shadow: 0 0 17px #0004; transform: scale(0.99);}
	100% { box-shadow: 0 0 31px #244e1c44; transform: scale(1.0);}
}


/* Lightbox open animation */
/* NOTE: For opening lightboxes on homepage, and a sidenote, the "outlightbox" equivalent for closeing
   the lightboxes is found in lib.js */
@keyframes inlightbox {
	50% {
		width: 100%;
		left: 0;
		height: 200px;
	}

	100% {
		height: 100%;
		width: 100%;
		top: 0;
		left: 0;
	}
}


/* Fade-in animation */
.fadein {
	animation: fadeIn var(--fadein-speed);
	-webkit-animation: fadeIn var(--fadein-speed);
	-moz-animation: fadeIn var(--fadein-speed);
	-o-animation: fadeIn var(--fadein-speed);
	-ms-animation: fadeIn var(--fadein-speed);
	animation-iteration-count: 1;
}
@keyframes fadeIn {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-moz-keyframes fadeIn {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-webkit-keyframes fadeIn {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-o-keyframes fadeIn {
	0% {opacity:0;}
	100% {opacity:1;}
}
@-ms-keyframes fadeIn {
	0% {opacity:0;}
	100% {opacity:1;}
}


/* Fade-out animation */
.fadeout {
	animation: fadeout 1s;
	opacity: 0!important;
}
.partialFadeout{
	opacity: 0.2 !important;
}
@keyframes fadeout {
	0% {
		opacity: 1
	}
	100% {
		opacity: 0
	}
}


/* Greyout animation */
.greyout {
	animation: greyout 3s;
	opacity: 0.3!important;
}
@keyframes greyout {
	0% {
		opacity: 1
	}
	100% {
		opacity: 0.3
	}
}


/**
 * Below lsits the classes and font styles for the font-awesome icons.
 */

@font-face {
	font-family: 'Line Awesome Free';
	font-style: normal;
	font-weight: 900;
	font-display: auto;
	src: url("lineawesome/fonts/la-solid-900.eot");
	src: url("lineawesome/fonts/la-solid-900.eot?#iefix") format("embedded-opentype"), url("lineawesome/fonts/la-solid-900.woff2") format("woff2"), url("lineawesome/fonts/la-solid-900.woff") format("woff"), url("lineawesome/fonts/la-solid-900.ttf") format("truetype"), url("lineawesome/fonts/la-solid-900.svg#lineawesome") format("svg"); 
}
.las {
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	display: inline-block;
	font-style: normal;
	font-variant: normal;
	text-rendering: auto;
	line-height: 1; }
.las {
	font-family: 'Line Awesome Free';
	font-weight: 900; }
.la-bell:before {
	content: "\f0f3"; }
.la-bell-slash:before {
	content: "\f1f6"; }
.la-long-arrow-alt-right:before {
	content: "\f30b"; }
.la-paperclip:before {
	content: "\f0c6"; }
.la-window-close:before {
	content: "\f410"; }
.la-stream:before {
	content: "\f550"; }
.la-file-upload:before {
	content: "\f574"; }
.la-comment-alt:before {
	content: "\f27a"; }
.la-tv:before {
	content: "\f26c"; }
.la-volume-up:before {
	content: "\f028"; }
.la-comment-dots:before {
	content: "\f4ad"; }
.la-microphone:before {
	content: "\f130"; }
.la-microphone-alt:before {
	content: "\f3c9"; }
.la-video:before {
	content: "\f03d"; }
.la-desktop:before {
	content: "\f108"; }
.la-tv:before {
	content: "\f26c"; }
.la-window-maximize:before {
	content: "\f2d0"; }
.la-sync-alt:before {
	content: "\f2f1"; }
.la-users-cog:before {
	content: "\f509"; }
.la-cog:before {
	content: "\f013"; }
.la-phone:before {
	content: "\f095"; }
.la-gamepad:before {
	content: "\f11b"; }
.la-user-slash:before {
	content: "\f506"; }
.la-skull-crossbones:before {
	content: "\f714"; }
.la-hand-paper:before {
	content: "\f256"; }
.la-phone-slash:before {
	content: "\f3dd;"; }
.la-dot-circle:before {
	content: "\f192"; }
.la-bug:before {
	content: "\f188"; }
.la-question-circle:before {
	content: "\f059"; }
.la-language:before {
	content: "\f1ab"; }
.la-calendar:before {
	content: "\f073"; }
.la-exclamation-circle:before {
	content: "\f06a"; }
.la-plug:before {
	content: "\f1e6"; }
.la-ethernet:before {
	content: "\f796"; }
.la-headphones:before {
	content: "\f025"; }
.la-robot:before {
	content: "\f544"; }
.la-info-circle:before {
	content: "\f05a"; }
.la-play:before {
	content: "\f04b"; }
.la-gamepad:before {
	content: "\f11b"; }
.la-file-video:before {
	content: "\f1c8"; }
.la-blender:before {
	content: "\f517"; }
.la-heartbeat:before {
	content: "\f21e"; }
.la-code-branch:before {
	content: "\f126"; }
.la-info:before {
	content: "\f129"; }
.la-square:before {
	content: "\f0c8"; }
.la-play-circle:before {
	content: "\f144"; }
.la.la-hdd-o:before {
	content: "\f0a0"; }
.la-key:before {
	content: "\f084"; }
.la-broadcast-tower:before {
	content: "\f519"; }
.la-clock:before {
	content: "\f017"; }
.la-tachometer-alt:before {
	content: "\f3fd"; }
.la-fire-alt:before {
	content: "\f7e4"; }
.la-book-open:before {
	content: "\f518"; }
.la-caret-down:before {
	content: "\f0d7"; }
.la-comments:before {
	content: "\f086"; }
.la-caret-right:before {
	content: "\f0da"; }
.la-copy:before {
	content: "\f0c5"; }
.la-tools:before {
	content: "\f7d9"; }
.la-th-large:before {
	content: "\f009"; }
.la-user-circle:before {
	content: "\f2bd"; }
.la-paper-plane:before {
	content: "\f1d8"; }
.la-envelope:before {
	content: "\f0e0"; }
.la-sign-out-alt:before {
	content: "\f2f5"; }
.la-angle-right:before {
	content: "\f105"; }
.la-angle-left:before {
	content: "\f104"; }
.la-external-link-square-alt:before  {
	content: "\f360"; }
.la-plus-square:before {
	content: "\f0fe"; }
.la-microphone-slash:before {
	content: "\f131"; }
.la-user:before {
	content: "\f007"; }
.la-video-slash:before {
	content: "\f4e2"; }
.la-volume-off:before {
	content: "\f026"; }
.la-eye-slash:before {
	content: "\f070"; }
.la-eye:before {
	content: "\f06e"; }
.la-minus:before {
	content: "\f068"; }
.la-minus-circle:before {
	content: "\f056"; }
.la-window-minimize:before {
	content: "\f2d1"; }
.la-hat-wizard:before {
	content: "\f6e8"; }
.la-plus:before {
	content: "\f067"; }
.la-sync:before {
	content: "\f021"; }
.la-circle:before {
	content: "\f111"; }
.la-chevron-left:before {
	content: "\f053"; }
.la-chevron-right:before {
	content: "\f054"; }
.la-binoculars:before {
	content: "\f1e5"; }
.la-user-cog:before {
	content: "\f4fe"; }
.la-stop-circle:before {
	content: "\f28d"; }
.la-redo-alt:before {
	content: "\f2f9"; }
.la-sliders-h:before {
	content: "\f1de"; }
.la-compress-arrows-alt:before {
	content: "\f78c"; }
.la-users:before {
	content: "\f0c0"; }
.la-spinner:before {
	content: "\f110"; }
.la-external-link:before {
	content: "\f35d"; }
.la-pen:before {
	content: "\f304"; }
.la-external-link-alt:before {
	content: "\f35d"; }
.la-times:before {
	content: "\f00d"; }
.la-volume-mute:before {
	content: "\f6a9"; }
.la-plug:before {
	content: "\f1e6"; }
.la-reply:before {
	content: "\f3e5"; }
.la-expand-arrows-alt:before {
	content: "\f31e"; }
.la-headset:before {
	content: "\f590"; }
.la-check:before {
	content: "\f00c"; }
.la-exclamation:before {
	content: "\f12a"; }
.la-chevron-down:before {
	content: "\f078"; }
.la-music:before {
	content: "\f001"; }
.la-thumbtack:before {
	content: "\f08d"; }
.la-hdd:before {
	content: "\f0a0"; }
.la-signal:before {
	content: "\f012"; }
.la-unlock:before {
	content: "\f023"; }
.la-lock-open:before {
	content: "\f3c1"; }
.la-theater-masks:before {
	content: "\f630"; }
.la-compact-disc:before {
	content: "\f51f"; }
.la-random:before {
	content: "\f074"; }
.la-moon:before {
	content: "\f186"; }
.la-mobile:before {
	content: "\f10b"; }
.la-podcast:before {
	content: "\f2ce"; }
.la-chalkboard:before {
	content: "\f51b"; }