{"titles": {"100": "100ay", "enter-a-room-name-to-quick-join": "Enter a room name to quick join", "join-room": "oin roomJay", "enter-the-url-to-load": "Enter the URL to load", "load-url": "oad URLLay", "number-of-outbound-connections": "umber of outbound connectionsNay", "number-of-outbound-audio-streams": "umber of outbound audio streamsNay", "number-of-outbound-video-streams": "umber of outbound video streamsNay", "number-of-scenes-": "umber of scenes.Nay", "total-upload-bitrate": "otal upload bitrateTay", "copy-link-to-clipboard": "opy link to clipboardCay", "save-and-ask-to-reload-the-current-page-on-next-site-visit": "ave and ask to reload the current page on next site visitSay", "will-remember-the-room-prompting-you-the-next-time-you-visit-if-you-wish-to-load-this-director-s-room-again": "ill remember the room, prompting you the next time you visit if you wish to load this director's room againWay", "toggle-between-the-director-control-room-view-and-a-scene-preview-mode-": "oggle between the director control-room view and a scene preview-mode.<PERSON><PERSON>", "stream-a-media-file": "eam a media fileStray", "hold-ctrl-or-cmd-to-select-multiple-files": "old CTRL (or CMD) to select multiple filesHay", "blind-all-guests-in-room-toggle-": "ind all guests in room (toggle)Blay", "load-the-next-guest-in-queue": "oad the next guest in queueLay", "transfer-any-file-to-the-group": "ansfer any file to the groupTray", "toggle-the-chat": "oggle the ChatTay", "mute-the-speaker-alt-a-": "Mute the Speaker (ALT + A)", "mute-the-mic-ctrl-m-": "Mute the Mic (CTRL/⌘ + M)", "disable-the-camera-ctrl-b-": "Disable the Camera (CTRL/⌘ + B)", "share-a-screen-with-others": "are a Screen with othersShay", "add-a-screen-share": "a Screen ShareAdd ay", "share-a-website-with-your-guests-iframe-": "are a website with your guests (IFRAME)Shay", "hold-ctrl-or-cmd-and-click-to-spotlight-this-video": "old CTRL (or CMD) and click to spotlight this videoHay", "full-screen-the-page": "ull-screen the pageFay", "picture-in-picture-the-video-mix": "Picture-in-Picture the video mix", "cycle-the-cameras": "e the CamerasCyclay", "obs-remote-controller-start-stop-and-change-scenes-": "emote Controller; start/stop and change scenes.OBS Ray", "room-settings": "oom SettingsRay", "your-audio-and-video-settings": "our audio and video SettingsYay", "hangup-the-call": "angup the CallHay", "alert-the-host-you-want-to-speak": "ert the host you want to speakAlay", "go-back-a-slide": "o back a slideGay", "next-slide": "ext slideNay", "record-your-stream-to-disk": "ecord your stream to diskRay", "stop-screen-share-recording": "op screen share recordingStay", "this-is-the-preview-of-the-director-s-audio-and-video-output-": "is is the preview of the Director's audio and video output.Thay", "cancel-the-director-s-video-audio": "ancel the Director's Video/AudioCay", "submit-any-error-logs": "ubmit any error logsSay", "show-help-contact-info": "Show help contact info", "language-options": "anguage OptionsLay", "add-to-calendar": "o CalendarAdd tay", "youtube-video-demoing-how-to-do-this": "outube Video demoing how to do thisYay", "invite-a-guest-or-camera-source-to-publish-into-the-group-room": "ite a guest or camera source to publish into the group roomInvay", "if-disabled-the-invited-guest-will-not-be-able-to-see-or-hear-anyone-in-the-room-": "isabled, the invited guest will not be able to see or hear anyone in the room.If day", "use-this-link-in-the-obs-browser-source-to-capture-the-video-or-audio": "e this link as a Browser Source to capture the video or audioUsay", "if-disabled-you-must-manually-add-a-video-to-a-scene-for-it-to-appear-": "isabled, you must manually add a video to a scene for it to appear.If day", "disables-echo-cancellation-and-improves-audio-quality": "isables Echo Cancellation and improves audio qualityDay", "audio-only-sources-are-visually-hidden-from-scenes": "udio-only sources are visually hidden from scenesAay", "allow-for-remote-stat-monitoring-via-the-monitoring-tool": "ow for remote stat monitoring via the monitoring toolAllay", "the-guest-will-be-asked-if-they-want-to-reload-the-previous-link-when-revisiting": "e guest will be asked if they want to reload the previous link when revisitingThay", "guest-will-be-prompted-to-enter-a-display-name": "uest will be prompted to enter a Display NameGay", "display-names-will-be-shown-in-the-bottom-left-corner-of-videos": "isplay Names will be shown in the bottom-left corner of videosDay", "guests-not-actively-speaking-will-be-hidden": "uests not actively speaking will be hiddenGay", "show-a-custom-welcome-message-to-the-joining-guest-of-this-invite-link": "ow a custom welcome message to the joining guest of this invite linkShay", "request-1080p60-from-the-guest-instead-of-720p60-if-possible": "equest 1080p60 from the <PERSON> instead of 720p60, if possibleRay", "the-default-microphone-will-be-pre-selected-for-the-guest": "e default microphone will be pre-selected for the guestThay", "the-default-camera-device-will-selected-automatically": "e default camera device will selected automaticallyThay", "the-camera-will-load-in-a-default-safe-mode-that-may-work-if-other-modes-fail-": "e camera will load in a default safe-mode that may work if other modes fail.Thay", "the-guest-won-t-have-access-to-changing-camera-settings-or-screenshare": "e guest won't have access to changing camera settings or screenshareThay", "allow-the-guests-to-pick-a-virtual-backscreen-effect": "ow the guests to pick a virtual backscreen effectAllay", "disable-animated-transitions-during-video-mixing": "isable animated transitions during video mixingDay", "this-mode-encodes-the-video-and-audio-into-chunks-which-are-shared-with-multiple-viewers-limited-browser-support-can-potentially-reduce-cpu-and-improve-video-quality-but-will-rely-on-a-buffer-": "is mode encodes the video and audio into chunks, which are shared with multiple viewers. Limited browser support. Can potentially reduce CPU and improve video quality, but will rely on a buffer.Thay", "increase-video-quality-that-guests-in-room-see-": "ease video quality that guests in room see.Incray", "the-guest-will-not-see-their-own-self-preview-after-joining": "e guest will not see their own self-preview after joiningThay", "guests-will-have-an-option-to-poke-the-director-by-pressing-a-button": "uests will have an option to poke the Director by pressing a buttonGay", "add-an-audio-compressor-to-the-guest-s-microphone": "an audio compressor to the guest's microphoneAdd ay", "add-an-equalizer-to-the-guest-s-microphone-that-the-director-can-control": "an Equalizer to the guest's microphone that the director can controlAdd ay", "show-some-prep-suggestions-to-the-guests-on-connect": "ow some prep suggestions to the guests on connectShay", "have-screen-shares-stream-id-s-use-a-predictable-prefixed-value-instead-of-a-random-one-": "ave screen-shares stream ID's use a predictable prefixed value instead of a random one.Hay", "allow-the-guest-to-select-an-avatar-image-for-when-they-hide-their-camera": "ow the guest to select an avatar image for when they hide their camera<PERSON><PERSON>y", "use-meshcast-servers-to-restream-video-data-from-this-guest-to-its-viewers-reducing-the-cpu-and-upload-load-in-some-cases-will-increase-latency-a-bit-": "e Meshcast servers to restream video data from this guest to its viewers, reducing the CPU and upload load in some cases. Will increase latency a bit.Usay", "the-guest-s-self-video-preview-will-appear-tiny-in-the-top-right": "e guest's self-video preview will appear tiny in the top rightThay", "show-an-ovelaid-grid-on-the-guest-s-preview-video-to-help-with-self-centering-of-the-guest-": "ow an ovelaid grid on the guest's preview video to help with self-centering of the guest.Shay", "the-guest-can-only-see-the-director-s-video-if-provided": "e guest can only see the Director's video, if providedThay", "the-guest-s-microphone-will-be-muted-on-joining-they-can-unmute-themselves-": "e guest's microphone will be muted on joining. They can unmute themselves.Thay", "have-the-guest-join-muted-so-only-the-director-can-unmute-the-guest-": "ave the guest join muted, so only the director can Unmute the guest.<PERSON>", "the-guest-will-not-be-asked-for-a-video-device-on-connection": "e guest will not be asked for a video device on connectionThay", "make-the-invite-url-encoded-so-parameters-are-harder-to-tinker-with-by-guests-this-also-debrands-the-interface-and-gives-it-a-new-domain-name-": "ake the invite URL encoded, so parameters are harder to tinker with by guests. This also debrands the interface and gives it a new domain name.May", "the-active-speakers-are-made-visible-automatically": "e active speakers are made visible automaticallyThay", "set-the-background-color-to-bright-green": "et the background color to bright greenSay", "fade-videos-in-over-500ms": "ade videos in over 500msFay", "videos-use-an-animated-transition-when-being-remixed": "ideos use an animated transition when being remixedVay", "add-a-10px-margin-around-all-video-elements": "a 10px margin around all video elementsAdd ay", "disable-fit-to-window-optmized-video-scaling-for-added-sharpness-increases-cpu-network-load-though-": "isable fit-to-window optmized video scaling for added sharpness; increases CPU / Network load though.Day", "playback-the-video-with-mono-channel-audio": "ayback the video with mono-channel audioPlay", "have-the-videos-fit-their-respective-areas-even-if-it-means-cropping-a-bit": "ave the videos fit their respective areas, even if it means cropping a bitHay", "have-videos-be-aligned-with-sizing-designed-for-vertical-video": "ave videos be aligned with sizing designed for vertical videoHay", "does-not-impact-scene-order-": "oes not impact scene order.Day", "copy-this-stream-id-to-the-clipboard": "opy this Stream ID to the clipboardCay", "minimize-this-control-box": "inimize this control boxMay", "click-here-to-edit-the-label-for-this-stream-changes-will-propagate-to-all-viewers-of-this-stream": "ick here to edit the label for this stream. Changes will propagate to all viewers of this streamClay", "video-packet-loss-indicator-of-video-preview-green-is-good-red-is-bad-flame-implies-cpu-is-overloaded-may-not-reflect-the-packet-loss-seen-by-scenes-or-other-guests-": "ideo packet loss indicator of video preview; green is good, red is bad. Flame implies CPU is overloaded. May not reflect the packet loss seen by scenes or other guests.Vay", "100-charging": "100% charging", "hold-ctrl-or-cmd-while-clicking-the-video-to-open-detailed-stats": "old CTRL or CMD (⌘) while clicking the video to open detailed statsHay", "remotely-change-the-volume-of-this-guest-updates-on-release-dbl-click-to-reset-": "emotely change the volume of this guest; updates on release. Dbl-click to reset.Ray", "mute-this-guest-everywhere": "ute this guest everywhereMay", "disable-video-preview": "isable Video PreviewDay", "low-quality-preview": "ow-Quality PreviewLay", "high-quality-preview": "igh-Quality PreviewHay", "send-a-direct-message-to-this-user-": "end a Direct Message to this user.Say", "toggle-between-the-message-appearing-as-a-large-overlay-and-as-normal-chat": "oggle between the message appearing as a large overlay and as normal chatTay", "move-the-user-to-another-room-controlled-by-another-director": "ove the user to another room, controlled by another director<PERSON><PERSON>", "force-the-user-to-disconnect-they-can-always-reconnect-": "orce the user to Disconnect. They can always reconnect.Fay", "toggle-solo-voice-chat-or-hold-ctrl-cmd-when-selecting-to-make-it-two-way-private-": "oggle solo voice chat or hold CTRL/CMD when selecting to make it two-way private.Tay", "solo-this-video-everywhere-hold-ctrl-cmd-to-just-make-video-larger-": "olo this video everywhere. (Hold CTRL/CMD to just make video larger)Say", "disable-this-guest-s-video-track": "isable this guest's video trackDay", "toggle-the-remote-guest-s-speaker-output": "oggle the remote guest's speaker outputTay", "hide-this-guest-everywhere": "ide this guest everywhereHay", "toggle-the-remote-guest-s-display-output": "oggle the remote guest's display outputTay", "add-this-video-to-any-remote-scene-1-": "is Video to any remote '&amp;scene=1'Add thay", "add-this-video-to-any-remote-scene-2-": "is Video to any remote '&amp;scene=2'Add thay", "remotely-mute-this-audio-in-all-remote-scene-views": "emotely Mute this Audio in all remote '&amp;scene' viewsRay", "add-to-scene-3": "o Scene 3Add tay", "add-to-scene-4": "o Scene 4Add tay", "add-to-scene-5": "o Scene 5Add tay", "add-to-scene-6": "o Scene 6Add tay", "add-to-scene-7": "o Scene 7Add tay", "add-to-scene-8": "o Scene 8Add tay", "request-the-statistics-of-this-video-in-any-active-scene": "equest the statistics of this video in any active sceneRay", "shift-this-video-down-in-order": "ift this Video Down in OrderShay", "current-index-order-of-this-video": "urrent Index Order of this VideoCay", "shift-this-video-up-in-order": "ift this Video Up in OrderShay", "set-a-countdown-timer-that-this-guest-sees-ctrl-cmd-click-to-pause-": "et a countdown timer that this guest sees. CTRL (cmd) + click to pause.Say", "start-recording-this-remote-stream-to-this-local-drive-experimental-": "art Recording this remote stream to this local drive. *experimental*'Stay", "the-remote-guest-will-record-their-local-stream-to-their-local-drive-experimental-": "e Remote Guest will record their local stream to their local drive. *experimental*Thay", "change-user-parameters": "ange user parametersChay", "remotely-reload-the-guest-s-page-with-a-new-url": "emotely reload the guest's page with a new URLRay", "allow-the-guest-to-select-a-file-to-upload-to-the-director-once-shared-it-will-show-in-the-chat-as-a-download-link-": "ow the guest to select a file to upload to the director. Once shared, it will show in the chat as a download link.Allay", "mirror-the-video-of-this-guest-globally": "irror the video of this guest <PERSON><PERSON><PERSON>", "force-the-remote-sender-to-issue-a-keyframe-to-all-scenes-fixing-pixel-smearing-issues-": "orce the remote sender to issue a keyframe to all scenes, fixing Pixel Smearing issues.Fay", "set-to-audio-channel-1": "et to Audio Channel 1Say", "set-to-audio-channel-2": "et to Audio Channel 2Say", "set-to-audio-channel-3": "et to Audio Channel 3Say", "set-to-audio-channel-4": "et to Audio Channel 4Say", "set-to-audio-channel-5": "et to Audio Channel 5Say", "set-to-audio-channel-6": "et to Audio Channel 6Say", "add-remove-from-group-1": "emove from group 1Add/ray", "add-remove-from-group-2": "emove from group 2Add/ray", "add-remove-from-group-3": "emove from group 3Add/ray", "add-remove-from-group-4": "emove from group 4Add/ray", "add-remove-from-group-5": "emove from group 5Add/ray", "add-remove-from-group-6": "emove from group 6Add/ray", "remote-audio-settings": "emote Audio SettingsRay", "advanced-video-settings": "anced Video SettingsAdvay", "previously-was-0": "Previously was: 0", "this-will-refresh-the-current-device": "This will refresh the current device", "this-will-ask-the-remote-guest-for-permission-to-change": "This will ask the remote guest for permission to change", "a-direct-solo-view-of-the-video-audio-stream-with-nothing-else-its-audio-can-be-remotely-controlled-from-here": "irect solo view of the video/audio stream with nothing else. Its audio can be remotely controlled from hereA day", "this-guest-raised-their-hand-click-this-to-clear-notification-": "is guest raised their hand. Click this to clear notification.Thay", "takes-the-guest-out-of-queue-mode-they-will-then-join-as-a-normal-guest-": "akes the guest out of queue mode; they will then join as a normal guest.<PERSON><PERSON>", "add-to-scene-2": "o Scene 2Add tay", "activate-or-reload-this-video-device-": "ivate or Reload this video device.Actay", "tip-hold-ctrl-command-to-select-multiple": "ip: Hold CTRL (command) to select Multiple<PERSON>y", "experimental": "experimentalway", "face-detection-api-not-detected-you-may-be-able-to-enable-it-here-chrome-flags-enable-experimental-web-platform-features": "ace Detection API not detected; you may be able to enable it here: chrome://flags/#enable-experimental-web-platform-featuresFay", "improve-performance-and-quality-with-this-tip": "ove performance and quality with this tipImpray", "adjust-the-amount-of-effect-applied": "ust the amount of effect appliedAdjay", "the-solo-view-link-of-the-director-s-video-": "e solo view link of the Director's video.Thay", "this-will-reduce-the-gain-80-when-there-is-no-one-talking-loudly": "is will reduce the gain ~80% when there is no one talking loudlyThay", "only-one-option-available-so-can-t-be-changed": "Only one option available, so can't be changed", "previously-was-1-7777777777777777": "Previously was:  1.7777777777777777", "previously-was-1280": "eviously was:  1280Pray", "hold-ctrl-or-cmd-to-lock-width-and-height-together-when-changing-them": "old CTRL (or cmd) to lock width and height together when changing themHay", "previously-was-720": "eviously was:  720Pray", "previously-was-30-000030517578125": "eviously was:  30.000030517578125Pray", "choose-a-hotkey-for-hold-to-talk-if-using-electron-capture-elevate-privilleges-to-have-it-become-global": "oose a hotkey for Hold-to-Talk. If using Electron Capture, elevate privilleges to have it become globalChay", "draw-on-the-screen": "aw on the ScreenDray", "ctrl-cmd-alt-d-to-toggle": "o toggleCTRL (cmd) + ALT + D tay", "audio-only-sources-can-be-stylized-in-different-ways": "udio-only sources can be stylized in different waysAay", "clear-site-s-local-storage-and-settings": "ear site's local storage and settingsClay", "using-this-may-cause-audio-issues-on-some-systems": "ing this may cause audio-issues on some systemsUsay", "increase-this-at-your-peril-changes-the-total-inbound-video-bitrate-per-guest-mobile-devices-excluded-": "ease this at your peril. Changes the total inbound video bitrate per guest; mobile devices excluded.Incray", "sets-your-max-total-allowed-upload-bandwidth-across-all-connections": "Sets YOUR max total allowed upload bandwidth; across all connections", "only-the-director-s-video-will-be-visible-to-guests-and-within-group-scenes-hold-ctrl-cmd-to-just-make-video-larger-": "e director's video will be visible to guests and within group scenes. (Hold CTRL/CMD to just make video larger)Only thay", "set-a-count-up-timer-that-this-guest-sees-ctrl-cmd-click-to-pause-": "et a count-up timer that this guest sees. CTRL (cmd) + click to pause.Say", "allow-for-remote-co-directors": "ow for remote co-directors<PERSON><PERSON><PERSON>", "record-all-the-guests": "ecord all the guestsRay", "which-audio-bitrate-target-would-you-prefer-": "ich audio bitrate target would you prefer?Whay", "constant-audio-bitrate-or-variable-audio-bitrate": "Constant audio bitrate or variable audio bitrate", "remove-background-noise-but-may-limit-audio-quality": "Remove background noise, but may limit audio quality", "let-the-browser-control-the-mic-s-gain-automatically": "Let the browser control the mic's gain automatically", "stereo-audio-or-mono-must-have-denoise-off-for-this-to-work-": "Stereo audio or mono; must have denoise off for this to work.", "which-video-bitrate-target-would-you-prefer-": "ich video bitrate target would you prefer?Whay", "which-video-codec-would-you-prefer-to-be-used-if-available-": "ich video codec would you prefer to be used if available?Whay", "cannot-see-videos": "annot see videosCay", "cannot-hear-others": "annot hear othersCay", "see-director-only": "ee director only<PERSON>ay", "show-mini-preview": "ow Mini previewShay", "raise-hand-button": "aise hand buttonRay", "show-labels": "ow labelsShay", "transfer-to-a-new-room": "ansfer to a new RoomTray", "enable-custom-password": "able custom passwordEnay", "hide-this-window": "ide this windowHay", "reload-the-page": "eload the pageRay", "select-a-location-that-is-closest-to-both-you-and-your-audience-": "elect a location that is closest to both you and your audience.Say", "add-group-chat-to-obs": "oup <PERSON><PERSON><PERSON><PERSON>", "generate-a-random-room-name": "enerate a random room nameGay", "for-large-group-rooms-this-option-can-reduce-the-load-on-remote-guests-substantially": "or large group rooms, this option can reduce the load on remote guests substantiallyFay", "the-director-will-be-visible-in-scenes-as-if-a-performer-themselves-": "e director will be visible in scenes, as if a performer themselves.Thay", "if-checked-the-director-can-be-added-to-scenes-as-if-a-guest-otherwise-the-director-will-never-appear-in-a-scene-": "ecked, the director can be added to scenes as if a guest. Otherwise, the director will never appear in a scene.If chay", "which-video-codec-would-you-want-used-by-default-": "ich video codec would you want used by default?Whay", "you-ll-enter-as-the-room-s-director": "ou'll enter as the room's director<PERSON><PERSON>", "add-your-camera-to-obs": "our CameraAdd yay", "right-click-this-video-for-additional-options": "ight-click this video for additional optionsRay", "start-streaming-alt-s-": "Start streaming (Alt + s)", "video-source-list": "Video source list", "play-a-sound-out-of-the-selected-audio-playback-device": "Play a sound out of the selected audio playback device", "enable-the-chrome-experimental-features-flag-to-use-chrome-flags-enable-experimental-web-platform-features": "able the Chrome experimental features flag to use: chrome://flags/#enable-experimental-web-platform-featuresEnay", "add-an-optional-password": "Add an optional password", "enter-an-optional-password-here": "Enter an optional password here", "remember-and-reuse-the-provided-stream-id-on-each-visit": "Remember and reuse the provided stream ID on each visit", "consider-using-chrome-instead-of-safari": "Consider using Chrome instead of Safari", "please-update-your-version-of-ios-for-best-performance": "Please update your version of iOS for best performance", "go-back": "Go back", "add-your-microphone-to-obs": "our Microphone to OBSAdd yay", "remote-screenshare-into-obs": "emote ScreenshareRay", "create-reusable-invite": "eate Reusable InviteCray", "ideal-for-1080p60-gaming-if-your-computer-and-upload-are-up-for-it": "eal for 1080p60 gaming, if your computer and upload are up for itIday", "better-video-compression-and-quality-at-the-cost-of-increased-cpu-encoding-load": "etter video compression and quality at the cost of increased CPU encoding loadBay", "disable-digital-audio-effects-and-increase-audio-bitrate": "isable digital audio-effects and increase audio bitrateDay", "the-guest-will-be-able-to-select-digital-video-effects-to-apply-": "e guest will be able to select digital video effects to apply.Thay", "the-guest-will-not-have-a-choice-over-audio-options": "e guest will not have a choice over audio-optionsThay", "the-guest-will-only-be-able-to-select-their-webcam-as-an-option": "e guest will only be able to select their webcam as an optionThay", "encode-the-url-so-that-it-s-harder-for-a-guest-to-modify-the-settings-": "ode the URL so that it's harder for a guest to modify the settings.Encay", "add-a-password-to-make-the-stream-inaccessible-to-those-without-the-password": "a password to make the stream inaccessible to those without the passwordAdd ay", "a-link-for-the-host-speaker-to-chat-with-the-guest-2-way-interview-chat-": "ink for the host speaker to chat with the guest; 2-way interview chat.A lay", "add-the-guest-to-a-group-chat-room-it-will-be-created-automatically-if-needed-": "e guest to a group-chat room; it will be created automatically if needed.Add thay", "customize-the-room-settings-for-this-guest": "ustomize the room settings for this guestCay", "more-options": "ore OptionsMay", "transfer-any-file": "ansfer any fileTray", "enter-an-https-url": "er an HTTPS URLEntay", "open-a-youtube-video-demoing-the-basics-of-vdo-ninja": "Open a YouTube video demoing the basics of VDO.Ninja", "for-a-list-of-common-or-known-issues-click-here": "For a list of common or known issues, click here", "click-to-link-out-to-the-vdo-ninja-help-guide-for-common-obs-studio-problems": "Click to link out to the VDO.Ninja help guide for common OBS Studio problems", "open-a-page-with-recent-vdo-ninja-development-and-feature-updates": "Open a page with recent VDO.Ninja development and feature updates", "info-on-the-native-app-versions-of-vdo-ninja": "Info on the native app versions of VDO.Ninja", "mute-the-speaker": "ute the Speaker<PERSON><PERSON>", "mute-the-mic": "ute the MicMay", "disable-the-camera": "isable the CameraDay", "show-help-info": "ow Help InfoShay", "previously-was-0-5625": "eviously was:  0.5625<PERSON><PERSON>", "join-by-room-name-here": "er a room name to quick join<PERSON><PERSON><PERSON>", "create-a-secondary-stream": "Maak een tweede stream aan", "share-a-website-as-an-embedded-iframe": "Share a website as an embedded iFRAME", "you-can-also-enable-the-director-s-video-output-afterwards-by-clicking-the-setting-s-button": "ou can also enable the director`s Video Output afterwards by clicking the Setting`s buttonYay", "this-low-fi-video-codec-uses-very-little-cpu-even-with-dozens-of-active-viewers-": "This low-fi video codec uses very little CPU, even with dozens of active viewers.", "make-the-invite-url-encoded-so-parameters-are-harder-to-tinker-with-by-guests": "Encodeer de invitatie URL. Om aanpassingen door gast moeilijker te maken", "toggle-solo-voice-chat": "Schakel Solo Geluids Chat", "solo-this-video-everywhere": "Solo this video everywhere", "remotely-change-the-volume-of-this-guest": "Op afstand veranderen volume gast", "increase-this-at-your-peril-changes-the-total-inbound-video-bitrate-per-guest-mobile-devices-excluded-webp-mode-also-excluded-": "Increase this at your peril. Changes the total inbound video bitrate per guest; mobile devices excluded. Webp-mode also excluded.", "useful-if-you-want-to-perform-and-direct-at-the-same-time": "<PERSON>ig als je de director en gast tegelijk wilt zijn.", "start-streaming": "art streamingstay", "hold-ctrl-and-the-mouse-wheel-to-zoom-in-and-out-remotely-of-compatible-video-streams": "Scroll in en uit door CTRL vast te houden en te scrollen met d emuis (wanneer mogelijk)", "creative-commons-by-3-0": "Creative Commons BY 3.0", "set-a-countdown-timer-that-this-guest-sees": "Set a countdown timer that this guest sees", "create-a-third-stream": "Create a Third Stream", "add-group-chat": "Add Group Chat", "add-your-camera": "Add your Camera", "remote-screenshare": "Remote Screenshare", "use-this-link-as-a-browser-source-to-capture-the-video-or-audio": "Use this link as a Browser Source to capture the video or audio", "only-the-director-s-video-will-be-visible-to-guests-and-within-group-scenes": "Only the director's video will be visible to guests and within group scenes", "jump-to-the-documentation": "ump to the documentationJay", "100-battery-remaining": "100% battery remaining", "for-more-known-issues-click-here": "or more known issues, click hereFay", "previously-was-29-970029830932617": "Previously was:  29.970029830932617", "link-out-to-the-vdo-ninja-help-guide-for-obs-studio": "ink out to the help guidelay", "will-slowly-pan-tilt-and-zoom-in-on-the-first-face-detected": "ill slowly pan, tilt, and zoom in on the first face detectedWay", "load-a-website-url": "er the URL to loadEntay", "optional": "Enter an optional password here", "settings": "Instellingen", "lucy-g": "<PERSON>", "flaticon": "Flaticon", "gregor-cresnar": "<PERSON>", "forward-user-to-another-room-they-can-always-return-": "<PERSON><PERSON><PERSON> gast door naar andere kamer,deze kan terug<PERSON>en.", "start-recording-this-stream-experimental-views": "Stream opnemen starten. *experimenteel*", "change-this-audio-s-volume-in-all-remote-scene-views": "Veranderd dit audio volume in alle '&scene' weergaven", "send-direct-message": "<PERSON><PERSON>ur een prive bericht", "advanced-settings-and-remote-control": "Geavanceerde instellingen en Remote Control", "toggle-voice-chat-with-this-guest": "<PERSON><PERSON><PERSON> geluids chat aan/uit met deze gast", "if-enabled-the-invited-guest-will-not-be-able-to-see-or-hear-anyone-in-the-room-": "<PERSON><PERSON> deze optie aan staat kan de gast niemand zien of horen", "if-enabled-you-must-manually-add-a-video-to-a-scene-for-it-to-appear-": "<PERSON><PERSON> deze optie aanstaat moet je video handmatig toevoegen aan een scene om hem te zien.", "set-to-default-audio-channel": "Set to Default Audio Channel", "set-to-audio-channel-7": "Set to Audio Channel 7", "set-to-audio-channel-8": "Set to Audio Channel 8"}, "innerHTML": {"logo-header": "\n<font id=\"qos\" style=\"color: white;\">Vay</font>inja \nDO.Nay", "copy-this-url": "opy this URL into your \"browser source\"Cay", "you-are-in-the-control-center": "ontrol center for room:<PERSON>ay", "joining-room": "ou are in roomYay", "only-director-can-hear-you": "e director can hear you currently.Only thay", "director-muted-you": "e director has muted you.Thay", "director-video-muted-you": "e director has disabled your camera temporarily.Thay", "welcome-to-vdo-ninja-chat": "elcome! You can send text messages directly to connected peers from here.\n\nWay", "send-chat": "endSay", "upload-chat": "<i class=\"las la-file-upload\"></i>oad File Uplay", "mute-the-mic": "\n<i id=\"mutetoggle\" class=\"toggleSize las la-microphone\" style=\"position: relative; top: 0.5px;\"></i>\n", "disable-the-camera": "\n<i id=\"mutevideotoggle\" onmousedown=\"event.preventDefault(); event.stopPropagation();\" class=\"toggleSize las la-video\"></i>\n", "hide-the-links": " LINKS (GUEST INVITES &amp; SCENES)ay", "click-here-for-help": "ick Here for a quick overview and helpClay", "welcome-to-control-room": "\n<b>elcome. This is the director's control-room for the group-chat.Way</b><br><br>ou can host a group chat with friends using a room. Share the blue link to invite guests who will join the chat automatically.\n\nYay<br><br>oup room can handle normally around 6 to 20 guests, depending on numerous factors, including CPU and available bandwidth of all guests in the room\n\nA gray", "invite-a-guest": "INVITE A GUESTay", "invite-users-to-join": "uests can use the link to join the group roomGay", "guests-hear-others": "uests hear othersGay", "copy-link": "opy linkCay", "customize": "ustomizeCay", "capture-a-group-scene": "CAPTURE A GROUP SCENEay", "this-is-obs-browser-source-link": "e studio software to capture the group video mixUsay", "auto-add-guests": "uto-add guestsAay", "pro-audio-mode": "o-audio modePray", "hide-audio-only-sources": "ide audio-only sourcesHay", "remote-monitoring": "emote MonitoringRay", "invite-saved-to-cookie": "ite saved to cookieInvay", "ask-for-display-name": "or display nameAsk fay", "show-display-names": "ow display namesShay", "show-active-speaker": "ow active speakersShay", "show-welcome-message": "ow welcome messageShay", "1080p60-if-available": "ideo if Available1080p60 Vay", "auto-select-microphone": "uto-select default microphoneAay", "auto-select-camera": "uto-select default cameraAay", "compatibility-mode": "ompatibility modeCay", "hide-setting-buttons": "ide settings buttonHay", "virtual-backgrounds": "irtual backgroundsVay", "disable-animated-mixing": "isable animationsDay", "chunked-mode": "unked-modeP2P Chay", "powerful-computers-only": "use with powerful computers and small groups!!Only ay", "guests-see-HD-video": "uests see HD videoGay", "no-self-preview": "isable self-previewDay", "raise-hand-button": "isplay 'raise-hand' buttonDay", "enable-compressor": "able audio compressorEnay", "enable-equalizer": "able equalizer as optionEnay", "show-guest-tips": "ow guest setup tipsShay", "prefix-screenshare": "efix screenshare IDsPray", "avatar-selection": "an select an Avatar imageCay", "meshcast-mode": "eam via serverStray", "mini-self-preview": "ini self-previewMay", "rule-of-thirds": "ow rule-of-thirds gridShay", "only-see-director-feed": "ee the director's feedOnly say", "mute-microphone-by-default": "uted; guest can unmuteMay", "unmute-by-director-only": "uted; director can unmuteMay", "guest-joins-with-no-camera": "uest joins with no cameraGay", "obfuscate-link": "uscate link and parametersObfay", "this-can-reduce-packet-loss": "an reduce packet loss video corruptionCay", "use-h264-codec": "e H264 codecUsay", "show-active-speakers": "ow active speakersShay", "green-background": "een <PERSON>", "fade-videos-in": "ade videos inFay", "animate-mixing": "imate mixingAnay", "add-margin": "argin to videosAdd may", "unlock-video-bitrate": "ock Video Bitrate (20mbps)Unlay", "disable-downscaling": "ease sharpnessIncray", "force-mono-audio": "orce mono audioFay", "fill-video-space": "op video to fitCray", "vertical-aspect-ratio": "ertical video modeVay", "learn-more-about-params": "earn more about URL parameters at Lay", "add-a-label": "a labelAdd ay", "mute": "uteMay", "send-direct-chat": "essageMay", "close": "oseclay", "send-message": "end messagesay", "forward-to-room": "ansferTray", "disconnect-guest": "angupHay", "voice-chat": "olo <PERSON> Say", "solo-video": "ighlightHay", "mute-video-guest": "ideo offVay", "toggle-remote-speaker": "eafenDay", "hide-guest": "ideHay", "toggle-remote-display": "indBlay", "add-to-scene": "add to scene 1way", "scene-options": "ene optionsScay", "add-to-scene2": "add to scene 2way", "mute-scene": "ute in scenemay", "stats-remote": "ene Stats Scay", "additional-controls": "itional controlsAdday", "order-down": "<i class=\"las la-minus\"></i>", "create-timer": "eate TimerCray", "record-local": "ecord Local Ray", "record-remote": "ecord Remote Ray", "google-drive-record": " Google Drive", "change-params": "aramsURL Pay", "change-url": "ange <PERSON>", "request-upload": "equest File Ray", "mirror-guest": "irror Video May", "force-keyframe": "<PERSON><PERSON> <PERSON>uke <PERSON>", "advanced-audio-settings": "udioAay", "advanced-camera-settings": "ideoVay", "user-raised-hand": "ower Raised <PERSON>", "remove-from-queue": "ivate GuestActay", "record-director-local": "ecord Ray", "solo-video-director": "ighlightHay", "video-source": "ideo Source  Vay", "max-resolution": "ax <PERSON>ay", "balanced": "alancedBay", "smooth-cool": "ooth and CoolSmay", "select-audio-source": "udio Source(s)  Aay", "select-output-source": "udio Output Destination:  Aay", "select-avatar-image": "efault <PERSON> / Placeholder Image:  Day", "select-digital-effect": "igital Video Effects:  Day", "no-effects-applied": "o effects appliedNay", "blurred-background": "urred backgroundBlay", "blurred-background-2": "Blurred background 2 🧪", "digital-greenscreen": "igital greenscreenDay", "virtual-background": "irtual backgroundVay", "face-mesh": "ace mesh (slow load)Fay", "digital-zoom": "igital zoomDay", "anonymous-mask": "onymous maskAnay", "dog-face": "og ears and noseDay", "face-tracker": "ace tracker<PERSON>ay", "close-settings": "ose SettingsClay", "user": "<PERSON><PERSON><PERSON>", "hold-to-talk": "Hold-to-Talk Hot-key", "clear": "Clear", "enable": "Enable", "stop": "Stop", "cycle-between-audio-visualizations": "Cycle between several audio-visualizations styles", "cleaer-sites-local-storage": "Clear site's local browser storage and saved settings", "open-in-new-tab": "en in new TabOpay", "copy-to-clipboard": "opy to ClipboardCay", "edit-url": "it URL manuallyEday", "publish-url": "ublish via WHIPPay", "show-qr-code": "ow as QR CodeShay", "open-ss-in-new-tab": "are from a new tabShay", "ss-mode-1": "een Share Mode 1Scray", "ss-mode-2": "een Share Mode 2Scray", "ss-mode-3": "een Share Mode 3Scray", "detach-clock2-pip": "op-out clock togglePay", "mirror-video": "irrorMay", "show-controls-video": "ow control barShay", "hide-controls-video": "ide control barHay", "picture-in-picture": "icture-in-picturePay", "picture-in-picture-all": "Picture-in-picture all", "full-window": "ull-windowFay", "shrink-window": "ink-windowShray", "pause-stream": "ause streamPay", "resume-stream": "esume streamRay", "record-to-disk": "ecord to diskRay", "stop-record-to-disk": "op RecordingStay", "copy-to-clipboard-frame": "apshot to clipboardSnay", "save-current-frame": "ave frame to diskSay", "show-video-stats": "ow StatsShay", "custom-audio-output": "udio DestinationAay", "remote-hangup-connection": "emote Hang-up<PERSON>ay", "remote-reload-connection": "emote Reload PageRay", "change-playout-buffer": "uffer (ms): Bay", "hold-ctrl": "ip:  tay<b>CTRLay</b>(⌘) + ay<b>ickClay</b>or alt-menu fay", "change-room-settings": "ange room settingsChay", "change-room-video-quality": "ange room video quality:<PERSON><PERSON>", "limit-total-bitrate-quality": "Your max allowed total video upload bandwidth:", "highlight-director-only-video-guests-will-see": "ighlight Director (only video guests will see)Hay", "create-global-timer": "eate Global Count-down TimerCray", "create-clock-timer": "oggle Room ClockTay", "allow-for-remote-co-directors": "ow for remote co-directors<PERSON><PERSON><PERSON>", "allow-co-directors-to-transfer-guests": "ow co-directors to transfer guests<PERSON><PERSON><PERSON>", "allow-co-directors-to-change-a-guests-url": "ow co-directors to change a guest's U<PERSON><PERSON><PERSON><PERSON>", "basic-co-director-invite-link": "asic co-director invite link:Bay", "local-global-record-start": "Local record - start all", "local-global-record-stop": "Local record - stop all", "remote-global-record": "emote record - stop allRay", "buffer-settings": "uffer SettingsBay", "publish-settings": "ublishing setupPay", "remote-control-obs-menu": "emote Controller for OBS StudioRay", "apply-new-guest-settings": "ettingsApply say", "cancel": "ancelCay", "invisible-guests": "ot VisibleNay", "select-local-image": "elect Local ImageSay", "available-languages": "ailable Languages:Avay", "add-more-here": "ore Here!Add May", "add-to-calendar": "etails to your Calendar:Add day", "add-to-google-calendar": "o Google CalendarAdd tay", "add-to-outlook-calendar": "o Outlook CalendarAdd tay", "add-to-yahoo-calendar": "o Yahoo CalendarAdd tay", "reload-page": "efreshRay", "add-group-chat": "eate a RoomCray", "rooms-allow-for": "ooms allow for group-chat and the tools to manage multiple guests.<PERSON>", "room-name": "oom NameRay", "password-input-field": "asswordPay", "guests-only-see-director": "u<PERSON> can only see the Director's VideoGay", "scenes-can-see-director": "irector will also be a performerDay", "default-codec-select": "eferred Video Codec:  Pray", "enter-the-rooms-control": "er the Room's Control CenterEntay", "show-tips": "ow me some tips..<PERSON>", "added-notes": "\n<u>\n<i>ortant Tips:Impay</i><br>\n</u>\n<li>isabling video sharing between guests will improve performanceDay</li>\n<li>ite only guests to the room that you trust.Invay</li>\n<li>e \"Recording\" option is considered experimental.Thay</li>", "looking-to-just-chat-and-not-direct": "ooking to just chat and not direct?Lay", "join-the-room-basic": "oin room as participant<PERSON><PERSON>", "back": "ackBay", "add-your-camera": "our CameraAdd yay", "ask-for-permissions": "ow Access to Camera/MicrophoneAllay", "start": "STARTay", "privacy-disabled": "ivacy warning: The director will be able to remotely change your camera, microphone, and URL while this page is open, if you continue.Pray", "for-the-best-possible-experience-make-sure": "or the best possible experience, make sure<PERSON>ay", "your-device-is-powered": "our device is poweredYay", "your-connection-is-hardwired-instead-of-wifi": "our connection is hardwired instead of wifiYay", "you-are-using-headphones-earphones": "ou are using headphones / earphonesYay", "up-to-4k": "4K", "no-audio": "o AudioNay", "add-a-password": "a Password: Add ay", "use-chrome-instead": "onsider using a Chromium-based browser instead.Cay<br>afari is more prone to having audio issues\n Say", "update-your-device": "e've detected that you are using an old version of Apple iOS, which is known to have many issues.Way<br><br>ease consider updating.Play", "add-your-microphone": "our Microphone to OBSAdd yay", "remote-screenshare-obs": "emote ScreenshareRay", "select-screen-to-share": "SELECT SCREEN TO SHAREay", "audio-sources": "udio SourcesAay", "application-audio-capture": "", "1080p-screen-capture-guide": "or achieving 1080p60 game-capture, <PERSON><a href=\"https://docs.vdo.ninja/guides/how-to-screen-share-in-1080p\" target=\"_blank\">ee heresay</a>", "create-reusable-invite": "eate Reusable InviteCray", "here-you-can-pre-generate": "ere you can pre-generate a reusable Browser Source link and a related guest invite link.Hay", "generate-invite-link": "GENERATE THE INVITE LINKay", "quality-paramaters": "uality settingsQay", "force-vp9-video-codec": "orce VP9 Video CodecFay", "enable-stereo-and-pro": "able Stereo and Pro HD AudioEnay", "video-resolution": "ideo Resolution: <PERSON>ay", "general-paramaters": "er <PERSON><PERSON>say", "allow-effects-invite": "ow video effects to be usedAllay", "hide-mic-selection": "orce Default MicrophoneFay", "hide-screen-share": "ide Screenshare OptionHay", "obfuscate_url": "uscate the Invite URLObfay", "add-a-password-to-stream": "a password: Add ay", "interview-paramaters": "o-way chatTway", "generate-host-link": "eate a link for the host speaker<PERSON><PERSON>", "add-the-guest-to-a-room": "e guest to a room: Add thay", "invite-group-chat-type": "is room guest can:<PERSON><PERSON>", "can-see-and-hear": "an see and hear the group chatCay", "can-hear-only": "an only hear the group chatCay", "cant-see-or-hear": "annot hear or see the group chatCay", "share-local-video-file": "eam Media FileStray", "select-the-video-files-to-share": "SELECT THE VIDEO FILES TO SHAREay", "share-website-iframe": "are WebsiteShay", "enter-the-website-URL-you-wish-to-share": "er the URL website you wish to share.Entay", "run-a-speed-test": "un a Speed TestRay", "try-the-mixer-out": "out the MixerTry ay", "try-out-versus-cam": "ulti-Stream MonitorMay", "voice-comms-app": "oup <PERSON> CommsGray", "read-the-guides": "owse the GuidesBray", "wizard-link-generator": "izard Link GeneratorWay", "get-full-documentation": "ull DocumentationFay", "get-the-source-code": "ource CodeSay", "show-your-support": "ow Your SupportShay", "publish-via-whip": "ublish via WHIPPay", "share-whepsrc": "are via WHEPShay", "enter-the-whep-URL-you-wish-to-share": "er the WHEP URL you wish to share.Entay", "info-blob": "\n<h2>at is VDO.NinjaWhay</h2>\n<br>\n<li>100% ay<b>eefray</b>o downloads; no personal data collection; no sign-in; nay</li>\n<li>ing live video from your smartphone, remote computer, or friends directly into OBS or other studio software.Bray</li>\n<li>e use cutting edge Peer-to-Peer forwarding technology that offers privacy and ultra-low latencyWay</li>\n<br>\n<li>outube video \nYay<i class=\"lab la-youtube\"></i>\n<a href=\"https://www.youtube.com/watch?v=QaA_6aOP9z8&amp;list=PLWodc2tCfAH1WHjl4WAOOoRSscJ8CHACe&amp;index=1\" alt=\"Youtube video demoing VDO.Ninja\">emoing it hereDay</a>\n</li>\n<br><h3>or support, see the \n   🛠 Fay<a href=\"https://www.reddit.com/r/VDONinja/\">ub-reddit say<i class=\"lab la-reddit-alien\"></i></a>or join the  ay<a href=\"https://discord.vdo.ninja/\">iscord Day<i class=\"lab la-discord\"></i></a>e . Thay<a href=\"https://docs.vdo.ninja/\">ocumentation is hereday</a>and my personal email is  ay<i><EMAIL></i>\n</h3> \n\n", "guest-1": "uest 1Gay", "guest-2": "uest 2Gay", "guest-3": "uest 3Gay", "guest-4": "uest 4Gay", "waiting-for-camera": "aiting for Camera to LoadWay", "local-global-record": "ocal record - stop allLay", "ok": "✔ OK", "join-room": "Join room", "join-room-with-mic": "<PERSON><PERSON> <PERSON> ka<PERSON> binnen met microfoon", "join-room-with-mic-only": "Join with just Microphone", "join-room-with-camera": "<PERSON><PERSON> <PERSON><PERSON> met camera", "join-room-with-video": "Join Room with Video", "share-screen-with-room": "<PERSON><PERSON> je scherm met de kamer", "share-your-mic": "Deel je microfoon", "share-your-camera": "Deel je camera", "share-your-screen": "Deel je scherm", "click-start-to-join": "Druk op start om erin te gaan", "waiting-for-mic-to-load": "Waiting for mic to load", "waiting-for-camera-to-load": "Wachten tot camera geladen is", "push-to-talk-enable": "enable director`s microphone or video ay<br>only guests can see this feed)(ay", "low-cpu=broadcast-codec": "Low-CPU broadcast codec", "mute-guest": "ute <PERSON>", "More-scene-options": "More scene options", "unmute": "un-mute", "unhide-guest": "un-hide", "undeafen": "un-deafen", "unblind": "un-blind", "advanced": "Advanced ", "advanced-paramaters": "Geavanceerde Parameters", "allow-remote-control": "Afstandsbediening Camera Zoom (android)", "more-than-four-can-join": "<PERSON>r sta<PERSON> momenteel vier gasten plekken gevuld voor de demonstratie. Het is mogelijk om meer gasten te hebben in een kamer.", "toggle-control-video": "Toggle control bar", "chrome-cast": "Cast..", "please-select-option-to-join": "Please select an option to join.", "guest-toggle": "uest ToggleGay", "settings": "ettingsSay", "more": "oreMay", "note-share-audio": "\n\t<b>Noot</b>: <PERSON><PERSON><PERSON><PERSON> niet op \"Deel geluid\" te klikken in Chrome.<br>(Firefox ondersteung geen geluid delen.)", "record": "Neem op", "change-to-low-quality": "&nbsp;&nbsp;<i class=\"las la-video-slash\"></i>", "change-to-medium-quality": "&nbsp;&nbsp;<i class=\"las la-video\"></i>", "change-to-high-quality": "&nbsp;&nbsp;<i class=\"las la-binoculars\"></i>", "click-for-quick-room-overview": "❔ Klik hier voor een snel overzicht en hulp", "names-and-labels-coming-soon": "\n\tNames identifying connected peers will be a feature in an upcoming release.\n", "order-up": "<i class=\"las la-plus\"></i>", "links": "<PERSON><PERSON>"}, "placeholders": {"join-by-room-name-here": "oin by Room Name hereJay", "load-a-website-url": "oad a website URLLay", "enter-chat-message-to-send-here": "er chat message to send hereE<PERSON>y", "enter-your-message-here": "er your message hereEntay", "press-a-key-here": "ess a key herepray", "enter-a-url-for-the-sidebar-page": "er a URL for the sidebar pageEntay", "-whip-url-to-publish-to-goes-here": "o publish to goes here➡️ WHIP URL tay", "-authentication-bearer-token-optional-": "uthentication Bear<PERSON> (optional)🗝️ Aay", "enter-the-remote-obs-password-here": "er the remote OBS password hereEntay", "enter-the-room-name-here": "er the room name hereE<PERSON>y", "enter-the-room-password-here": "er the room password hereEntay", "enter-a-room-name-here": "er a Room Name hereEntay", "optional-room-password-here": "ional room password here<PERSON><PERSON>y", "optional": "optionalway", "give-this-media-source-a-name-optional-": "ive this media source a name (optional)Gay", "add-an-optional-password": "an optional passwordAdd ay", "enter-room-name-here": "er Room name hereEntay"}, "miscellaneous": {"new-display-name": "er a new Display Name for this streamEntay", "submit-error-report": "ess OK to submit any error logs to VDO.Ninja. Error logs may contain private information.Pray", "director-redirect-1": "e director wishes to redirect you to the URL: Thay", "director-redirect-2": "ess OK to be redirected.\n\n<PERSON><PERSON>", "audio-processing-disabled": "udio processing is disabled with this guest. Can't mute or change volumeAay", "not-the-director": "<span color=\"red\">ou are not the director of this room. You will have limited to no control. See Yay<a target=\"_blank\" href=\"https://docs.vdo.ninja/director-settings/codirector\">odirector&amp;cay</a>on how to become a co-director. ay</span>", "room-is-claimed": "e room is already claimed by someone else.\n\nOnly the first person to join a room is the assigned director.\n\nRefresh after the first director leaves to claim.Thay", "token-room-is-claimed": "e room is claimed by someone else.\n\nJoin as a guest or co-director instead.<PERSON>hay", "room-is-claimed-codirector": "e room is already claimed by someone else.\n\nTrying to join as a co-director...Thay", "streamid-already-published": "e stream ID you are publishing to is already in use.\n\nPlease try with a different invite link or refresh to retry again.\n\nYou will now be disconnected.Thay", "director": "irector<PERSON>ay", "unknown-user": "own UserUnknay", "room-test-not-good": "e room name 'test' is very commonly used and may not be secure.\n\nAre you sure you wish to proceed?Thay", "load-previous-session": "ould you like to load your previous session's settings?Way", "enter-password": "ease enter the password below: \n\n(Note: Passwords are case-sensitive and you will not be alerted if it is incorrect.)Play", "enter-password-2": "ease enter the password below: \n\n(Note: Passwords are case-sensitive.)Play", "enter-director-password": "ease enter the director's password:\n\n(Note: Passwords are case-sensitive and you will not be alerted if it is incorrect.)Play", "password-incorrect": "e password was incorrect.\n\nRefresh and try again.Thay", "enter-display-name": "ease enter your display name:Play", "enter-new-display-name": "er a new Display Name for this streamEntay", "what-bitrate": "at bitrate would you like to record at? (kbps)\n(note: This feature is experimental, so have backup recordings going)Whay", "enter-website": "er a website URL to shareEntay", "press-ok-to-record": "ess OK to start recording. Press again to stop and download.\n\nWarning: Keep this browser tab active to continue recording.\n\nYou can change the default video bitrate if desired below (kbps)Pray", "no-streamID-provided": "o streamID was provided; one will be generated randomily.\n\nStream ID: Nay", "alphanumeric-only": "o: Only AlphaNumeric characters should be used for the stream ID.\n\nThe offending characters have been replaced by an underscoreInfay", "stream-id-too-long": "e Stream ID should be less than 45 alPhaNuMeric characters long.\n\nWe will trim it to length.Thay", "share-with-trusted": "are only with those you trustShay", "pass-recommended": "assword is recommendedA pay", "insecure-room-name": "ecure room name.Insay", "allowed-chars": "owed charsAllay", "transfer": "ans<PERSON><PERSON>y", "armed": "armedway", "transfer-guest-to-room": "ansfer guests to room:\n\n(Please note: rooms must share the same password)Tray", "transfer-guest-to-url": "ansfer guests to new website URL.\n\nGuests will be prompted to accept unless they are using &amp;consentTray", "mute-in-scene": "ute in scenemay", "unmute-guest": "unmute guestway", "deafen": "eafen guestday", "blind": "ind guestblay", "unhide": "unhide guestway", "confirm-disconnect-users": "e you sure you wish to disconnect these users?Aray", "confirm-disconnect-user": "e you sure you wish to disconnect this user?Aray", "enter-new-codirector-password": "er a co-director password to use<PERSON><PERSON><PERSON>", "control-room-co-director": "ontrol Room: Co-Director<PERSON><PERSON>", "volume-control": "olume control for local playback onlyVay", "signal-meter": "ideo packet loss indicator of video preview; green is good, red is bad. Flame implies CPU is overloaded. May not reflect the packet loss seen by scenes or other guests.Vay", "waiting-for-the-stream": "aiting for the stream. Tip: Adding &amp;cleanoutput to the URL will hide this spinner, or click to retry, which will also hide it.Way", "main-director": "ain <PERSON>", "share-a-screen": "are a screenShay", "stop-screen-sharing": "op screen sharingStay", "you-have-been-transferred": "ou've been transferred to a different roomYay", "you-have-been-activated": "e director has allowed you to see others in the room nowThay", "you-are-no-longer-a-co-director": "ou are no longer a co-director as you were transferred.Yay", "transferred": "an<PERSON><PERSON><PERSON><PERSON>", "room-changed": "our room has changedYay", "headphones-tip": "<i>ip:Tay</i>e headphones to avoid audio echo issues. Usay", "camera-tip-c922": "<i>ip:Tay</i>o achieve 60-fps with a C922 webcam, low-light compensation needs to be turned off, exposure set to auto, and 720p used. Tay", "camera-tip-camlink": "<i>ip:<PERSON>y</i>am Link may glitch green/purple if accessed elsewhere while already in use. A Cay", "samsung-a-series": "amsung A-series phones may have issues with Chrome; if so, try Firefox Mobile instead or switch video codecs.Say", "screen-permissions-denied": "ermission to capture denied. Ensure your browser has screen record system permissions\n\n1.On your Mac, choose Apple menu  &gt; System Preferences, click Security &amp; Privacy , then click Privacy.\n2.Select Screen Recording.\n3.Select the checkbox next to your browser to allow it to record your screen.Pay", "change-audio-output-device": "udio could not be captured. Please make sure you have an audio output device available.\n\nSome gaming headsets (ie: Corsair) may need to be set to 2-channel output to work, as surround sound drivers may cause problems.Aay", "prompt-access-request": "is trying to view your stream. Allow them? ay", "confirm-reload-user": "e you sure you wish to reload this user's browser?Aray", "webrtc-is-blocked": "is browser has either blocked WebRTC or does not support it.\n\nThis site will not work without it.\n\nDisable any browser extensions or privacy settings that may be blocking WebRTC, or try a different browser.⚠ Thay", "not-clean-session": "ideo effects or canvas rendering failed.\n\nCheck to ensure any remotely hosted images are cross-origin allowed.Vay", "ios-no-screen-share": "orry, but your iOS browser does not support screen-sharing.\n\nPlease see Say<a href=\"https://docs.vdo.ninja/guides/screen-share-your-iphone-ipad\" target=\"_blank\">is guidethay</a>or an alternative method to do so. fay", "android-no-screen-share": "orry, your mobile browser does not support screen-sharing.\n\nThe Say<a href=\"https://docs.vdo.ninja/getting-started/native-mobile-app-versions\" target=\"_blank\">oid native appAndray</a>oes offer basic support for it though. day", "no-screen-share-supported": "orry, your browser does not support screen-sharing.\n\nPlease use the desktop versions of Firefox or Chrome instead.Say", "speech-not-suppoted": "eech Recognition is not supported by this browser⚠ Spay", "blue-yeti-tip": "<i>ip:<PERSON>y</i>ue Yeti microphones may experience issues being overly loud.  Blay<a href=\"https://support.google.com/chrome/thread/7542181?hl=en&amp;msgid=********\">ease see herePlay</a>or a solution or disable auto-gain in VDO.Ninja. fay", "site-not-responsive": "<h3>otice: The system cannot be accessed or is currently slow to respond.Nay</h3>a routing issue, try adding \nIf ay<i title=\"or try visiting https://proxy.vdo.ninja/\">oxy&amp;pray</i>o the URL; you can also try  tay<i>oxy.vdo.ninjahttps://pray</i>or a VPN if the service is blocked in your country.\n\nIf the main service is down, a backup version is also available here:  ay<i>ackup.vdo.ninjahttps://bay</i>ontact <EMAIL> for added help.\n\nThis service requires the use of Websockets over port 443.\n\nCay", "no-audio-source-detected": "o Audio Source was detected.\n\nIf you were wanting to capture an Application's Audio, please see:\nhttps://docs.vdo.ninja/help/guides-and-how-tos#audio for some guides.Nay", "viewer-count": "otal outbound p2p connections of this remote streamTay", "enter-url-for-widget": "er a URL for a page to embed as a sidebarEntay", "director-password": "er the main director's passwordE<PERSON>y", "vision-disabled": "e Director has disabled your vision temporarilyThay<br><br><center><i style=\"font-size:500%;\" class=\"las la-eye-slash\"></i></center>", "invalid-remote-code": "alid remote control code.\n\nUse the field below to try again with a different passcode.Invay", "invalid-remote-code-obs": "alid remote control code.\n\nThe remote OBS system needs a matching passcode set using &amp;remote.\n\nSee the documentation for help..Invay", "request-rejected-obs": "e request was rejected.\n\nThe remote OBS system needs a matching passcode set using &amp;remote.\n\nSee the documentation for help.Thay", "remote-token-rejected": "e remote request failed; the &amp;remote token did not match or the remote user does not allow remote control.Thay", "remote-control-failed": "e remote control request failed.T<PERSON>", "remote-peer-connected": "emote peer connected to video stream.\n\nConnection to handshake server being killed on request. This increases security, but the peer will not be able to reconnect automatically on connection failure.\n\nPress OK to start the stream!Ray", "director-denied": "e main director denied you as a co-directorThay", "only-main-director": "e main director can transfer this guest<PERSON>n<PERSON> thay", "request-failed": "e request failed; you can't apply this actionThay", "tokens-did-not-match": "e remote request failed; the remote token did not match or the remote user does not allow remote control.Thay", "token-not-director": "e request failed; the remote user did not recognize you as the directorThay", "approved-as-director": "e director approved you as a co-directorThay", "you-are-a-codirector": "ou are a co-director of this room; you have partial director control assigned to you.Yay", "this-is-you": "is is you, a co-director.Thay<br>ou are also a performer.Yay", "preview-meshcast-disabled": "ou can't adjust the preview bitrate for Meshcast-based streamsYay", "co-director": "Co-Director", "you-not-yet-activated": "Please wait until the director brings you into the room", "sample-rate-too-high": "Your audio playback device has its sample rate set very high. If having audio issues, try using 48-kHz instead.", "no-network": "Network connection lost 🤷‍♀️❌📶", "no-network-details": "Network connection lost. 🤷‍♀️❌📶\n\nHave you lost your Internet connection?", "enter-password-if-desired": "Enter a password if provided, otherwise just click Cancel", "your-screenshare": "Your screenshare", "your-camera": "Your camera", "accept-inbound-caller": "Accept the inbound telephone caller?", "disable-video": "Disable Video", "show-more-options": "Show more options", "system-default": "System Default"}}