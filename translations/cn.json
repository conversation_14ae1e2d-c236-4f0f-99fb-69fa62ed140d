{"titles": {"100": "100", "enter-a-room-name-to-quick-join": "输入房间名以快速加入", "join-room": "加入房间", "enter-the-url-to-load": "请输入要加载的URL", "load-url": "加载URL", "number-of-outbound-connections": "出站连接数", "number-of-outbound-audio-streams": "出站音频流数", "number-of-outbound-video-streams": "出站视频流数", "number-of-scenes-": "场景数。", "total-upload-bitrate": "总上传比特率", "copy-link-to-clipboard": "复制链接到剪贴板", "save-and-ask-to-reload-the-current-page-on-next-site-visit": "保存并在下次访问网站时询问是否重新加载当前页面", "will-remember-the-room-prompting-you-the-next-time-you-visit-if-you-wish-to-load-this-director-s-room-again": "将记住该房间，并在您下次访问时，如果您希望再次加载该导演的房间，讲提示您", "toggle-between-the-director-control-room-view-and-a-scene-preview-mode-": "在导演控制室视图与场景预览模式之间切换。", "stream-a-media-file": "流式传输媒体文件", "hold-ctrl-or-cmd-to-select-multiple-files": "按住 CTRL（或 CMD）以选择多个文件", "blind-all-guests-in-room-toggle-": "关闭房间内所有嘉宾的视图（切换）", "load-the-next-guest-in-queue": "加载队列中的下一位嘉宾", "transfer-any-file-to-the-group": "将任何文件传输到群组", "toggle-the-chat": "切换聊天功能", "mute-the-speaker-alt-a-": "静音扬声器（ALT + A）", "mute-the-mic-ctrl-m-": "静音麦克风（CTRL/⌘ + M）", "disable-the-camera-ctrl-b-": "关闭摄像头（CTRL/⌘ + B）", "share-a-screen-with-others": "与他人共享屏幕", "add-a-screen-share": "增加共享屏幕", "share-a-website-with-your-guests-iframe-": "通过内嵌框架（IFRAME）与嘉宾分享网站", "hold-ctrl-or-cmd-and-click-to-spotlight-this-video": "按住 CTRL（或 CMD）并单击以聚焦此视频", "full-screen-the-page": "将页面全屏显示", "picture-in-picture-the-video-mix": "画中画视频混合", "cycle-the-cameras": "循环切换摄像头", "obs-remote-controller-start-stop-and-change-scenes-": "OBS 远程控制器；开始/停止及切换场景。", "room-settings": "房间设置", "your-audio-and-video-settings": "您的音频和视频设置", "hangup-the-call": "挂断通话", "alert-the-host-you-want-to-speak": "提醒主持人您想发言", "go-back-a-slide": "返回上一张幻灯片", "next-slide": "下一张幻灯片", "record-your-stream-to-disk": "将您的流媒体记录到磁盘", "stop-screen-share-recording": "停止屏幕共享录制", "this-is-the-preview-of-the-director-s-audio-and-video-output-": "这是导播音视频输出的预览。", "cancel-the-director-s-video-audio": "取消导播的视频/音频", "submit-any-error-logs": "提交任何错误日志", "show-help-contact-info": "显示帮助和联系信息", "language-options": "语言选项", "add-to-calendar": "添加到日历", "youtube-video-demoing-how-to-do-this": "Youtube 视频演示如何操作", "invite-a-guest-or-camera-source-to-publish-into-the-group-room": "邀请嘉宾或摄像头源发布到群组房间", "if-disabled-the-invited-guest-will-not-be-able-to-see-or-hear-anyone-in-the-room-": "如果禁用，受邀嘉宾将无法看到或听到房间内的任何人。", "use-this-link-in-the-obs-browser-source-to-capture-the-video-or-audio": "使用此链接作为OBS浏览器源，以捕捉视频或音频", "if-disabled-you-must-manually-add-a-video-to-a-scene-for-it-to-appear-": "如果禁用，您必须手动添加视频至场景中以显示。", "disables-echo-cancellation-and-improves-audio-quality": "禁用回声消除，提升音频质量", "audio-only-sources-are-visually-hidden-from-scenes": "仅音频源在场景中不可见", "allow-for-remote-stat-monitoring-via-the-monitoring-tool": "允许通过监看工具进行远程状态监控", "the-guest-will-be-asked-if-they-want-to-reload-the-previous-link-when-revisiting": "嘉宾在重新访问时会被询问是否要重新加载之前的链接", "guest-will-be-prompted-to-enter-a-display-name": "将提示嘉宾输入显示名", "display-names-will-be-shown-in-the-bottom-left-corner-of-videos": "显示名称将出现在视频的左下角", "guests-not-actively-speaking-will-be-hidden": "未积极发言的嘉宾将被隐藏", "show-a-custom-welcome-message-to-the-joining-guest-of-this-invite-link": "为使用此邀请链接加入的嘉宾显示自定义欢迎信息", "request-1080p60-from-the-guest-instead-of-720p60-if-possible": "如果可能，请从嘉宾请求 1080p60 而不是 720p60", "the-default-microphone-will-be-pre-selected-for-the-guest": "将为嘉宾预选默认麦克风", "the-default-camera-device-will-selected-automatically": "将自动选择默认摄像头设备", "the-camera-will-load-in-a-default-safe-mode-that-may-work-if-other-modes-fail-": "摄像头将以默认安全模式加载，若其他模式失效，该模式可能仍可正常工作", "the-guest-won-t-have-access-to-changing-camera-settings-or-screenshare": "嘉宾将无权更改摄像头设置或进行屏幕共享", "allow-the-guests-to-pick-a-virtual-backscreen-effect": "允许嘉宾选择虚拟背景幕效果", "disable-animated-transitions-during-video-mixing": "在视频混合时禁用动画过渡效果", "this-mode-encodes-the-video-and-audio-into-chunks-which-are-shared-with-multiple-viewers-limited-browser-support-can-potentially-reduce-cpu-and-improve-video-quality-but-will-rely-on-a-buffer-": "此模式将视频和音频编码成块状格式与多位观众分享，受限的浏览器支持可能会降低CPU使用率并提升视频质量，但依赖于缓冲机制。", "increase-video-quality-that-guests-in-room-see-": "提高房间内嘉宾看到的视频质量。", "the-guest-will-not-see-their-own-self-preview-after-joining": "嘉宾加入后将看不到自己的预览画面", "guests-will-have-an-option-to-poke-the-director-by-pressing-a-button": "嘉宾可通过按下按钮来提醒导演", "add-an-audio-compressor-to-the-guest-s-microphone": "为嘉宾的麦克风添加音频压缩器", "add-an-equalizer-to-the-guest-s-microphone-that-the-director-can-control": "为嘉宾的麦克风添加一个导演可控制的均衡器", "show-some-prep-suggestions-to-the-guests-on-connect": "连接时向嘉宾提供一些准备建议", "have-screen-shares-stream-id-s-use-a-predictable-prefixed-value-instead-of-a-random-one-": "屏幕共享的流ID使用可预测的前缀值，而非随机生成。", "allow-the-guest-to-select-an-avatar-image-for-when-they-hide-their-camera": "允许嘉宾在关闭摄像头时选择一个头像图片", "use-meshcast-servers-to-restream-video-data-from-this-guest-to-its-viewers-reducing-the-cpu-and-upload-load-in-some-cases-will-increase-latency-a-bit-": "使用 Meshcast 服务器将此嘉宾的视频数据重新分发给其他观众，以减少某些情况下的 CPU 和上传负载，但可能会稍微增加延迟。", "the-guest-s-self-video-preview-will-appear-tiny-in-the-top-right": "嘉宾的自拍视频预览将以微小形式出现在右上角", "show-an-ovelaid-grid-on-the-guest-s-preview-video-to-help-with-self-centering-of-the-guest-": "在嘉宾的预览视频上显示一个覆盖网格，帮助嘉宾自我居中。", "the-guest-can-only-see-the-director-s-video-if-provided": "嘉宾仅能观看导演提供的视频", "the-guest-s-microphone-will-be-muted-on-joining-they-can-unmute-themselves-": "嘉宾加入时麦克风将自动静音，他们可以自行解除静音。", "have-the-guest-join-muted-so-only-the-director-can-unmute-the-guest-": "让嘉宾加入时静音，只有导演可以解除其静音状态。", "the-guest-will-not-be-asked-for-a-video-device-on-connection": "连接时不会要求嘉宾提供视频设备", "make-the-invite-url-encoded-so-parameters-are-harder-to-tinker-with-by-guests-this-also-debrands-the-interface-and-gives-it-a-new-domain-name-": "将邀请URL进行编码，使参数难以被客人篡改，同时去除了界面品牌标识并赋予新域名", "the-active-speakers-are-made-visible-automatically": "活动发言者自动可见", "set-the-background-color-to-bright-green": "将背景颜色设置为亮绿色", "fade-videos-in-over-500ms": "视频淡入时间超过500毫秒", "videos-use-an-animated-transition-when-being-remixed": "视频在重新混合时使用动画过渡", "add-a-10px-margin-around-all-video-elements": "在所有视频元素周围添加 10 像素的边距", "disable-fit-to-window-optmized-video-scaling-for-added-sharpness-increases-cpu-network-load-though-": "禁用适应窗口的优化视频缩放以增加清晰度；但会增加 CPU 和网络负载。", "playback-the-video-with-mono-channel-audio": "以单声道音频播放视频", "have-the-videos-fit-their-respective-areas-even-if-it-means-cropping-a-bit": "使视频适应各自区域，即使需要裁剪一部分", "have-videos-be-aligned-with-sizing-designed-for-vertical-video": "使视频与专为垂直视频设计的尺寸对齐", "does-not-impact-scene-order-": "不影响场景顺序。", "copy-this-stream-id-to-the-clipboard": "将此流 ID 复制到剪贴板", "minimize-this-control-box": "最小化此控制框", "click-here-to-edit-the-label-for-this-stream-changes-will-propagate-to-all-viewers-of-this-stream": "点击此处编辑此流的标签，更改将同步至所有观看此流的观众", "video-packet-loss-indicator-of-video-preview-green-is-good-red-is-bad-flame-implies-cpu-is-overloaded-may-not-reflect-the-packet-loss-seen-by-scenes-or-other-guests-": "视频预览的视频包丢失指示器；绿色表示良好，红色表示不良。火焰表示 CPU 过载。可能不会反映场景或其他嘉宾看到的包丢失。", "100-charging": "充电中，电量100%", "hold-ctrl-or-cmd-while-clicking-the-video-to-open-detailed-stats": "点击视频时按住 CTRL 或 CMD (⌘) 以查看详细统计数据", "remotely-change-the-volume-of-this-guest-updates-on-release-dbl-click-to-reset-": "远程调整此嘉宾的音量；释放时更新。双击可重置音量。", "mute-this-guest-everywhere": "在所有地方让这位嘉宾静音", "disable-video-preview": "关闭视频预览功能", "low-quality-preview": "低质量预览模式", "high-quality-preview": "高质量预览模式", "send-a-direct-message-to-this-user-": "向该用户发送私信。", "toggle-between-the-message-appearing-as-a-large-overlay-and-as-normal-chat": "在消息以大型覆盖层和正常聊天之间切换", "move-the-user-to-another-room-controlled-by-another-director": "将用户转移到另一个由其他导演控制的房间", "force-the-user-to-disconnect-they-can-always-reconnect-": "强制用户断开连接。他们可以随时重新连接。", "toggle-solo-voice-chat-or-hold-ctrl-cmd-when-selecting-to-make-it-two-way-private-": "切换单独语音聊天或在选择时按住 CTRL/CMD 以使其双向私密。", "solo-this-video-everywhere-hold-ctrl-cmd-to-just-make-video-larger-": "在所有地方单独显示此视频。（按住 CTRL/CMD 仅放大视频）", "disable-this-guest-s-video-track": "禁用此嘉宾的视频轨道", "toggle-the-remote-guest-s-speaker-output": "切换远程嘉宾的扬声器输出", "hide-this-guest-everywhere": "在所有地方隐藏此嘉宾", "toggle-the-remote-guest-s-display-output": "切换远程嘉宾的显示输出", "add-this-video-to-any-remote-scene-1-": "将此视频添加到任何远程场景 '&scene=1'", "add-this-video-to-any-remote-scene-2-": "将此视频添加到任何远程场景 '&scene=2'", "remotely-mute-this-audio-in-all-remote-scene-views": "在所有远程场景视图中远程静音此音频", "add-to-scene-3": "添加到场景 3", "add-to-scene-4": "添加到场景 4", "add-to-scene-5": "添加到场景 5", "add-to-scene-6": "添加到场景 6", "add-to-scene-7": "添加到场景 7", "add-to-scene-8": "添加到场景 8", "request-the-statistics-of-this-video-in-any-active-scene": "在任何活动场景中请求此视频的统计信息", "shift-this-video-down-in-order": "按顺序将此视频向下移动", "current-index-order-of-this-video": "此视频的当前索引顺序", "shift-this-video-up-in-order": "按顺序将此视频向上移动", "set-a-countdown-timer-that-this-guest-sees-ctrl-cmd-click-to-pause-": "为此嘉宾设置一个倒计时计时器。按住 CTRL (cmd) 并点击以暂停。", "start-recording-this-remote-stream-to-this-local-drive-experimental-": "开始将此远程流录制到此本地驱动器。*实验性的*", "the-remote-guest-will-record-their-local-stream-to-their-local-drive-experimental-": "远程嘉宾将他们的本地流录制到他们的本地驱动器。*实验性的*", "change-user-parameters": "更改用户参数", "remotely-reload-the-guest-s-page-with-a-new-url": "使用新 URL 远程重新加载嘉宾页面", "allow-the-guest-to-select-a-file-to-upload-to-the-director-once-shared-it-will-show-in-the-chat-as-a-download-link-": "允许嘉宾选择文件上传给导演。分享后，文件将以下载链接形式显示在聊天中。", "mirror-the-video-of-this-guest-globally": "在全球范围内镜像此嘉宾的视频", "force-the-remote-sender-to-issue-a-keyframe-to-all-scenes-fixing-pixel-smearing-issues-": "强制远程发送者向所有场景发出关键帧，修复像素拖尾问题。", "set-to-audio-channel-1": "设置为音频通道 1", "set-to-audio-channel-2": "设置为音频通道 2", "set-to-audio-channel-3": "设置为音频通道 3", "set-to-audio-channel-4": "设置为音频通道 4", "set-to-audio-channel-5": "设置为音频通道 5", "set-to-audio-channel-6": "设置为音频通道 6", "add-remove-from-group-1": "从组 1 中添加/删除", "add-remove-from-group-2": "从组 2 中添加/删除", "add-remove-from-group-3": "从组 3 中添加/删除", "add-remove-from-group-4": "从组 4 中添加/删除", "add-remove-from-group-5": "从组 5 中添加/删除", "add-remove-from-group-6": "从组 6 中添加/删除", "remote-audio-settings": "远程音频设置", "advanced-video-settings": "高级视频设置", "previously-was-0": "之前是：0", "this-will-refresh-the-current-device": "这将刷新当前设备", "this-will-ask-the-remote-guest-for-permission-to-change": "这将向远程嘉宾请求更改权限", "a-direct-solo-view-of-the-video-audio-stream-with-nothing-else-its-audio-can-be-remotely-controlled-from-here": "视频/音频流的直接独播视图，没有别的。它的音频可以从这里远程控制", "this-guest-raised-their-hand-click-this-to-clear-notification-": "这位嘉宾举手了。单击此处清除通知。", "takes-the-guest-out-of-queue-mode-they-will-then-join-as-a-normal-guest-": "将嘉宾从队列模式中移出；他们将以普通嘉宾身份加入。", "add-to-scene-2": "添加到场景 2", "activate-or-reload-this-video-device-": "激活或重新加载此视频设备。", "tip-hold-ctrl-command-to-select-multiple": "提示：按住 CTRL（命令键）以选择多个", "experimental": "实验性的", "face-detection-api-not-detected-you-may-be-able-to-enable-it-here-chrome-flags-enable-experimental-web-platform-features": "未检测到人脸识别 API；你或许可以在这里启用它：chrome://flags/#enable-experimental-web-platform-features", "improve-performance-and-quality-with-this-tip": "使用此技巧提高性能和质量", "adjust-the-amount-of-effect-applied": "调整应用效果的量", "the-solo-view-link-of-the-director-s-video-": "导播视频的单独观看链接。", "this-will-reduce-the-gain-80-when-there-is-no-one-talking-loudly": "当没有人大声说话时，这将减少增益约 80%", "only-one-option-available-so-can-t-be-changed": "只有一个选项可用，因此无法更改", "previously-was-1-7777777777777777": "之前为：1.7777777777777777", "previously-was-1280": "之前为：1280", "hold-ctrl-or-cmd-to-lock-width-and-height-together-when-changing-them": "更改宽度和高度时，按住 CTRL（或命令键）以锁定宽度和高度", "previously-was-720": "之前为：720", "previously-was-30-000030517578125": "之前为：30.000030517578125", "choose-a-hotkey-for-hold-to-talk-if-using-electron-capture-elevate-privilleges-to-have-it-become-global": "为按住说话选择一个热键。如果使用 Electron Capture，提升权限使其成为全局热键", "draw-on-the-screen": "在屏幕上绘画", "ctrl-cmd-alt-d-to-toggle": "CTRL (cmd) + ALT + D 切换", "audio-only-sources-can-be-stylized-in-different-ways": "仅音频源可以以不同的方式进行样式设计", "clear-site-s-local-storage-and-settings": "清除站点的本地存储和设置", "using-this-may-cause-audio-issues-on-some-systems": "使用这个可能会在一些系统上导致音频问题", "increase-this-at-your-peril-changes-the-total-inbound-video-bitrate-per-guest-mobile-devices-excluded-": "冒险增加这个设置会改变每位嘉宾的总入站视频比特率；不包括移动设备。", "sets-your-max-total-allowed-upload-bandwidth-across-all-connections": "设置你的最大总上传带宽，适用于所有连接", "only-the-director-s-video-will-be-visible-to-guests-and-within-group-scenes-hold-ctrl-cmd-to-just-make-video-larger-": "只有导演的视频对嘉宾和在群组场景中可见。（按住 CTRL/CMD 可以只让视频变大）", "set-a-count-up-timer-that-this-guest-sees-ctrl-cmd-click-to-pause-": "为这位嘉宾设置一个正计时器。CTRL (cmd) + 点击暂停。", "allow-for-remote-co-directors": "允许远程联合导演", "record-all-the-guests": "录制所有嘉宾", "which-audio-bitrate-target-would-you-prefer-": "你更倾向于哪种音频比特率？", "constant-audio-bitrate-or-variable-audio-bitrate": "固定音频比特率还是可变音频比特率", "remove-background-noise-but-may-limit-audio-quality": "移除背景噪音，但可能会限制音频质量", "let-the-browser-control-the-mic-s-gain-automatically": "让浏览器自动控制麦克风的增益", "stereo-audio-or-mono-must-have-denoise-off-for-this-to-work-": "立体声或单声道；为了使其工作，必须关闭降噪功能", "which-video-bitrate-target-would-you-prefer-": "你更倾向于哪种视频比特率？", "which-video-codec-would-you-prefer-to-be-used-if-available-": "如果可用，你更倾向使用哪种视频编解码器？", "cannot-see-videos": "无法看到视频", "cannot-hear-others": "无法听到其他人", "see-director-only": "只看导演", "show-mini-preview": "显示迷你预览", "raise-hand-button": "举手按钮", "show-labels": "显示标签", "transfer-to-a-new-room": "转移到新房间", "enable-custom-password": "启用自定义密码", "hide-this-window": "隐藏此窗口", "reload-the-page": "重新加载页面", "select-a-location-that-is-closest-to-both-you-and-your-audience-": "选择一个离您和您的观众都最近的位置。", "add-group-chat-to-obs": "将群聊添加到 OBS", "generate-a-random-room-name": "生成一个随机房间名称", "for-large-group-rooms-this-option-can-reduce-the-load-on-remote-guests-substantially": "对于大型群组房间，此选项可以显著减少远程嘉宾的负担", "the-director-will-be-visible-in-scenes-as-if-a-performer-themselves-": "导演将在场景中可见，就像表演者本人一样。", "if-checked-the-director-can-be-added-to-scenes-as-if-a-guest-otherwise-the-director-will-never-appear-in-a-scene-": "如果选中，导演可以像嘉宾一样添加到场景中。否则，导演将永远不会出现在场景中。", "which-video-codec-would-you-want-used-by-default-": "默认情况下，您希望使用哪个视频编解码器？", "you-ll-enter-as-the-room-s-director": "您将以房间导演的身份进入", "add-your-camera-to-obs": "将您的摄像头添加到 OBS", "right-click-this-video-for-additional-options": "右键单击此视频以获取更多选项", "start-streaming-alt-s-": "开始直播（Alt + s）", "video-source-list": "视频源列表", "play-a-sound-out-of-the-selected-audio-playback-device": "从选定的音频播放设备播放声音", "enable-the-chrome-experimental-features-flag-to-use-chrome-flags-enable-experimental-web-platform-features": "启用 Chrome 实验性功能标志以使用：chrome://flags/#enable-experimental-web-platform-features", "add-an-optional-password": "添加可选密码", "enter-an-optional-password-here": "在此输入可选密码", "remember-and-reuse-the-provided-stream-id-on-each-visit": "记住并在每次访问时重用提供的流 ID", "consider-using-chrome-instead-of-safari": "考虑使用 Chrome 而不是 Safari", "please-update-your-version-of-ios-for-best-performance": "请更新您的 iOS 版本以获得最佳性能", "go-back": "返回", "add-your-microphone-to-obs": "将您的麦克风添加到 OBS", "remote-screenshare-into-obs": "远程屏幕共享至 OBS", "create-reusable-invite": "创建可重复使用的邀请链接", "ideal-for-1080p60-gaming-if-your-computer-and-upload-are-up-for-it": "适用于 1080p60 游戏，前提是您的计算机和上传能满足要求", "better-video-compression-and-quality-at-the-cost-of-increased-cpu-encoding-load": "以增加 CPU 编码负载为代价实现更好的视频压缩和质量", "disable-digital-audio-effects-and-increase-audio-bitrate": "禁用数字音频效果并提高音频比特率", "the-guest-will-be-able-to-select-digital-video-effects-to-apply-": "嘉宾可以选择要应用的数字视频效果", "the-guest-will-not-have-a-choice-over-audio-options": "嘉宾无法选择音频选项", "the-guest-will-only-be-able-to-select-their-webcam-as-an-option": "嘉宾只能选择他们的网络摄像头作为选项", "encode-the-url-so-that-it-s-harder-for-a-guest-to-modify-the-settings-": "对 URL 进行编码，使嘉宾更难修改设置", "add-a-password-to-make-the-stream-inaccessible-to-those-without-the-password": "设置密码，防止没有密码的人访问流", "a-link-for-the-host-speaker-to-chat-with-the-guest-2-way-interview-chat-": "主持人发言者与嘉宾聊天的链接；双向访谈聊天", "add-the-guest-to-a-group-chat-room-it-will-be-created-automatically-if-needed-": "将嘉宾添加到群聊房间，如有需要将自动创建。", "customize-the-room-settings-for-this-guest": "自定义此嘉宾的房间设置", "more-options": "更多选项", "transfer-any-file": "传输任何文件", "enter-an-https-url": "输入一个 HTTPS URL", "open-a-youtube-video-demoing-the-basics-of-vdo-ninja": "打开一个演示 VDO.Ninja 基本操作的 YouTube 视频", "for-a-list-of-common-or-known-issues-click-here": "点击此处查看常见或已知问题列表", "click-to-link-out-to-the-vdo-ninja-help-guide-for-common-obs-studio-problems": "点击此处链接到 VDO.Ninja 帮助指南，解决常见的 OBS Studio 问题", "open-a-page-with-recent-vdo-ninja-development-and-feature-updates": "打开一个包含 VDO.Ninja 最新开发和功能更新的页面", "info-on-the-native-app-versions-of-vdo-ninja": "关于 VDO.Ninja 原生应用版本的信息", "mute-the-speaker": "使扬声器静音", "mute-the-mic": "使麦克风静音", "disable-the-camera": "禁用摄像头", "show-help-info": "显示帮助信息", "previously-was-0-5625": "之前是：0.5625", "join-by-room-name-here": "在此输入要快速加入的房间名称", "create-a-secondary-stream": "创建第二个流", "share-a-website-as-an-embedded-iframe": "将网站以嵌入式 iframe 形式共享", "you-can-also-enable-the-director-s-video-output-afterwards-by-clicking-the-setting-s-button": "您还可以通过单击设置按钮启用导演的视频输出", "this-low-fi-video-codec-uses-very-little-cpu-even-with-dozens-of-active-viewers-": "这种低保真视频编解码器使用很少的 CPU，即使有几十位活跃的观众。", "make-the-invite-url-encoded-so-parameters-are-harder-to-tinker-with-by-guests": "对邀请 URL 进行编码，这样嘉宾更难修改参数", "toggle-solo-voice-chat": "切换单独语音聊天", "solo-this-video-everywhere": "在任何地方单独播放此视频", "remotely-change-the-volume-of-this-guest": "远程调整此嘉宾的音量", "increase-this-at-your-peril-changes-the-total-inbound-video-bitrate-per-guest-mobile-devices-excluded-webp-mode-also-excluded-": "慎重增加此项，它会更改每位嘉宾的总入站视频比特率（移动设备和 Webp 模式除外）。", "useful-if-you-want-to-perform-and-direct-at-the-same-time": "如果您希望边表演边导演，这项功能非常实用", "start-streaming": "开始直播", "hold-ctrl-and-the-mouse-wheel-to-zoom-in-and-out-remotely-of-compatible-video-streams": "按住 CTRL 键并滚动鼠标滚轮可以远程缩放兼容的视频流", "creative-commons-by-3-0": "知识共享署名 3.0 许可", "set-a-countdown-timer-that-this-guest-sees": "为此嘉宾设置一个倒计时器", "create-a-third-stream": "创建第三条流", "add-group-chat": "添加群聊", "add-your-camera": "添加您的摄像头", "remote-screenshare": "远程屏幕共享", "use-this-link-as-a-browser-source-to-capture-the-video-or-audio": "使用此链接作为浏览器源以捕获视频或音频", "only-the-director-s-video-will-be-visible-to-guests-and-within-group-scenes": "只有导演的视频才会对嘉宾和群组场景中的人可见", "jump-to-the-documentation": "跳转到文档", "100-battery-remaining": "剩余电量 100%", "for-more-known-issues-click-here": "点击这里查看更多已知问题", "previously-was-29-970029830932617": "之前是：29.970029830932617", "link-out-to-the-vdo-ninja-help-guide-for-obs-studio": "链接到 VDO.Ninja 的 OBS Studio 帮助指南", "will-slowly-pan-tilt-and-zoom-in-on-the-first-face-detected": "将慢慢平移、倾斜并缩放至检测到的第一个面孔", "load-a-website-url": "输入要加载的网址 URL", "optional": "在此输入可选密码", "settings": "设置", "lucy-g": "<PERSON>", "flaticon": "Flaticon", "gregor-cresnar": "<PERSON>", "forward-user-to-another-room-they-can-always-return-": "将用户转发到另一个房间。他们随时可以返回。", "start-recording-this-stream-experimental-views": "开始录制此流（实验性功能）。", "change-this-audio-s-volume-in-all-remote-scene-views": "在所有远程场景视图中更改此音频的音量。", "send-direct-message": "发送直接消息", "advanced-settings-and-remote-control": "高级设置和远程控制", "toggle-voice-chat-with-this-guest": "切换与该嘉宾的语音聊天", "if-enabled-the-invited-guest-will-not-be-able-to-see-or-hear-anyone-in-the-room-": "如果启用，被邀请的嘉宾将看不到或听不到房间内的任何人。", "if-enabled-you-must-manually-add-a-video-to-a-scene-for-it-to-appear-": "如果启用，您必须手动将视频添加到场景中才能显示。", "set-to-default-audio-channel": "设置为默认音频频道", "set-to-audio-channel-7": "设置为音频频道 7", "set-to-audio-channel-8": "设置为音频频道 8"}, "innerHTML": {"logo-header": "\n<font id=\"qos\">V</font>DO.Ninja \n", "copy-this-url": "将此 URL 复制到“浏览器源”中", "you-are-in-the-control-center": "您处于控制中心：", "joining-room": "正在加入房间", "only-director-can-hear-you": "目前只有导演能听到您的声音。", "director-muted-you": "导演已经把您静音了。", "director-video-muted-you": "导演已暂时禁用您的摄像头。", "welcome-to-vdo-ninja-chat": "\n欢迎！您可以从此处直接向连接的对等方发送文本信息。\n", "send-chat": "发送", "upload-chat": "<i class=\"las la-file-upload\"></i> 上传文件", "mute-the-mic": "\n<i id=\"mutetoggle\" class=\"toggleSize las la-microphone\" style=\"position: relative; top: 0.5px;\"></i>\n", "disable-the-camera": "\n<i id=\"mutevideotoggle\" onmousedown=\"event.preventDefault(); event.stopPropagation();\" class=\"toggleSize las la-video\"></i>\n", "hide-the-links": " 隐藏链接（邀请嘉宾和场景）", "click-here-for-help": "单击此处获取快速概览和帮助", "welcome-to-control-room": "\n<b>欢迎光临。这是导演的控制室，用于群聊</b><br><br>\n您可以使用聊天室与朋友进行群聊。共享蓝色链接以邀请将自动加入聊天的嘉宾。\n<br><br>\n根据许多因素，包括 CPU 和房间内所有嘉宾的可用带宽，一个群聊房间通常可以处理大约 6 到 20 位嘉宾\n", "invite-a-guest": "邀请嘉宾", "invite-users-to-join": "嘉宾可以使用链接加入群组房间", "guests-hear-others": "嘉宾能听到别人的声音", "copy-link": "复制链接", "customize": "自定义", "capture-a-group-scene": "捕获群组场景", "this-is-obs-browser-source-link": "这是用于在演播室软件中捕捉群组视频合成的链接", "auto-add-guests": "自动添加嘉宾", "pro-audio-mode": "专业音频模式", "hide-audio-only-sources": "隐藏纯音频源", "remote-monitoring": "远程监看", "invite-saved-to-cookie": "邀请信息已保存到 cookie", "ask-for-display-name": "要求提供显示名称", "show-display-names": "显示嘉宾的名称", "show-active-speaker": "高亮显示当前发言者", "show-welcome-message": "显示欢迎信息", "1080p60-if-available": "如果可用，使用 1080p60 视频", "auto-select-microphone": "自动选择默认麦克风", "auto-select-camera": "自动选择默认摄像头", "compatibility-mode": "兼容模式", "hide-setting-buttons": "隐藏设置按钮", "virtual-backgrounds": "虚拟背景", "disable-animated-mixing": "禁用动画混合", "chunked-mode": "分块模式 (P2P)", "powerful-computers-only": "仅适用于高性能计算机和小型群组！！！", "guests-see-HD-video": "让嘉宾观看高清视频", "no-self-preview": "禁用个人预览", "raise-hand-button": "显示“举手”按钮", "enable-compressor": "启用音频压缩器", "enable-equalizer": "启用均衡器", "show-guest-tips": "显示嘉宾设置提示", "prefix-screenshare": "屏幕共享的 ID 前缀", "avatar-selection": "选择头像图片", "meshcast-mode": "启用服务器中继流传输", "mini-self-preview": "迷你自我预览", "rule-of-thirds": "显示三分法网格", "only-see-director-feed": "只看导演视频流", "mute-microphone-by-default": "静音；嘉宾可以取消静音", "unmute-by-director-only": "静音；导演可以取消静音", "guest-joins-with-no-camera": "嘉宾以无摄像头模式加入", "obfuscate-link": "混淆链接和参数", "this-can-reduce-packet-loss": "这有助于减少数据包丢失和视频损坏", "use-h264-codec": "使用 H264 编解码器", "show-active-speakers": "显示活跃发言者", "green-background": "绿色背景", "fade-videos-in": "视频淡入效果", "animate-mixing": "动画合成", "add-margin": "添加视频边框", "unlock-video-bitrate": "解锁视频比特率 (20mbps)", "disable-downscaling": "禁用降低分辨率", "force-mono-audio": "强制使用单声道音频", "fill-video-space": "裁剪视频以填满空间", "vertical-aspect-ratio": "竖屏视频模式", "learn-more-about-params": "了解有关 URL 参数的详细信息，请访问 ", "add-a-label": "添加标签", "mute": "静音", "send-direct-chat": "发送私信", "close": "关闭", "send-message": "发送信息", "forward-to-room": "转发到房间", "disconnect-guest": "断开嘉宾连接", "voice-chat": " 语音聊天", "solo-video": "突出显示嘉宾视频", "mute-video-guest": "关闭嘉宾视频", "toggle-remote-speaker": "使嘉宾听不到", "hide-guest": "隐藏嘉宾", "toggle-remote-display": "使嘉宾看不见", "add-to-scene": "添加到场景1", "scene-options": "场景选项", "add-to-scene2": "添加到场景2", "mute-scene": "场景静音", "stats-remote": " 场景统计信息", "additional-controls": "额外控制项", "order-down": "<i class=\"las la-minus\"></i>", "create-timer": "创建计时器", "record-local": " 本地录制", "record-remote": " 远程录制", "google-drive-record": "使用 Google Drive 录制", "change-params": "修改 URL 参数", "change-url": "更改 URL", "request-upload": " 请求上传文件", "mirror-guest": " 镜像嘉宾视频", "force-keyframe": "强制关键帧以修复拖影", "advanced-audio-settings": "<i class=\"las la-sliders-h\"></i> 音频设置", "advanced-camera-settings": "<i class=\"las la-sliders-h\"></i> 视频设置", "user-raised-hand": "用户举手", "remove-from-queue": "激活嘉宾", "record-director-local": " 本地录制导演视角", "solo-video-director": "突出显示", "video-source": " 视频源 ", "max-resolution": "最大分辨率", "balanced": "均衡模式", "smooth-cool": "流畅顺滑", "select-audio-source": " 选择音频源 ", "select-output-source": " 选择音频输出 ", "select-avatar-image": " 选择默认头像 / 占位图 ", "select-digital-effect": " 选择数字视频效果: ", "no-effects-applied": "未应用效果", "blurred-background": "模糊背景", "blurred-background-2": "模糊背景 2 🧪", "digital-greenscreen": "数字绿幕", "virtual-background": "虚拟背景", "face-mesh": "面部网格（缓慢加载）", "digital-zoom": "数字变焦", "anonymous-mask": "匿名面具", "dog-face": "狗狗面孔", "face-tracker": "面部追踪器", "close-settings": "关闭设置", "user": "用户", "hold-to-talk": "按住说话", "clear": "清除", "enable": "启用", "stop": "停止", "cycle-between-audio-visualizations": "循环切换音频可视化样式", "cleaer-sites-local-storage": "清除网站的本地浏览器存储和保存的设置", "open-in-new-tab": "在新标签页中打开", "copy-to-clipboard": "复制到剪贴板", "edit-url": "编辑 URL", "publish-url": "通过 WHIP 发布", "show-qr-code": "显示二维码", "open-ss-in-new-tab": "在新标签页中分享", "ss-mode-1": "屏幕共享模式 1", "ss-mode-2": "屏幕共享模式 2", "ss-mode-3": "屏幕共享模式 3", "detach-clock2-pip": "弹出时钟切换", "mirror-video": "视频镜像", "show-controls-video": "显示视频控制栏", "hide-controls-video": "隐藏视频控制栏", "picture-in-picture": "画中画模式", "picture-in-picture-all": "所有窗口画中画", "full-window": "全窗口模式", "shrink-window": "缩小窗口", "pause-stream": "暂停流", "resume-stream": "恢复流", "record-to-disk": "录制到磁盘", "stop-record-to-disk": "停止录制", "copy-to-clipboard-frame": "截图到剪贴板", "save-current-frame": "保存当前帧到磁盘", "show-video-stats": "显示视频统计信息", "custom-audio-output": "自定义音频输出", "remote-hangup-connection": "远程挂断连接", "remote-reload-connection": "远程刷新页面", "change-playout-buffer": "缓冲区大小（毫秒）: ", "hold-ctrl": "提示: <b>CTRL</b>(⌘) + <b>点击</b> 打开替代菜单", "change-room-settings": "更改房间设置", "change-room-video-quality": "更改房间视频质量: ", "limit-total-bitrate-quality": "你的最大允许总视频上传带宽: ", "highlight-director-only-video-guests-will-see": "突出显示导演（仅视频嘉宾将看到）", "create-global-timer": "创建全局倒计时计时器", "create-clock-timer": "切换房间时钟", "allow-for-remote-co-directors": "允许远程联合导演", "allow-co-directors-to-transfer-guests": "允许联合导演转移嘉宾", "allow-co-directors-to-change-a-guests-url": "允许联合导演更改嘉宾的URL", "basic-co-director-invite-link": "基础联合导演邀请链接: ", "local-global-record-start": "本地录制 - 开始所有", "local-global-record-stop": "本地录制 - 停止所有", "remote-global-record": "远程录制 - 停止所有", "buffer-settings": "缓冲设置", "publish-settings": "发布设置", "remote-control-obs-menu": "OBS Studio 远程控制菜单", "apply-new-guest-settings": "应用新嘉宾设置", "cancel": "取消", "invisible-guests": "嘉宾不可见", "select-local-image": "选择本地图片", "available-languages": "可用语言: ", "add-more-here": "在这里添加更多！", "add-to-calendar": "添加到日历: ", "add-to-google-calendar": "添加到谷歌日历", "add-to-outlook-calendar": "添加到 Outlook 日历", "add-to-yahoo-calendar": "添加到雅虎日历", "reload-page": "刷新页面", "add-group-chat": "创建群聊房间", "rooms-allow-for": "房间允许群聊和管理多个嘉宾", "room-name": "房间名称", "password-input-field": "输入密码", "guests-only-see-director": "嘉宾仅看到导演视频", "scenes-can-see-director": "导演作为参与者", "default-codec-select": "选择默认视频编解码器: ", "enter-the-rooms-control": "进入房间的控制中心", "show-tips": "告诉我一些提示..", "added-notes": "\n<u>\n<i>重要提示：</i><br><br>\n</u>\n<li>禁用嘉宾之间的视频共享将提高性能。</li>\n<li>只邀请嘉宾到你信任的房间。</li>\n<li>“录制”选项被认为是实验性的。</li>\n<li><a href=\"https://params.vdo.ninja\" style=\"color: black;\"><u>高级 URL 参数可用于自定义房间。</u></a></li><a href=\"https://params.vdo.ninja\" style=\"color: black;\"><u>\n</u></a>", "looking-to-just-chat-and-not-direct": "只想聊天而不想管理?", "join-the-room-basic": "以参与者身份加入房间", "back": "返回", "add-your-camera": "添加您的摄像头", "ask-for-permissions": "请求访问摄像头/麦克风权限", "start": "开始", "privacy-disabled": "隐私警告：如果您继续，导演将能够远程访问您的摄像头和麦克风。", "for-the-best-possible-experience-make-sure": "为了获得最佳体验，请确保", "your-device-is-powered": "您的设备已接通电源", "your-connection-is-hardwired-instead-of-wifi": "您的连接是通过有线而非Wi-Fi", "you-are-using-headphones-earphones": "您正在使用耳机/耳塞", "up-to-4k": "最高可达4K", "no-audio": "无音频", "add-a-password": " 添加密码: ", "use-chrome-instead": "请考虑使用基于 Chromium 的浏览器。<br>\n Safari 更容易出现音频问题", "update-your-device": "我们检测到您正在使用旧版本的Apple iOS，该版本已知存在许多问题。<br><br>请考虑进行更新。", "add-your-microphone": "将您的麦克风添加到OBS", "remote-screenshare-obs": "OBS远程屏幕共享", "select-screen-to-share": "选择要共享的屏幕", "audio-sources": "音频源", "application-audio-capture": "有关特定于应用程序的音频捕获，<a href=\"https://docs.vdo.ninja/audio\" style=\"color: #007AC8;\">请参见此处</a>", "1080p-screen-capture-guide": "要实现1080p60游戏捕获， <a href=\"https://docs.vdo.ninja/guides/how-to-screen-share-in-1080p\" style=\"color: #007AC8;\" target=\"_blank\">请参见此处</a>", "create-reusable-invite": "创建可重复使用的邀请", "here-you-can-pre-generate": "在这里你可以预先生成一个可重用的浏览器源链接和相关的嘉宾邀请链接", "generate-invite-link": "生成邀请链接", "quality-paramaters": "质量参数", "force-vp9-video-codec": "强制使用 VP9 视频编解码器", "enable-stereo-and-pro": "启用立体声和专业高清音频", "video-resolution": "视频分辨率: ", "general-paramaters": "用户参数", "allow-effects-invite": "允许使用视频效果", "hide-mic-selection": "隐藏麦克风选择", "hide-screen-share": "隐藏屏幕共享选项", "obfuscate_url": "混淆邀请 URL", "add-a-password-to-stream": " 为流媒体添加密码: ", "interview-paramaters": "双向聊天", "generate-host-link": "为主持人创建链接", "add-the-guest-to-a-room": " 将嘉宾添加到房间: ", "invite-group-chat-type": "这个房间的嘉宾可以: ", "can-see-and-hear": "可以看到和听到群聊", "can-hear-only": "只能听到群聊", "cant-see-or-hear": "无法听到或看到群聊", "share-local-video-file": "共享本地视频文件", "select-the-video-files-to-share": "选择要共享的视频文件", "share-website-iframe": "分享网站", "enter-the-website-URL-you-wish-to-share": "输入要共享的 URL 网站", "run-a-speed-test": "进行速度测试", "try-the-mixer-out": "尝试使用混音器", "try-out-versus-cam": "多流监看", "voice-comms-app": "语音通讯应用", "read-the-guides": "阅读指南", "wizard-link-generator": "向导式链接生成器", "get-full-documentation": "完整文档", "get-the-source-code": "源代码", "show-your-support": "表达您的支持", "publish-via-whip": "通过WHIP发布", "share-whepsrc": "通过WHEP共享", "enter-the-whep-URL-you-wish-to-share": "输入您希望分享的WHEP URL", "info-blob": "\n<h2>什么是VDO.Ninja</h2>\n<br>\n<li>100% <b>免费</b>；无需下载；不收集个人数据；无需登录</li>\n<li>将来自您的智能手机、远程计算机或朋友的实时视频直接传入 OBS 或其他工作室软件。</li>\n<li>我们使用尖端的 P2P 转发技术，提供隐私保护和超低延迟</li>\n<br>\n<li>Youtube视频 \n<i class=\"lab la-youtube\"></i>\n<a href=\"https://www.youtube.com/watch?v=QaA_6aOP9z8&amp;list=PLWodc2tCfAH1WHjl4WAOOoRSscJ8CHACe&amp;index=1\" alt=\"Youtube video demoing VDO.Ninja\">在这里演示</a>\n</li>\n<br><h3>\n   🛠 获取支持，请参阅 <a href=\"https://www.reddit.com/r/VDONinja/\">sub-reddit <i class=\"lab la-reddit-alien\"></i></a> 或加入 <a href=\"https://discord.vdo.ninja/\">Discord <i class=\"lab la-discord\"></i></a>。文档位于 <a href=\"https://docs.vdo.ninja/\">这里</a> ，我的个人邮箱是 <i><EMAIL></i>\n</h3> \n\n", "guest-1": "嘉宾 1", "guest-2": "嘉宾 2", "guest-3": "嘉宾 3", "guest-4": "嘉宾 4", "waiting-for-camera": "等待摄像头加载", "local-global-record": "本地录制 - 停止所有", "ok": "✔ 确定", "join-room": "加入房间", "join-room-with-mic": "使用麦克风加入房间", "join-room-with-mic-only": "仅使用麦克风加入", "join-room-with-camera": "使用摄像头加入房间", "join-room-with-video": "带视频加入房间", "share-screen-with-room": "与房间共享屏幕", "share-your-mic": "分享您的麦克风", "share-your-camera": "分享您的摄像头", "share-your-screen": "分享您的屏幕", "click-start-to-join": "点击“开始”加入", "waiting-for-mic-to-load": "等待麦克风加载", "waiting-for-camera-to-load": "等待摄像头加载", "push-to-talk-enable": " 启用导演的麦克风或视频<br>（只有嘉宾看得到这个提醒）", "low-cpu=broadcast-codec": "低 CPU 使用的广播编解码器", "mute-guest": "静音嘉宾", "More-scene-options": "更多场景选项", "unmute": "取消静音", "unhide-guest": "取消隐藏嘉宾", "undeafen": "开启声音", "unblind": "开启视频", "advanced": "高级 ", "advanced-paramaters": "高级参数", "allow-remote-control": "允许远程控制摄像头变焦（适用于安卓）", "more-than-four-can-join": "这四个嘉宾位置仅用于演示。一个房间可以容纳四位以上的嘉宾。", "toggle-control-video": "切换视频控制", "chrome-cast": "投射..", "please-select-option-to-join": "请选择一个加入选项。", "guest-toggle": "嘉宾切换", "settings": "设置", "more": "更多", "note-share-audio": "\n\t<b>注意</b>: Nezapomeňte zakliknout \"Sdílet audio\" v Chromu.<br>(Firefox不支持音频分享。)", "record": "录制", "change-to-low-quality": "&nbsp;&nbsp;<i class=\"las la-video-slash\"></i>", "change-to-medium-quality": "&nbsp;&nbsp;<i class=\"las la-video\"></i>", "change-to-high-quality": "&nbsp;&nbsp;<i class=\"las la-binoculars\"></i>", "click-for-quick-room-overview": "\n<i class=\"las la-question-circle\"></i> <span data-translate=\"click-here-for-help\">单击此处获取快速概览和帮助</span>\n", "names-and-labels-coming-soon": "\n\t成员名称将是VDO.Ninja未来的功能之一。\n", "order-up": "<i class=\"las la-plus\"></i>"}, "placeholders": {"join-by-room-name-here": "在此使用房间名称加入", "load-a-website-url": "加载一个网站 URL", "enter-chat-message-to-send-here": "在此处输入要发送的聊天消息", "enter-your-message-here": "在此输入您的消息", "press-a-key-here": "在此按下一个键", "enter-a-url-for-the-sidebar-page": "为边栏页面输入一个URL", "-whip-url-to-publish-to-goes-here": "➡️ 在此输入WHIP URL以发布", "-authentication-bearer-token-optional-": "🗝️ 认证Bearer令牌（可选）", "enter-the-remote-obs-password-here": "在此输入远程OBS密码", "enter-the-room-name-here": "在此输入房间名称", "enter-the-room-password-here": "在此输入房间密码", "enter-a-room-name-here": "在此输入一个房间名称", "optional-room-password-here": "在此输入可选的房间密码", "optional": "可选", "give-this-media-source-a-name-optional-": "为该媒体源命名 (可选的)", "add-an-optional-password": "添加一个可选密码", "enter-room-name-here": "在此处输入房间名称"}, "miscellaneous": {"new-display-name": "为流输入新的显示名称", "submit-error-report": "按\"确定\"提交任何错误日志。错误日志可能包含私人信息。", "director-redirect-1": "导演希望将您重定向到 URL: ", "director-redirect-2": "\n\n按\"确定\"进行重定向。", "audio-processing-disabled": "此嘉宾已禁用音频处理。无法静音或更改音量", "not-the-director": "<font color='red'>您不是这个房间的导演。您将无法控制。参见 <a target='_blank' href='https://docs.vdo.ninja/director-settings/codirector'>&codirector</a> 关于如何成为联合导演。</font>", "room-is-claimed": "这个房间已经有人认领了。\n\n只有第一个加入房间的人是指定的导演。\n\n在第一个导演离开后刷新以进行认领。", "token-room-is-claimed": "房间已被其他人认领。\n\n请作为嘉宾或联合房间管理员加入。", "room-is-claimed-codirector": "这个房间已经有人认领了。\n\n正在尝试以联合导演的身份加入...", "streamid-already-published": "您要发布到的流 ID 已在使用中。\n\n请尝试使用其他邀请链接或刷新以重试。\n\n您现在将断开连接。", "director": "导演", "unknown-user": "未知用户", "room-test-not-good": "房间名称“test”非常常用，可能不安全。\n\n您确定要继续吗？", "load-previous-session": "是否要加载上一个会话的设置？", "enter-password": "请在下面输入密码: \n\n（注意：密码区分大小写，如果密码不正确，将不会提醒您。）", "enter-password-2": "请在下面输入密码: \n\n（注意：密码区分大小写。）", "enter-director-password": "请输入导演的密码: \n\n（注意：密码区分大小写，如果密码不正确，将不会提醒您。）", "password-incorrect": "密码不正确.\n\n请刷新并重试.", "enter-display-name": "请输入您的显示名称: ", "enter-new-display-name": "为此流创建一个新名称", "what-bitrate": "您希望以什么比特率录制？(kbps)\n（注意：此功能是实验性的，因此需要备份录制）", "enter-website": "输入要共享的网站 URL", "press-ok-to-record": "按\"确定\"开始录制。再次按下可停止并下载。\n\n警告：保持此浏览器标签页处于活动状态以继续录制。\n\n如果需要，您可以在下面更改默认视频比特率 (kbps)", "no-streamID-provided": "没有提供流 ID；将随机生成一个。\n\n流 ID: ", "alphanumeric-only": "信息：流 ID 只能使用字母数字字符。\n\n有问题的字符已替换为下划线", "stream-id-too-long": "流 ID 的长度应小于 45 个字母数字字符。\n\n我们将修剪它的长度。", "share-with-trusted": "只与您信任的人分享", "pass-recommended": "建议使用密码", "insecure-room-name": "不安全的房间名称。", "allowed-chars": "允许的字符", "transfer": "转移", "armed": "已激活", "transfer-guest-to-room": "将嘉宾转移到另一个房间：\n\n（请注意，目标房间必须使用相同的密码）", "transfer-guest-to-url": "将嘉宾转移到新的网站 URL。\n\n（嘉宾将会收到接受提示）", "mute-in-scene": "在场景中静音", "unmute-guest": "取消嘉宾静音", "deafen": "使嘉宾听不到", "blind": "使嘉宾看不见", "unhide": "取消隐藏嘉宾", "confirm-disconnect-users": "您确定要断开这些用户的连接吗？", "confirm-disconnect-user": "您确定要断开此用户的连接吗？", "enter-new-codirector-password": "输入要使用的联合导演密码", "control-room-co-director": "控制房间：联合导演", "volume-control": "仅限本地回放的音量控制", "signal-meter": "视频预览的视频数据包丢失指示器。绿色表示良好，红色表示有问题。火焰图标表示CPU过载。这可能不反映场景或其他嘉宾看到的数据包丢失情况。", "waiting-for-the-stream": "等待流。提示：将 &cleanoutput 添加到 URL 将隐藏此微调器，或者单击以重试，这也将隐藏它。", "main-director": "主要导演", "share-a-screen": "分享屏幕", "stop-screen-sharing": "停止屏幕共享", "you-have-been-transferred": "您已被转移到其他房间", "you-have-been-activated": "房间管理员现在允许您看到房间内的其他人", "you-are-no-longer-a-co-director": "您不再是联合导演，因为您已被转移。", "transferred": "已转移", "room-changed": "您的房间已改变", "headphones-tip": "<i>提示：</i> 使用耳机以避免音频回音问题。", "camera-tip-c922": "<i>提示：</i> 要想用C922摄像头达到60fps，需要关闭低光补偿，将曝光设置为自动，并使用720p分辨率。", "camera-tip-camlink": "<i>提示：</i> 如果Cam Link在已经使用的情况下被其他程序访问，可能会出现绿色/紫色故障。", "samsung-a-series": "三星A系列手机在Chrome上可能会有问题；如果是这样，尝试使用Firefox Mobile，或者更换视频编解码器。", "screen-permissions-denied": "屏幕捕获权限被拒绝。请确保您的浏览器具有屏幕录制系统权限。\n\n1.在Mac上，选择苹果菜单 > 系统偏好设置，点击安全性与隐私，然后点击隐私。\n2.选择屏幕录制。\n3.勾选您的浏览器旁边的复选框以允许它录制您的屏幕。", "change-audio-output-device": "无法捕获音频。请确保您有一个可用的音频输出设备。\n\n一些游戏耳机（如：Corsair）可能需要设置为2声道输出才能工作，因为环绕声驱动可能会导致问题。", "prompt-access-request": " 正在尝试观看您的流。允许他们吗？", "confirm-reload-user": "您确定要重新加载该用户的浏览器吗？", "webrtc-is-blocked": "⚠ 此浏览器已经阻止了WebRTC或者不支持它。\n\n没有WebRTC，本站无法正常工作。\n\n禁用可能阻止WebRTC的任何浏览器扩展或隐私设置，或尝试使用其他浏览器。", "not-clean-session": "视频效果或画布渲染失败。\n\n请检查以确保任何远程托管的图片都被允许跨源访问。", "ios-no-screen-share": "很抱歉，您的iOS浏览器不支持屏幕共享。\n\n请参见 <a href='https://docs.vdo.ninja/guides/screen-share-your-iphone-ipad' target='_blank'>这个指南</a> ，了解替代方法。", "android-no-screen-share": "很抱歉，您的移动浏览器不支持屏幕共享。\n\n不过， <a href='https://docs.vdo.ninja/getting-started/native-mobile-app-versions' target='_blank'>Android原生应用</a> 确实提供了基本的支持。", "no-screen-share-supported": "很抱歉，您的浏览器不支持屏幕共享。\n\n请改用桌面版的Firefox或Chrome。", "speech-not-suppoted": "⚠ 该浏览器不支持语音识别", "blue-yeti-tip": "<i>提示: </i> Blue Yeti麦克风可能会出现音量过大的问题。 <a href='https://support.google.com/chrome/thread/7542181?hl=en&msgid=79691143'>请查看这里</a> 的解决方案，或在VDO.Ninja中禁用自动增益。", "site-not-responsive": "<h3>注意：系统无法访问或当前响应缓慢。</h3>\n如果是路由问题，尝试在URL中添加 <i title='or try visiting https://proxy.vdo.ninja/'>&proxy</i> ；如果该服务在您的国家被屏蔽，您也可以尝试使用 <i>https://proxy.vdo.ninja</i> 或VPN。\n\n如果主服务宕机，这里也提供了一个备份版本：<i>https://backup.vdo.ninja</i>\n\n如需进一步帮助，请联系******************。\n\n该服务需要通过443端口使用Websockets。", "no-audio-source-detected": "未检测到音频源。\n\n如果您想捕获应用程序的音频，请参见：\nhttps://docs.vdo.ninja/help/guides-and-how-tos#audio 音频指南", "viewer-count": "此远程流的总出站p2p连接数", "enter-url-for-widget": "输入一个网页URL，以嵌入为侧边栏", "director-password": "输入主导演的密码", "vision-disabled": "导演暂时禁用了您的视觉<br /><br ><center><i style='font-size: 500%;' class='las la-eye-slash'></i></center>", "invalid-remote-code": "无效的远程控制代码。\n\n使用下面的字段尝试使用不同的密码。", "invalid-remote-code-obs": "无效的远程控制代码。\n\n远程OBS系统需要使用 &remote 设置匹配的密码。\n\n参见文档获取帮助。", "request-rejected-obs": "请求被拒绝。\n\n远程OBS系统需要使用 &remote 设置匹配的密码。\n\n参见文档获取帮助。", "remote-token-rejected": "远程请求失败；&remote令牌不匹配或远程用户不允许远程控制。", "remote-control-failed": "远程控制请求失败。", "remote-peer-connected": "远程对端已连接至视频流。\n\n应请求销毁与握手服务器的连接。这提高了安全性，但在连接失败时对端将无法自动重连。\n\n点击确定开始流媒体！", "director-denied": "主导演拒绝了您作为联合导演的请求", "only-main-director": "只有主导演可以转移这位嘉宾", "request-failed": "请求失败；您不能执行此操作", "tokens-did-not-match": "远程请求失败；远程令牌不匹配或远程用户不允许远程控制。", "token-not-director": "请求失败；远程用户未将您识别为导演", "approved-as-director": "导演批准您作为联合导演", "you-are-a-codirector": "您是此房间的联合导演；您被分配了部分导演控制权。", "this-is-you": "这是您，一位联合导演。<br />您同时也是表演者。", "preview-meshcast-disabled": "您无法调整基于Meshcast的流的预览比特率", "co-director": "联合导演", "you-not-yet-activated": "请等待导演将您带入房间", "sample-rate-too-high": "您的音频播放设备的采样率设置得非常高。如果遇到音频问题，尝试使用48-kHz。", "no-network": "网络连接丢失 🤷‍♀️❌📶", "no-network-details": "网络连接丢失。 🤷‍♀️❌📶\n\n您的互联网连接是否已丢失？", "enter-password-if-desired": "如果提供了密码，请输入，否则直接点击取消", "your-screenshare": "您的屏幕共享", "your-camera": "您的摄像头", "accept-inbound-caller": "接受入站电话呼叫者吗？", "disable-video": "禁用视频", "show-more-options": "显示更多选项", "system-default": "系统默认"}}