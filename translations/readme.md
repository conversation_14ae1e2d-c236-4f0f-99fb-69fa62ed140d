Welcome to the translation / language section of VDO.Ninja

You can specify a translation using this code, if deploying the code yourself. blank can be replaced with ru, en, pt, etc..
```
<script type="text/javascript" id="main-js" src="./main.js" data-translation="blank"></script>
```

You can also add &ln=ru to the URL as a parameter to launch the translation that way.

There is a file called translate.js, which if you copy/paste the content (and hit enter) into the Chrome browser's console, while on VDO.Ninja, it will download the translation files.
It will add any new translation entries and add them to the bottom of the files. Please feel to correct these new translations and upload them back to github as a pull request.

In the future I will add a Github action so that this automatically occurs with new code posting.

Translation files can contain more than just language; they can contain HTML and image links, etc. This offers a basic amount of customization.

the "blank.json" file contains a minimal template.  Little to no VDO.Ninja branding.

