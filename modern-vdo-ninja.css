/* VDO.Ninja Modern UI Framework */
/* Glassmorphism and Modern Design System */

:root {
  /* Modern Color Palette */
  --primary: #1a1a2e;
  --primary-light: #16213e;
  --secondary: #0f3460;
  --accent: #e94560;
  --accent-light: #f16d7a;
  --success: #00d4aa;
  --warning: #ffa726;
  --error: #ef5350;
  --info: #29b6f6;
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b0b0b0;
  --text-muted: #888888;
  --text-dark: #2c2c2c;
  
  /* Glass Effects */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-bg-strong: rgba(255, 255, 255, 0.15);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary), var(--primary-light));
  --gradient-accent: linear-gradient(135deg, var(--accent), var(--accent-light));
  --gradient-success: linear-gradient(135deg, var(--success), #00b894);
  --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;
  
  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.25);
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Z-index layers */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Base Modern Theme */
.modern-theme {
  background: var(--gradient-primary);
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  min-height: 100vh;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.modern-theme * {
  box-sizing: border-box;
}

/* Modern Typography */
.modern-theme h1,
.modern-theme h2,
.modern-theme h3,
.modern-theme h4,
.modern-theme h5,
.modern-theme h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.modern-theme h1 { font-size: 2.5rem; }
.modern-theme h2 { font-size: 2rem; }
.modern-theme h3 { font-size: 1.75rem; }
.modern-theme h4 { font-size: 1.5rem; }
.modern-theme h5 { font-size: 1.25rem; }
.modern-theme h6 { font-size: 1rem; }

/* Modern Navigation */
.modern-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modern-nav-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.modern-nav-brand:hover {
  color: var(--accent);
  transition: var(--transition-fast);
}

.modern-nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.version-switcher {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.version-switch {
  color: var(--accent);
  text-decoration: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

.version-switch:hover {
  background: var(--glass-bg);
  color: var(--accent-light);
}

/* Modern Container System */
.modern-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.modern-container-fluid {
  width: 100%;
  padding: 0 var(--spacing-lg);
}

/* Room Section Base */
.room-section {
  padding: var(--spacing-xxl) var(--spacing-lg);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.room-section.with-nav {
  padding-top: calc(var(--spacing-xxl) + 80px); /* Account for fixed nav */
}

/* Modern Card System */
.modern-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin: var(--spacing-md) 0;
  box-shadow: var(--glass-shadow);
  transition: var(--transition-normal);
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  border-color: rgba(255, 255, 255, 0.3);
}

.modern-card-header {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--glass-border);
}

.modern-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.modern-card-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: var(--spacing-xs) 0 0 0;
}

.modern-card-body {
  flex: 1;
}

.modern-card-footer {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--glass-border);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

/* Modern Button System */
.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  min-height: 44px; /* Touch-friendly */
  position: relative;
  overflow: hidden;
}

.modern-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: var(--transition-normal);
}

.modern-btn:hover:before {
  left: 100%;
}

.modern-btn-primary {
  background: var(--gradient-accent);
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
}

.modern-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.modern-btn-secondary {
  background: var(--glass-bg);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
}

.modern-btn-secondary:hover {
  background: var(--glass-bg-strong);
  border-color: rgba(255, 255, 255, 0.3);
}

.modern-btn-success {
  background: var(--gradient-success);
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
}

.modern-btn-success:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.modern-btn-error {
  background: linear-gradient(135deg, var(--error), #d32f2f);
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
}

.modern-btn-error:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.modern-btn-warning {
  background: linear-gradient(135deg, var(--warning), #f57c00);
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
}

.modern-btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.modern-btn-outline {
  background: transparent;
  color: var(--accent);
  border: 2px solid var(--accent);
}

.modern-btn-outline:hover {
  background: var(--accent);
  color: var(--text-primary);
}

.modern-btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 0.75rem;
  min-height: 36px;
}

.modern-btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 1rem;
  min-height: 52px;
}

.modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Modern Form Controls */
.modern-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 1rem;
  transition: var(--transition-fast);
  backdrop-filter: blur(10px);
  min-height: 44px;
}

.modern-input:focus {
  outline: none;
  border-color: var(--accent);
  box-shadow: 0 0 0 3px rgba(233, 69, 96, 0.1);
  background: var(--glass-bg-strong);
}

.modern-input::placeholder {
  color: var(--text-muted);
}

.modern-select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition-fast);
  backdrop-filter: blur(10px);
  min-height: 44px;
}

.modern-select:focus {
  outline: none;
  border-color: var(--accent);
  box-shadow: 0 0 0 3px rgba(233, 69, 96, 0.1);
}

.modern-form-group {
  margin-bottom: var(--spacing-lg);
}

.modern-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.modern-form-row {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.modern-form-col {
  flex: 1;
}

/* Modern Grid System */
.modern-grid {
  display: grid;
  gap: var(--spacing-md);
}

.modern-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.modern-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.modern-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.modern-grid-auto {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Video Grid Modern Styling */
.modern-video-grid {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  padding: var(--spacing-md);
}

.modern-video-container {
  background: var(--secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
  aspect-ratio: 16/9;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
}

.modern-video-container:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-lg);
}

.modern-video-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modern-video-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
  padding: var(--spacing-md);
  color: var(--text-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modern-video-name {
  font-weight: 500;
  font-size: 0.875rem;
}

.modern-video-controls {
  display: flex;
  gap: var(--spacing-xs);
}

.modern-video-control-btn {
  background: var(--glass-bg);
  border: none;
  border-radius: var(--radius-full);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition-fast);
}

.modern-video-control-btn:hover {
  background: var(--glass-bg-strong);
  transform: scale(1.1);
}

/* Landing Page Styles */
.modern-landing {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-xxl);
}

.hero-container {
  margin-bottom: var(--spacing-xxl);
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  background: linear-gradient(135deg, var(--text-primary), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-md);
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
}

.action-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  width: 100%;
  max-width: 800px;
}

.action-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.action-card:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: rgba(255, 255, 255, 0.3);
}

.action-card-icon {
  font-size: 3rem;
  color: var(--accent);
  margin-bottom: var(--spacing-md);
}

.action-card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.action-card-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

/* Director Dashboard Styles */
.modern-director {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: var(--spacing-lg);
  height: 100vh;
  padding: var(--spacing-md);
}

.director-sidebar {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.director-main {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  overflow: hidden;
}

.participants-panel {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.participant-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--glass-bg);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  transition: var(--transition-fast);
}

.participant-item:hover {
  background: var(--glass-bg-strong);
}

.participant-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.participant-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: var(--gradient-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-weight: 600;
}

.participant-name {
  font-weight: 500;
  color: var(--text-primary);
}

.participant-status {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.participant-controls {
  display: flex;
  gap: var(--spacing-xs);
}

/* Guest/Join Interface Styles */
.modern-guest {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 600px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

.device-setup-card {
  width: 100%;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

.device-preview {
  width: 100%;
  aspect-ratio: 16/9;
  background: var(--secondary);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
  position: relative;
}

.device-preview video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.device-preview-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  font-size: 1.125rem;
}

/* Communications Interface Styles */
.modern-comms {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 0;
}

.comms-header {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.comms-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.comms-video-area {
  flex: 1;
  background: var(--primary);
  position: relative;
}

.comms-sidebar {
  width: 300px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-left: 1px solid var(--glass-border);
  display: flex;
  flex-direction: column;
}

.comms-controls {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-top: 1px solid var(--glass-border);
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.control-btn {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  cursor: pointer;
  transition: var(--transition-fast);
}

.control-btn-mute {
  background: var(--glass-bg);
  color: var(--text-primary);
}

.control-btn-mute.active {
  background: var(--error);
  color: var(--text-primary);
}

.control-btn-video {
  background: var(--glass-bg);
  color: var(--text-primary);
}

.control-btn-video.active {
  background: var(--error);
  color: var(--text-primary);
}

.control-btn-screen {
  background: var(--glass-bg);
  color: var(--text-primary);
}

.control-btn-screen.active {
  background: var(--success);
  color: var(--text-primary);
}

.control-btn-hangup {
  background: var(--error);
  color: var(--text-primary);
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

.status-online {
  background: rgba(0, 212, 170, 0.2);
  color: var(--success);
  border: 1px solid var(--success);
}

.status-offline {
  background: rgba(239, 83, 80, 0.2);
  color: var(--error);
  border: 1px solid var(--error);
}

.status-connecting {
  background: rgba(255, 167, 38, 0.2);
  color: var(--warning);
  border: 1px solid var(--warning);
}

/* Loading States */
.modern-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--glass-border);
  border-top: 3px solid var(--accent);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-left: var(--spacing-md);
  color: var(--text-secondary);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.d-grid { display: grid; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.align-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .modern-nav {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .modern-nav-brand {
    font-size: 1.25rem;
  }

  .version-switcher {
    display: none; /* Hide on mobile to save space */
  }

  .room-section {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .room-section.with-nav {
    padding-top: calc(var(--spacing-lg) + 60px);
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .action-cards {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .action-card {
    padding: var(--spacing-lg);
  }

  .modern-director {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    height: auto;
    min-height: 100vh;
  }

  .director-sidebar {
    order: 2;
    margin-top: var(--spacing-md);
  }

  .director-main {
    order: 1;
  }

  .modern-video-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
  }

  .modern-guest {
    padding: var(--spacing-md);
  }

  .device-setup-card {
    padding: var(--spacing-lg);
  }

  .comms-main {
    flex-direction: column;
  }

  .comms-sidebar {
    width: 100%;
    height: 200px;
    border-left: none;
    border-top: 1px solid var(--glass-border);
  }

  .comms-controls {
    padding: var(--spacing-sm) var(--spacing-md);
    gap: var(--spacing-sm);
  }

  .control-btn {
    width: 44px;
    height: 44px;
    font-size: 1.125rem;
  }

  .modern-form-row {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .modern-grid-2,
  .modern-grid-3,
  .modern-grid-4 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .action-cards {
    gap: var(--spacing-md);
  }

  .modern-card {
    padding: var(--spacing-md);
  }

  .modern-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
  }

  .modern-btn-lg {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 0.875rem;
  }
}
