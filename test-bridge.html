<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VDO.Ninja Bridge Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1f2e;
            color: white;
        }
        .test-section {
            background: #2a2f3e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3b82f6;
        }
        input {
            background: #1a1f2e;
            color: white;
            border: 1px solid #4a9eff;
            padding: 8px;
            border-radius: 4px;
            margin: 5px;
        }
        .result {
            background: #0a0e1a;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>VDO.Ninja Modern UI Bridge Test</h1>
    
    <div class="test-section">
        <h2>Bridge Initialization</h2>
        <button onclick="testInitialization()">Test Bridge Initialization</button>
        <div id="init-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Room Creation Test</h2>
        <input type="text" id="room-name" placeholder="Room name (leave empty for random)">
        <input type="password" id="room-password" placeholder="Room password (optional)">
        <br>
        <button onclick="testCreateRoom()">Test Create Room</button>
        <div id="create-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Room Joining Test</h2>
        <input type="text" id="join-room-id" placeholder="Room ID to join">
        <input type="text" id="display-name" placeholder="Display name">
        <br>
        <button onclick="testJoinRoom()">Test Join Room</button>
        <div id="join-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Utility Functions Test</h2>
        <button onclick="testSanitizeRoomName()">Test Sanitize Room Name</button>
        <button onclick="testGenerateString()">Test Generate Random String</button>
        <div id="utils-result" class="result"></div>
    </div>

    <!-- Import ALL original VDO.Ninja scripts for functionality preservation -->
    <script src="./thirdparty/adapter.js"></script>
    <script src="./main.js"></script>
    <script src="./webrtc.js"></script>
    
    <!-- Bridge Script -->
    <script src="./app-bridge.js"></script>
    
    <script>
        function testInitialization() {
            const result = document.getElementById('init-result');
            try {
                result.textContent = `Bridge initialized: ${window.vdoNinjaBridge.isInitialized}
Available functions: ${Object.keys(window.vdoNinjaBridge.originalFunctions).join(', ')}
Modern UI state: ${JSON.stringify(window.vdoNinjaBridge.modernUI, null, 2)}`;
            } catch (error) {
                result.textContent = `Error: ${error.message}`;
            }
        }
        
        function testCreateRoom() {
            const result = document.getElementById('create-result');
            const roomName = document.getElementById('room-name').value;
            const roomPassword = document.getElementById('room-password').value;
            
            try {
                const settings = {
                    roomName: roomName,
                    roomPassword: roomPassword,
                    directorMode: true,
                    hdVideo: true
                };
                
                result.textContent = `Testing room creation with settings:
${JSON.stringify(settings, null, 2)}

Calling modernCreateRoom...`;
                
                const createResult = window.vdoNinjaBridge.modernCreateRoom(settings);
                
                result.textContent += `

Result:
${JSON.stringify(createResult, null, 2)}`;
                
            } catch (error) {
                result.textContent += `

Error: ${error.message}
Stack: ${error.stack}`;
            }
        }
        
        function testJoinRoom() {
            const result = document.getElementById('join-result');
            const roomId = document.getElementById('join-room-id').value;
            const displayName = document.getElementById('display-name').value;
            
            if (!roomId) {
                result.textContent = 'Please enter a room ID to join';
                return;
            }
            
            try {
                const settings = {
                    displayName: displayName,
                    audioOnly: false,
                    startMuted: false
                };
                
                result.textContent = `Testing room joining with:
Room ID: ${roomId}
Settings: ${JSON.stringify(settings, null, 2)}

Calling modernJoinRoom...`;
                
                const joinResult = window.vdoNinjaBridge.modernJoinRoom(roomId, settings);
                
                result.textContent += `

Result:
${JSON.stringify(joinResult, null, 2)}`;
                
            } catch (error) {
                result.textContent += `

Error: ${error.message}
Stack: ${error.stack}`;
            }
        }
        
        function testSanitizeRoomName() {
            const result = document.getElementById('utils-result');
            
            const testNames = [
                'test-room-123',
                'Room With Spaces!@#',
                'special$chars%^&*()',
                '',
                'verylongroomnamethatexceedsthirtychars',
                'normal123'
            ];
            
            let output = 'Sanitize Room Name Tests:\n\n';
            
            testNames.forEach(name => {
                try {
                    const sanitized = window.vdoNinjaBridge.originalFunctions.sanitizeRoomName(name);
                    output += `"${name}" -> "${sanitized}"\n`;
                } catch (error) {
                    output += `"${name}" -> ERROR: ${error.message}\n`;
                }
            });
            
            result.textContent = output;
        }
        
        function testGenerateString() {
            const result = document.getElementById('utils-result');
            
            let output = 'Generate Random String Tests:\n\n';
            
            try {
                for (let i = 0; i < 5; i++) {
                    const randomString = window.vdoNinjaBridge.originalFunctions.generateString(10);
                    output += `Generated string ${i + 1}: "${randomString}"\n`;
                }
            } catch (error) {
                output += `ERROR: ${error.message}\n`;
            }
            
            result.textContent = output;
        }
        
        // Auto-run initialization test when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testInitialization, 1000);
        });
    </script>
</body>
</html>
