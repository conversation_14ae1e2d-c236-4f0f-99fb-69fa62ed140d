(function(root){var bundle=function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r}()({1:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});exports.TwilioError=exports.Logger=exports.PreflightTest=exports.Device=exports.Call=void 0;var call_1=require("./twilio/call");exports.Call=call_1.default;var device_1=require("./twilio/device");exports.Device=device_1.default;var TwilioError=require("./twilio/errors");exports.TwilioError=TwilioError;var log_1=require("./twilio/log");Object.defineProperty(exports,"Logger",{enumerable:true,get:function(){return log_1.Logger}});var preflight_1=require("./twilio/preflight/preflight");Object.defineProperty(exports,"PreflightTest",{enumerable:true,get:function(){return preflight_1.PreflightTest}})},{"./twilio/call":9,"./twilio/device":12,"./twilio/errors":15,"./twilio/log":18,"./twilio/preflight/preflight":20}],2:[function(require,module,exports){"use strict";var __awaiter=this&&this.__awaiter||function(thisArg,_arguments,P,generator){function adopt(value){return value instanceof P?value:new P(function(resolve){resolve(value)})}return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value))}catch(e){reject(e)}}function rejected(value){try{step(generator["throw"](value))}catch(e){reject(e)}}function step(result){result.done?resolve(result.value):adopt(result.value).then(fulfilled,rejected)}step((generator=generator.apply(thisArg,_arguments||[])).next())})};var __generator=this&&this.__generator||function(thisArg,body){var _={label:0,sent:function(){if(t[0]&1)throw t[1];return t[1]},trys:[],ops:[]},f,y,t,g;return g={next:verb(0),throw:verb(1),return:verb(2)},typeof Symbol==="function"&&(g[Symbol.iterator]=function(){return this}),g;function verb(n){return function(v){return step([n,v])}}function step(op){if(f)throw new TypeError("Generator is already executing.");while(_)try{if(f=1,y&&(t=op[0]&2?y["return"]:op[0]?y["throw"]||((t=y["return"])&&t.call(y),0):y.next)&&!(t=t.call(y,op[1])).done)return t;if(y=0,t)op=[op[0]&2,t.value];switch(op[0]){case 0:case 1:t=op;break;case 4:_.label++;return{value:op[1],done:false};case 5:_.label++;y=op[1];op=[0];continue;case 7:op=_.ops.pop();_.trys.pop();continue;default:if(!(t=_.trys,t=t.length>0&&t[t.length-1])&&(op[0]===6||op[0]===2)){_=0;continue}if(op[0]===3&&(!t||op[1]>t[0]&&op[1]<t[3])){_.label=op[1];break}if(op[0]===6&&_.label<t[1]){_.label=t[1];t=op;break}if(t&&_.label<t[2]){_.label=t[2];_.ops.push(op);break}if(t[2])_.ops.pop();_.trys.pop();continue}op=body.call(thisArg,_)}catch(e){op=[6,e];y=0}finally{f=t=0}if(op[0]&5)throw op[1];return{value:op[0]?op[1]:void 0,done:true}}};Object.defineProperty(exports,"__esModule",{value:true});exports.AsyncQueue=void 0;var deferred_1=require("./deferred");var AsyncQueue=function(){function AsyncQueue(){this._operations=[]}AsyncQueue.prototype.enqueue=function(callback){var hasPending=!!this._operations.length;var deferred=new deferred_1.default;this._operations.push({deferred:deferred,callback:callback});if(!hasPending){this._processQueue()}return deferred.promise};AsyncQueue.prototype._processQueue=function(){return __awaiter(this,void 0,void 0,function(){var _a,deferred,callback,result,error,hasResolved,e_1;return __generator(this,function(_b){switch(_b.label){case 0:if(!this._operations.length)return[3,5];_a=this._operations[0],deferred=_a.deferred,callback=_a.callback;result=void 0;error=void 0;hasResolved=void 0;_b.label=1;case 1:_b.trys.push([1,3,,4]);return[4,callback()];case 2:result=_b.sent();hasResolved=true;return[3,4];case 3:e_1=_b.sent();error=e_1;return[3,4];case 4:this._operations.shift();if(hasResolved){deferred.resolve(result)}else{deferred.reject(error)}return[3,0];case 5:return[2]}})})};return AsyncQueue}();exports.AsyncQueue=AsyncQueue},{"./deferred":11}],3:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();Object.defineProperty(exports,"__esModule",{value:true});var events_1=require("events");var device_1=require("./device");var errors_1=require("./errors");var log_1=require("./log");var outputdevicecollection_1=require("./outputdevicecollection");var mediadeviceinfo_1=require("./shims/mediadeviceinfo");var util_1=require("./util");var kindAliases={audioinput:"Audio Input",audiooutput:"Audio Output"};var AudioHelper=function(_super){__extends(AudioHelper,_super);function AudioHelper(onActiveOutputsChanged,onActiveInputChanged,options){var _a;var _this=_super.call(this)||this;_this.availableInputDevices=new Map;_this.availableOutputDevices=new Map;_this._audioConstraints=null;_this._defaultInputDeviceStream=null;_this._enabledSounds=(_a={},_a[device_1.default.SoundName.Disconnect]=true,_a[device_1.default.SoundName.Incoming]=true,_a[device_1.default.SoundName.Outgoing]=true,_a);_this._inputDevice=null;_this._isPollingInputVolume=false;_this._log=new log_1.default("AudioHelper");_this._processedStream=null;_this._selectedInputDeviceStream=null;_this._unknownDeviceIndexes={audioinput:{},audiooutput:{}};_this._updateAvailableDevices=function(){if(!_this._mediaDevices||!_this._enumerateDevices){return Promise.reject("Enumeration not supported")}return _this._enumerateDevices().then(function(devices){_this._updateDevices(devices.filter(function(d){return d.kind==="audiooutput"}),_this.availableOutputDevices,_this._removeLostOutput);_this._updateDevices(devices.filter(function(d){return d.kind==="audioinput"}),_this.availableInputDevices,_this._removeLostInput);var defaultDevice=_this.availableOutputDevices.get("default")||Array.from(_this.availableOutputDevices.values())[0];[_this.speakerDevices,_this.ringtoneDevices].forEach(function(outputDevices){if(!outputDevices.get().size&&_this.availableOutputDevices.size&&_this.isOutputSelectionSupported){outputDevices.set(defaultDevice.deviceId).catch(function(reason){_this._log.warn("Unable to set audio output devices. "+reason)})}})})};_this._removeLostInput=function(lostDevice){if(!_this.inputDevice||_this.inputDevice.deviceId!==lostDevice.deviceId){return false}_this._destroyProcessedStream();_this._replaceStream(null);_this._inputDevice=null;_this._maybeStopPollingVolume();var defaultDevice=_this.availableInputDevices.get("default")||Array.from(_this.availableInputDevices.values())[0];if(defaultDevice){_this.setInputDevice(defaultDevice.deviceId)}return true};_this._removeLostOutput=function(lostDevice){var wasSpeakerLost=_this.speakerDevices.delete(lostDevice);var wasRingtoneLost=_this.ringtoneDevices.delete(lostDevice);return wasSpeakerLost||wasRingtoneLost};options=Object.assign({AudioContext:typeof AudioContext!=="undefined"&&AudioContext,setSinkId:typeof HTMLAudioElement!=="undefined"&&HTMLAudioElement.prototype.setSinkId},options);_this._updateUserOptions(options);_this._audioProcessorEventObserver=options.audioProcessorEventObserver;_this._mediaDevices=options.mediaDevices||navigator.mediaDevices;_this._onActiveInputChanged=onActiveInputChanged;_this._enumerateDevices=typeof options.enumerateDevices==="function"?options.enumerateDevices:_this._mediaDevices&&_this._mediaDevices.enumerateDevices.bind(_this._mediaDevices);var isAudioContextSupported=!!(options.AudioContext||options.audioContext);var isEnumerationSupported=!!_this._enumerateDevices;if(options.enabledSounds){_this._enabledSounds=options.enabledSounds}var isSetSinkSupported=typeof options.setSinkId==="function";_this.isOutputSelectionSupported=isEnumerationSupported&&isSetSinkSupported;_this.isVolumeSupported=isAudioContextSupported;if(_this.isVolumeSupported){_this._audioContext=options.audioContext||options.AudioContext&&new options.AudioContext;if(_this._audioContext){_this._inputVolumeAnalyser=_this._audioContext.createAnalyser();_this._inputVolumeAnalyser.fftSize=32;_this._inputVolumeAnalyser.smoothingTimeConstant=.3}}_this.ringtoneDevices=new outputdevicecollection_1.default("ringtone",_this.availableOutputDevices,onActiveOutputsChanged,_this.isOutputSelectionSupported);_this.speakerDevices=new outputdevicecollection_1.default("speaker",_this.availableOutputDevices,onActiveOutputsChanged,_this.isOutputSelectionSupported);_this.addListener("newListener",function(eventName){if(eventName==="inputVolume"){_this._maybeStartPollingVolume()}});_this.addListener("removeListener",function(eventName){if(eventName==="inputVolume"){_this._maybeStopPollingVolume()}});_this.once("newListener",function(){if(!_this.isOutputSelectionSupported){_this._log.warn("Warning: This browser does not support audio output selection.")}if(!_this.isVolumeSupported){_this._log.warn("Warning: This browser does not support Twilio's volume indicator feature.")}});if(isEnumerationSupported){_this._initializeEnumeration()}return _this}Object.defineProperty(AudioHelper.prototype,"audioConstraints",{get:function(){return this._audioConstraints},enumerable:false,configurable:true});Object.defineProperty(AudioHelper.prototype,"inputDevice",{get:function(){return this._inputDevice},enumerable:false,configurable:true});Object.defineProperty(AudioHelper.prototype,"inputStream",{get:function(){return this._processedStream||this._selectedInputDeviceStream},enumerable:false,configurable:true});Object.defineProperty(AudioHelper.prototype,"processedStream",{get:function(){return this._processedStream},enumerable:false,configurable:true});AudioHelper.prototype._destroy=function(){this._stopDefaultInputDeviceStream();this._stopSelectedInputDeviceStream();this._destroyProcessedStream();this._maybeStopPollingVolume();this.removeAllListeners();this._unbind()};AudioHelper.prototype._maybeStartPollingVolume=function(){var _this=this;if(!this.isVolumeSupported||!this.inputStream){return}this._updateVolumeSource();if(this._isPollingInputVolume||!this._inputVolumeAnalyser){return}var bufferLength=this._inputVolumeAnalyser.frequencyBinCount;var buffer=new Uint8Array(bufferLength);this._isPollingInputVolume=true;var emitVolume=function(){if(!_this._isPollingInputVolume){return}if(_this._inputVolumeAnalyser){_this._inputVolumeAnalyser.getByteFrequencyData(buffer);var inputVolume=util_1.average(buffer);_this.emit("inputVolume",inputVolume/255)}requestAnimationFrame(emitVolume)};requestAnimationFrame(emitVolume)};AudioHelper.prototype._maybeStopPollingVolume=function(){if(!this.isVolumeSupported){return}if(!this._isPollingInputVolume||this.inputStream&&this.listenerCount("inputVolume")){return}if(this._inputVolumeSource){this._inputVolumeSource.disconnect();delete this._inputVolumeSource}this._isPollingInputVolume=false};AudioHelper.prototype._openDefaultDeviceWithConstraints=function(constraints){var _this=this;this._log.info("Opening default device with constraints",constraints);return this._getUserMedia(constraints).then(function(stream){_this._log.info("Opened default device. Updating available devices.");_this._updateAvailableDevices().catch(function(error){_this._log.warn("Unable to updateAvailableDevices after gUM call",error)});_this._defaultInputDeviceStream=stream;return _this._maybeCreateProcessedStream(stream)})};AudioHelper.prototype._stopDefaultInputDeviceStream=function(){if(this._defaultInputDeviceStream){this._log.info("stopping default device stream");this._defaultInputDeviceStream.getTracks().forEach(function(track){return track.stop()});this._defaultInputDeviceStream=null;this._destroyProcessedStream()}};AudioHelper.prototype._unbind=function(){if(!this._mediaDevices||!this._enumerateDevices){throw new errors_1.NotSupportedError("Enumeration is not supported")}if(this._mediaDevices.removeEventListener){this._mediaDevices.removeEventListener("devicechange",this._updateAvailableDevices)}};AudioHelper.prototype._updateUserOptions=function(options){if(typeof options.enumerateDevices==="function"){this._enumerateDevices=options.enumerateDevices}if(typeof options.getUserMedia==="function"){this._getUserMedia=options.getUserMedia}};AudioHelper.prototype.addProcessor=function(processor){this._log.debug(".addProcessor");if(this._processor){throw new errors_1.NotSupportedError("Adding multiple AudioProcessors is not supported at this time.")}if(typeof processor!=="object"||processor===null){throw new errors_1.InvalidArgumentError("Missing AudioProcessor argument.")}if(typeof processor.createProcessedStream!=="function"){throw new errors_1.InvalidArgumentError("Missing createProcessedStream() method.")}if(typeof processor.destroyProcessedStream!=="function"){throw new errors_1.InvalidArgumentError("Missing destroyProcessedStream() method.")}this._processor=processor;this._audioProcessorEventObserver.emit("add");return this._restartStreams()};AudioHelper.prototype.disconnect=function(doEnable){this._log.debug(".disconnect",doEnable);return this._maybeEnableSound(device_1.default.SoundName.Disconnect,doEnable)};AudioHelper.prototype.incoming=function(doEnable){this._log.debug(".incoming",doEnable);return this._maybeEnableSound(device_1.default.SoundName.Incoming,doEnable)};AudioHelper.prototype.outgoing=function(doEnable){this._log.debug(".outgoing",doEnable);return this._maybeEnableSound(device_1.default.SoundName.Outgoing,doEnable)};AudioHelper.prototype.removeProcessor=function(processor){this._log.debug(".removeProcessor");if(typeof processor!=="object"||processor===null){throw new errors_1.InvalidArgumentError("Missing AudioProcessor argument.")}if(this._processor!==processor){throw new errors_1.InvalidArgumentError("Cannot remove an AudioProcessor that has not been previously added.")}this._destroyProcessedStream();this._processor=null;this._audioProcessorEventObserver.emit("remove");return this._restartStreams()};AudioHelper.prototype.setAudioConstraints=function(audioConstraints){this._log.debug(".setAudioConstraints",audioConstraints);this._audioConstraints=Object.assign({},audioConstraints);delete this._audioConstraints.deviceId;return this.inputDevice?this._setInputDevice(this.inputDevice.deviceId,true):Promise.resolve()};AudioHelper.prototype.setInputDevice=function(deviceId){this._log.debug(".setInputDevice",deviceId);return this._setInputDevice(deviceId,false)};AudioHelper.prototype.unsetAudioConstraints=function(){this._log.debug(".unsetAudioConstraints");this._audioConstraints=null;return this.inputDevice?this._setInputDevice(this.inputDevice.deviceId,true):Promise.resolve()};AudioHelper.prototype.unsetInputDevice=function(){var _this=this;this._log.debug(".unsetInputDevice",this.inputDevice);if(!this.inputDevice){return Promise.resolve()}this._destroyProcessedStream();return this._onActiveInputChanged(null).then(function(){_this._replaceStream(null);_this._inputDevice=null;_this._maybeStopPollingVolume()})};AudioHelper.prototype._destroyProcessedStream=function(){if(this._processor&&this._processedStream){this._log.info("destroying processed stream");var processedStream=this._processedStream;this._processedStream.getTracks().forEach(function(track){return track.stop()});this._processedStream=null;this._processor.destroyProcessedStream(processedStream);this._audioProcessorEventObserver.emit("destroy")}};AudioHelper.prototype._getUnknownDeviceIndex=function(mediaDeviceInfo){var id=mediaDeviceInfo.deviceId;var kind=mediaDeviceInfo.kind;var index=this._unknownDeviceIndexes[kind][id];if(!index){index=Object.keys(this._unknownDeviceIndexes[kind]).length+1;this._unknownDeviceIndexes[kind][id]=index}return index};AudioHelper.prototype._initializeEnumeration=function(){var _this=this;if(!this._mediaDevices||!this._enumerateDevices){throw new errors_1.NotSupportedError("Enumeration is not supported")}if(this._mediaDevices.addEventListener){this._mediaDevices.addEventListener("devicechange",this._updateAvailableDevices)}this._updateAvailableDevices().then(function(){if(!_this.isOutputSelectionSupported){return}Promise.all([_this.speakerDevices.set("default"),_this.ringtoneDevices.set("default")]).catch(function(reason){_this._log.warn("Warning: Unable to set audio output devices. "+reason)})})};AudioHelper.prototype._maybeCreateProcessedStream=function(stream){var _this=this;if(this._processor){this._log.info("Creating processed stream");return this._processor.createProcessedStream(stream).then(function(processedStream){_this._processedStream=processedStream;_this._audioProcessorEventObserver.emit("create");return _this._processedStream})}return Promise.resolve(stream)};AudioHelper.prototype._maybeEnableSound=function(soundName,doEnable){if(typeof doEnable!=="undefined"){this._enabledSounds[soundName]=doEnable}return this._enabledSounds[soundName]};AudioHelper.prototype._replaceStream=function(stream){this._log.info("Replacing with new stream.");if(this._selectedInputDeviceStream){this._log.info("Old stream detected. Stopping tracks.");this._stopSelectedInputDeviceStream()}this._selectedInputDeviceStream=stream};AudioHelper.prototype._restartStreams=function(){if(this.inputDevice&&this._selectedInputDeviceStream){this._log.info("Restarting selected input device");return this._setInputDevice(this.inputDevice.deviceId,true)}if(this._defaultInputDeviceStream){var defaultDevice=this.availableInputDevices.get("default")||Array.from(this.availableInputDevices.values())[0];this._log.info("Restarting default input device, now becoming selected.");return this._setInputDevice(defaultDevice.deviceId,true)}return Promise.resolve()};AudioHelper.prototype._setInputDevice=function(deviceId,forceGetUserMedia){var _this=this;if(typeof deviceId!=="string"){return Promise.reject(new errors_1.InvalidArgumentError("Must specify the device to set"))}var device=this.availableInputDevices.get(deviceId);if(!device){return Promise.reject(new errors_1.InvalidArgumentError("Device not found: "+deviceId))}this._log.info("Setting input device. ID: "+deviceId);if(this._inputDevice&&this._inputDevice.deviceId===deviceId&&this._selectedInputDeviceStream){if(!forceGetUserMedia){return Promise.resolve()}this._log.info("Same track detected on setInputDevice, stopping old tracks.");this._stopSelectedInputDeviceStream()}this._stopDefaultInputDeviceStream();var constraints={audio:Object.assign({deviceId:{exact:deviceId}},this.audioConstraints)};this._log.info("setInputDevice: getting new tracks.");return this._getUserMedia(constraints).then(function(originalStream){_this._destroyProcessedStream();return _this._maybeCreateProcessedStream(originalStream).then(function(newStream){_this._log.info("setInputDevice: invoking _onActiveInputChanged.");return _this._onActiveInputChanged(newStream).then(function(){_this._replaceStream(originalStream);_this._inputDevice=device;_this._maybeStartPollingVolume()})})})};AudioHelper.prototype._stopSelectedInputDeviceStream=function(){if(this._selectedInputDeviceStream){this._log.info("Stopping selected device stream");this._selectedInputDeviceStream.getTracks().forEach(function(track){return track.stop()})}};AudioHelper.prototype._updateDevices=function(updatedDevices,availableDevices,removeLostDevice){var _this=this;var updatedDeviceIds=updatedDevices.map(function(d){return d.deviceId});var knownDeviceIds=Array.from(availableDevices.values()).map(function(d){return d.deviceId});var lostActiveDevices=[];var lostDeviceIds=util_1.difference(knownDeviceIds,updatedDeviceIds);lostDeviceIds.forEach(function(lostDeviceId){var lostDevice=availableDevices.get(lostDeviceId);if(lostDevice){availableDevices.delete(lostDeviceId);if(removeLostDevice(lostDevice)){lostActiveDevices.push(lostDevice)}}});var deviceChanged=false;updatedDevices.forEach(function(newDevice){var existingDevice=availableDevices.get(newDevice.deviceId);var newMediaDeviceInfo=_this._wrapMediaDeviceInfo(newDevice);if(!existingDevice||existingDevice.label!==newMediaDeviceInfo.label){availableDevices.set(newDevice.deviceId,newMediaDeviceInfo);deviceChanged=true}});if(deviceChanged||lostDeviceIds.length){if(this.inputDevice!==null&&this.inputDevice.deviceId==="default"){this._log.warn("Calling getUserMedia after device change to ensure that the           tracks of the active device (default) have not gone stale.");this._setInputDevice(this.inputDevice.deviceId,true)}this._log.debug("#deviceChange",lostActiveDevices);this.emit("deviceChange",lostActiveDevices)}};AudioHelper.prototype._updateVolumeSource=function(){if(!this.inputStream||!this._audioContext||!this._inputVolumeAnalyser){return}if(this._inputVolumeSource){this._inputVolumeSource.disconnect()}try{this._inputVolumeSource=this._audioContext.createMediaStreamSource(this.inputStream);this._inputVolumeSource.connect(this._inputVolumeAnalyser)}catch(ex){this._log.warn("Unable to update volume source",ex);delete this._inputVolumeSource}};AudioHelper.prototype._wrapMediaDeviceInfo=function(mediaDeviceInfo){var options={deviceId:mediaDeviceInfo.deviceId,groupId:mediaDeviceInfo.groupId,kind:mediaDeviceInfo.kind,label:mediaDeviceInfo.label};if(!options.label){if(options.deviceId==="default"){options.label="Default"}else{var index=this._getUnknownDeviceIndex(mediaDeviceInfo);options.label="Unknown "+kindAliases[options.kind]+" Device "+index}}return new mediadeviceinfo_1.default(options)};return AudioHelper}(events_1.EventEmitter);(function(AudioHelper){})(AudioHelper||(AudioHelper={}));exports.default=AudioHelper},{"./device":12,"./errors":15,"./log":18,"./outputdevicecollection":19,"./shims/mediadeviceinfo":33,"./util":36,events:39}],4:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();var __awaiter=this&&this.__awaiter||function(thisArg,_arguments,P,generator){function adopt(value){return value instanceof P?value:new P(function(resolve){resolve(value)})}return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value))}catch(e){reject(e)}}function rejected(value){try{step(generator["throw"](value))}catch(e){reject(e)}}function step(result){result.done?resolve(result.value):adopt(result.value).then(fulfilled,rejected)}step((generator=generator.apply(thisArg,_arguments||[])).next())})};var __generator=this&&this.__generator||function(thisArg,body){var _={label:0,sent:function(){if(t[0]&1)throw t[1];return t[1]},trys:[],ops:[]},f,y,t,g;return g={next:verb(0),throw:verb(1),return:verb(2)},typeof Symbol==="function"&&(g[Symbol.iterator]=function(){return this}),g;function verb(n){return function(v){return step([n,v])}}function step(op){if(f)throw new TypeError("Generator is already executing.");while(_)try{if(f=1,y&&(t=op[0]&2?y["return"]:op[0]?y["throw"]||((t=y["return"])&&t.call(y),0):y.next)&&!(t=t.call(y,op[1])).done)return t;if(y=0,t)op=[op[0]&2,t.value];switch(op[0]){case 0:case 1:t=op;break;case 4:_.label++;return{value:op[1],done:false};case 5:_.label++;y=op[1];op=[0];continue;case 7:op=_.ops.pop();_.trys.pop();continue;default:if(!(t=_.trys,t=t.length>0&&t[t.length-1])&&(op[0]===6||op[0]===2)){_=0;continue}if(op[0]===3&&(!t||op[1]>t[0]&&op[1]<t[3])){_.label=op[1];break}if(op[0]===6&&_.label<t[1]){_.label=t[1];t=op;break}if(t&&_.label<t[2]){_.label=t[2];_.ops.push(op);break}if(t[2])_.ops.pop();_.trys.pop();continue}op=body.call(thisArg,_)}catch(e){op=[6,e];y=0}finally{f=t=0}if(op[0]&5)throw op[1];return{value:op[0]?op[1]:void 0,done:true}}};Object.defineProperty(exports,"__esModule",{value:true});var deferred_1=require("./deferred");var eventtarget_1=require("./eventtarget");var AudioPlayer=function(_super){__extends(AudioPlayer,_super);function AudioPlayer(audioContext,srcOrOptions,options){if(srcOrOptions===void 0){srcOrOptions={}}if(options===void 0){options={}}var _this=_super.call(this)||this;_this._audioNode=null;_this._loop=false;_this._pendingPlayDeferreds=[];_this._sinkId="default";_this._src="";if(typeof srcOrOptions!=="string"){options=srcOrOptions}_this._audioContext=audioContext;_this._audioElement=new(options.AudioFactory||Audio);_this._bufferPromise=_this._createPlayDeferred().promise;_this._destination=_this._audioContext.destination;_this._gainNode=_this._audioContext.createGain();_this._gainNode.connect(_this._destination);_this._XMLHttpRequest=options.XMLHttpRequestFactory||XMLHttpRequest;_this.addEventListener("canplaythrough",function(){_this._resolvePlayDeferreds()});if(typeof srcOrOptions==="string"){_this.src=srcOrOptions}return _this}Object.defineProperty(AudioPlayer.prototype,"destination",{get:function(){return this._destination},enumerable:false,configurable:true});Object.defineProperty(AudioPlayer.prototype,"loop",{get:function(){return this._loop},set:function(shouldLoop){var self=this;function pauseAfterPlaythrough(){self._audioNode.removeEventListener("ended",pauseAfterPlaythrough);self.pause()}if(!shouldLoop&&this.loop&&!this.paused){this._audioNode.addEventListener("ended",pauseAfterPlaythrough)}this._loop=shouldLoop},enumerable:false,configurable:true});Object.defineProperty(AudioPlayer.prototype,"muted",{get:function(){return this._gainNode.gain.value===0},set:function(shouldBeMuted){this._gainNode.gain.value=shouldBeMuted?0:1},enumerable:false,configurable:true});Object.defineProperty(AudioPlayer.prototype,"paused",{get:function(){return this._audioNode===null},enumerable:false,configurable:true});Object.defineProperty(AudioPlayer.prototype,"src",{get:function(){return this._src},set:function(src){this._load(src)},enumerable:false,configurable:true});Object.defineProperty(AudioPlayer.prototype,"srcObject",{get:function(){return this._audioElement.srcObject},set:function(srcObject){this._audioElement.srcObject=srcObject},enumerable:false,configurable:true});Object.defineProperty(AudioPlayer.prototype,"sinkId",{get:function(){return this._sinkId},enumerable:false,configurable:true});AudioPlayer.prototype.load=function(){this._load(this._src)};AudioPlayer.prototype.pause=function(){if(this.paused){return}this._audioElement.pause();this._audioNode.stop();this._audioNode.disconnect(this._gainNode);this._audioNode=null;this._rejectPlayDeferreds(new Error("The play() request was interrupted by a call to pause()."))};AudioPlayer.prototype.play=function(){return __awaiter(this,void 0,void 0,function(){var buffer;var _this=this;return __generator(this,function(_a){switch(_a.label){case 0:if(!!this.paused)return[3,2];return[4,this._bufferPromise];case 1:_a.sent();if(!this.paused){return[2]}throw new Error("The play() request was interrupted by a call to pause().");case 2:this._audioNode=this._audioContext.createBufferSource();this._audioNode.loop=this.loop;this._audioNode.addEventListener("ended",function(){if(_this._audioNode&&_this._audioNode.loop){return}_this.dispatchEvent("ended")});return[4,this._bufferPromise];case 3:buffer=_a.sent();if(this.paused){throw new Error("The play() request was interrupted by a call to pause().")}this._audioNode.buffer=buffer;this._audioNode.connect(this._gainNode);this._audioNode.start();if(this._audioElement.srcObject){return[2,this._audioElement.play()]}return[2]}})})};AudioPlayer.prototype.setSinkId=function(sinkId){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(_a){switch(_a.label){case 0:if(typeof this._audioElement.setSinkId!=="function"){throw new Error("This browser does not support setSinkId.")}if(sinkId===this.sinkId){return[2]}if(sinkId==="default"){if(!this.paused){this._gainNode.disconnect(this._destination)}this._audioElement.srcObject=null;this._destination=this._audioContext.destination;this._gainNode.connect(this._destination);this._sinkId=sinkId;return[2]}return[4,this._audioElement.setSinkId(sinkId)];case 1:_a.sent();if(this._audioElement.srcObject){return[2]}this._gainNode.disconnect(this._audioContext.destination);this._destination=this._audioContext.createMediaStreamDestination();this._audioElement.srcObject=this._destination.stream;this._sinkId=sinkId;this._gainNode.connect(this._destination);return[2]}})})};AudioPlayer.prototype._createPlayDeferred=function(){var deferred=new deferred_1.default;this._pendingPlayDeferreds.push(deferred);return deferred};AudioPlayer.prototype._load=function(src){var _this=this;if(this._src&&this._src!==src){this.pause()}this._src=src;this._bufferPromise=new Promise(function(resolve,reject){return __awaiter(_this,void 0,void 0,function(){var buffer;return __generator(this,function(_a){switch(_a.label){case 0:if(!src){return[2,this._createPlayDeferred().promise]}return[4,bufferSound(this._audioContext,this._XMLHttpRequest,src)];case 1:buffer=_a.sent();this.dispatchEvent("canplaythrough");resolve(buffer);return[2]}})})})};AudioPlayer.prototype._rejectPlayDeferreds=function(reason){var deferreds=this._pendingPlayDeferreds;deferreds.splice(0,deferreds.length).forEach(function(_a){var reject=_a.reject;return reject(reason)})};AudioPlayer.prototype._resolvePlayDeferreds=function(result){var deferreds=this._pendingPlayDeferreds;deferreds.splice(0,deferreds.length).forEach(function(_a){var resolve=_a.resolve;return resolve(result)})};return AudioPlayer}(eventtarget_1.default);function bufferSound(context,RequestFactory,src){return __awaiter(this,void 0,void 0,function(){var request,event;return __generator(this,function(_a){switch(_a.label){case 0:request=new RequestFactory;request.open("GET",src,true);request.responseType="arraybuffer";return[4,new Promise(function(resolve){request.addEventListener("load",resolve);request.send()})];case 1:event=_a.sent();try{return[2,context.decodeAudioData(event.target.response)]}catch(e){return[2,new Promise(function(resolve){context.decodeAudioData(event.target.response,resolve)})]}return[2]}})})}exports.default=AudioPlayer},{"./deferred":5,"./eventtarget":6}],5:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var Deferred=function(){function Deferred(){var _this=this;this.promise=new Promise(function(resolve,reject){_this._resolve=resolve;_this._reject=reject})}Object.defineProperty(Deferred.prototype,"reject",{get:function(){return this._reject},enumerable:false,configurable:true});Object.defineProperty(Deferred.prototype,"resolve",{get:function(){return this._resolve},enumerable:false,configurable:true});return Deferred}();exports.default=Deferred},{}],6:[function(require,module,exports){"use strict";var __spreadArrays=this&&this.__spreadArrays||function(){for(var s=0,i=0,il=arguments.length;i<il;i++)s+=arguments[i].length;for(var r=Array(s),k=0,i=0;i<il;i++)for(var a=arguments[i],j=0,jl=a.length;j<jl;j++,k++)r[k]=a[j];return r};Object.defineProperty(exports,"__esModule",{value:true});var events_1=require("events");var EventTarget=function(){function EventTarget(){this._eventEmitter=new events_1.EventEmitter}EventTarget.prototype.addEventListener=function(name,handler){return this._eventEmitter.addListener(name,handler)};EventTarget.prototype.dispatchEvent=function(name){var _a;var args=[];for(var _i=1;_i<arguments.length;_i++){args[_i-1]=arguments[_i]}return(_a=this._eventEmitter).emit.apply(_a,__spreadArrays([name],args))};EventTarget.prototype.removeEventListener=function(name,handler){return this._eventEmitter.removeListener(name,handler)};return EventTarget}();exports.default=EventTarget},{events:39}],7:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();Object.defineProperty(exports,"__esModule",{value:true});exports.AudioProcessorEventObserver=void 0;var events_1=require("events");var log_1=require("./log");var AudioProcessorEventObserver=function(_super){__extends(AudioProcessorEventObserver,_super);function AudioProcessorEventObserver(){var _this=_super.call(this)||this;_this._log=new log_1.default("AudioProcessorEventObserver");_this._log.info("Creating AudioProcessorEventObserver instance");_this.on("enabled",function(){return _this._reEmitEvent("enabled")});_this.on("add",function(){return _this._reEmitEvent("add")});_this.on("remove",function(){return _this._reEmitEvent("remove")});_this.on("create",function(){return _this._reEmitEvent("create-processed-stream")});_this.on("destroy",function(){return _this._reEmitEvent("destroy-processed-stream")});return _this}AudioProcessorEventObserver.prototype.destroy=function(){this.removeAllListeners()};AudioProcessorEventObserver.prototype._reEmitEvent=function(name){this._log.info("AudioProcessor:"+name);this.emit("event",{name:name,group:"audio-processor"})};return AudioProcessorEventObserver}(events_1.EventEmitter);exports.AudioProcessorEventObserver=AudioProcessorEventObserver},{"./log":18,events:39}],8:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();Object.defineProperty(exports,"__esModule",{value:true});var events_1=require("events");var Backoff=function(_super){__extends(Backoff,_super);function Backoff(options){var _this=_super.call(this)||this;Object.defineProperties(_this,{_attempts:{value:0,writable:true},_duration:{enumerable:false,get:function(){var ms=this._min*Math.pow(this._factor,this._attempts);if(this._jitter){var rand=Math.random();var deviation=Math.floor(rand*this._jitter*ms);ms=(Math.floor(rand*10)&1)===0?ms-deviation:ms+deviation}return Math.min(ms,this._max)|0}},_factor:{value:options.factor||2},_jitter:{value:options.jitter>0&&options.jitter<=1?options.jitter:0},_max:{value:options.max||1e4},_min:{value:options.min||100},_timeoutID:{value:null,writable:true}});return _this}Backoff.prototype.backoff=function(){var _this=this;var duration=this._duration;if(this._timeoutID){clearTimeout(this._timeoutID);this._timeoutID=null}this.emit("backoff",this._attempts,duration);this._timeoutID=setTimeout(function(){_this.emit("ready",_this._attempts,duration);_this._attempts++},duration)};Backoff.prototype.reset=function(){this._attempts=0;if(this._timeoutID){clearTimeout(this._timeoutID);this._timeoutID=null}};return Backoff}(events_1.EventEmitter);exports.default=Backoff},{events:39}],9:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();var __assign=this&&this.__assign||function(){__assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)if(Object.prototype.hasOwnProperty.call(s,p))t[p]=s[p]}return t};return __assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:true});var events_1=require("events");var backoff_1=require("./backoff");var device_1=require("./device");var errors_1=require("./errors");var log_1=require("./log");var rtc_1=require("./rtc");var icecandidate_1=require("./rtc/icecandidate");var sdp_1=require("./rtc/sdp");var statsMonitor_1=require("./statsMonitor");var util_1=require("./util");var uuid_1=require("./uuid");var constants_1=require("./constants");var BACKOFF_CONFIG={factor:1.1,jitter:.5,max:3e4,min:1};var DTMF_INTER_TONE_GAP=70;var DTMF_PAUSE_DURATION=500;var DTMF_TONE_DURATION=160;var METRICS_BATCH_SIZE=10;var METRICS_DELAY=5e3;var MEDIA_DISCONNECT_ERROR={disconnect:true,info:{code:31003,message:"Connection with Twilio was interrupted.",twilioError:new errors_1.MediaErrors.ConnectionError}};var MULTIPLE_THRESHOLD_WARNING_NAMES={packetsLostFraction:{max:"packet-loss",maxAverage:"packets-lost-fraction"}};var WARNING_NAMES={audioInputLevel:"audio-input-level",audioOutputLevel:"audio-output-level",bytesReceived:"bytes-received",bytesSent:"bytes-sent",jitter:"jitter",mos:"mos",rtt:"rtt"};var WARNING_PREFIXES={max:"high-",maxAverage:"high-",maxDuration:"constant-",min:"low-",minStandardDeviation:"constant-"};var Call=function(_super){__extends(Call,_super);function Call(config,options){var _this=_super.call(this)||this;_this.parameters={};_this._inputVolumeStreak=0;_this._isAnswered=false;_this._isCancelled=false;_this._isRejected=false;_this._latestInputVolume=0;_this._latestOutputVolume=0;_this._log=new log_1.default("Call");_this._mediaStatus=Call.State.Pending;_this._messages=new Map;_this._metricsSamples=[];_this._options={MediaHandler:rtc_1.PeerConnection,enableImprovedSignalingErrorPrecision:false,offerSdp:null,shouldPlayDisconnect:function(){return true},voiceEventSidGenerator:uuid_1.generateVoiceEventSid};_this._outputVolumeStreak=0;_this._shouldSendHangup=true;_this._signalingStatus=Call.State.Pending;_this._soundcache=new Map;_this._status=Call.State.Pending;_this._wasConnected=false;_this.toString=function(){return"[Twilio.Call instance]"};_this._emitWarning=function(groupPrefix,warningName,threshold,value,wasCleared,warningData){var groupSuffix=wasCleared?"-cleared":"-raised";var groupName=groupPrefix+"warning"+groupSuffix;if(warningName==="constant-audio-input-level"&&_this.isMuted()){return}var level=wasCleared?"info":"warning";if(warningName==="constant-audio-output-level"){level="info"}var payloadData={threshold:threshold};if(value){if(value instanceof Array){payloadData.values=value.map(function(val){if(typeof val==="number"){return Math.round(val*100)/100}return value})}else{payloadData.value=value}}_this._publisher.post(level,groupName,warningName,{data:payloadData},_this);if(warningName!=="constant-audio-output-level"){var emitName=wasCleared?"warning-cleared":"warning";_this._log.debug("#"+emitName,warningName);_this.emit(emitName,warningName,warningData&&!wasCleared?warningData:null)}};_this._onAck=function(payload){var acktype=payload.acktype,callsid=payload.callsid,voiceeventsid=payload.voiceeventsid;if(_this.parameters.CallSid!==callsid){_this._log.warn("Received ack from a different callsid: "+callsid);return}if(acktype==="message"){_this._onMessageSent(voiceeventsid)}};_this._onAnswer=function(payload){if(typeof payload.reconnect==="string"){_this._signalingReconnectToken=payload.reconnect}if(_this._isAnswered&&_this._status!==Call.State.Reconnecting){return}_this._setCallSid(payload);_this._isAnswered=true;_this._maybeTransitionToOpen()};_this._onCancel=function(payload){var callsid=payload.callsid;if(_this.parameters.CallSid===callsid){_this._isCancelled=true;_this._publisher.info("connection","cancel",null,_this);_this._cleanupEventListeners();_this._mediaHandler.close();_this._status=Call.State.Closed;_this._log.debug("#cancel");_this.emit("cancel");_this._pstream.removeListener("cancel",_this._onCancel)}};_this._onConnected=function(){_this._log.info("Received connected from pstream");if(_this._signalingReconnectToken&&_this._mediaHandler.version){_this._pstream.reconnect(_this._mediaHandler.version.getSDP(),_this.parameters.CallSid,_this._signalingReconnectToken)}};_this._onHangup=function(payload){if(_this.status()===Call.State.Closed){return}if(payload.callsid&&(_this.parameters.CallSid||_this.outboundConnectionId)){if(payload.callsid!==_this.parameters.CallSid&&payload.callsid!==_this.outboundConnectionId){return}}else if(payload.callsid){return}_this._log.info("Received HANGUP from gateway");if(payload.error){var code=payload.error.code;var errorConstructor=errors_1.getPreciseSignalingErrorByCode(_this._options.enableImprovedSignalingErrorPrecision,code);var error=typeof errorConstructor!=="undefined"?new errorConstructor(payload.error.message):new errors_1.GeneralErrors.ConnectionError("Error sent from gateway in HANGUP");_this._log.error("Received an error from the gateway:",error);_this._log.debug("#error",error);_this.emit("error",error)}_this._shouldSendHangup=false;_this._publisher.info("connection","disconnected-by-remote",null,_this);_this._disconnect(null,true);_this._cleanupEventListeners()};_this._onMediaFailure=function(type){var _a=Call.MediaFailure,ConnectionDisconnected=_a.ConnectionDisconnected,ConnectionFailed=_a.ConnectionFailed,IceGatheringFailed=_a.IceGatheringFailed,LowBytes=_a.LowBytes;var isEndOfIceCycle=type===ConnectionFailed||type===IceGatheringFailed;if(!util_1.isChrome(window,window.navigator)&&type===ConnectionFailed){return _this._mediaHandler.onerror(MEDIA_DISCONNECT_ERROR)}if(_this._mediaStatus===Call.State.Reconnecting){if(isEndOfIceCycle){if(Date.now()-_this._mediaReconnectStartTime>BACKOFF_CONFIG.max){_this._log.warn("Exceeded max ICE retries");return _this._mediaHandler.onerror(MEDIA_DISCONNECT_ERROR)}try{_this._mediaReconnectBackoff.backoff()}catch(error){if(!(error.message&&error.message==="Backoff in progress.")){throw error}}}return}var pc=_this._mediaHandler.version.pc;var isIceDisconnected=pc&&pc.iceConnectionState==="disconnected";var hasLowBytesWarning=_this._monitor.hasActiveWarning("bytesSent","min")||_this._monitor.hasActiveWarning("bytesReceived","min");if(type===LowBytes&&isIceDisconnected||type===ConnectionDisconnected&&hasLowBytesWarning||isEndOfIceCycle){var mediaReconnectionError=new errors_1.MediaErrors.ConnectionError("Media connection failed.");_this._log.warn("ICE Connection disconnected.");_this._publisher.warn("connection","error",mediaReconnectionError,_this);_this._publisher.info("connection","reconnecting",null,_this);_this._mediaReconnectStartTime=Date.now();_this._status=Call.State.Reconnecting;_this._mediaStatus=Call.State.Reconnecting;_this._mediaReconnectBackoff.reset();_this._mediaReconnectBackoff.backoff();_this._log.debug("#reconnecting");_this.emit("reconnecting",mediaReconnectionError)}};_this._onMediaReconnected=function(){if(_this._mediaStatus!==Call.State.Reconnecting){return}_this._log.info("ICE Connection reestablished.");_this._mediaStatus=Call.State.Open;if(_this._signalingStatus===Call.State.Open){_this._publisher.info("connection","reconnected",null,_this);_this._log.debug("#reconnected");_this.emit("reconnected");_this._status=Call.State.Open}};_this._onMessageReceived=function(payload){var callsid=payload.callsid,content=payload.content,contenttype=payload.contenttype,messagetype=payload.messagetype,voiceeventsid=payload.voiceeventsid;if(_this.parameters.CallSid!==callsid){_this._log.warn("Received a message from a different callsid: "+callsid);return}var data={content:content,contentType:contenttype,messageType:messagetype,voiceEventSid:voiceeventsid};_this._log.debug("#messageReceived",JSON.stringify(data));_this.emit("messageReceived",data)};_this._onMessageSent=function(voiceEventSid){if(!_this._messages.has(voiceEventSid)){_this._log.warn("Received a messageSent with a voiceEventSid that doesn't exists: "+voiceEventSid);return}var message=_this._messages.get(voiceEventSid);_this._messages.delete(voiceEventSid);_this._log.debug("#messageSent",JSON.stringify(message));_this.emit("messageSent",message)};_this._onRinging=function(payload){_this._setCallSid(payload);if(_this._status!==Call.State.Connecting&&_this._status!==Call.State.Ringing){return}var hasEarlyMedia=!!payload.sdp;_this._status=Call.State.Ringing;_this._publisher.info("connection","outgoing-ringing",{hasEarlyMedia:hasEarlyMedia},_this);_this._log.debug("#ringing");_this.emit("ringing",hasEarlyMedia)};_this._onRTCSample=function(sample){var callMetrics=__assign(__assign({},sample),{inputVolume:_this._latestInputVolume,outputVolume:_this._latestOutputVolume});_this._codec=callMetrics.codecName;_this._metricsSamples.push(callMetrics);if(_this._metricsSamples.length>=METRICS_BATCH_SIZE){_this._publishMetrics()}_this.emit("sample",sample)};_this._onSignalingError=function(payload){var callsid=payload.callsid,voiceeventsid=payload.voiceeventsid;if(_this.parameters.CallSid!==callsid){_this._log.warn("Received an error from a different callsid: "+callsid);return}if(voiceeventsid&&_this._messages.has(voiceeventsid)){_this._messages.delete(voiceeventsid);_this._log.warn("Received an error while sending a message.",payload)}};_this._onSignalingReconnected=function(){if(_this._signalingStatus!==Call.State.Reconnecting){return}_this._log.info("Signaling Connection reestablished.");_this._signalingStatus=Call.State.Open;if(_this._mediaStatus===Call.State.Open){_this._publisher.info("connection","reconnected",null,_this);_this._log.debug("#reconnected");_this.emit("reconnected");_this._status=Call.State.Open}};_this._onTransportClose=function(){_this._log.error("Received transportClose from pstream");_this._log.debug("#transportClose");_this.emit("transportClose");if(_this._signalingReconnectToken){_this._status=Call.State.Reconnecting;_this._signalingStatus=Call.State.Reconnecting;_this._log.debug("#reconnecting");_this.emit("reconnecting",new errors_1.SignalingErrors.ConnectionDisconnected)}else{_this._status=Call.State.Closed;_this._signalingStatus=Call.State.Closed}};_this._reemitWarning=function(warningData,wasCleared){var groupPrefix=/^audio/.test(warningData.name)?"audio-level-":"network-quality-";var warningPrefix=WARNING_PREFIXES[warningData.threshold.name];var warningName;if(warningData.name in MULTIPLE_THRESHOLD_WARNING_NAMES){warningName=MULTIPLE_THRESHOLD_WARNING_NAMES[warningData.name][warningData.threshold.name]}else if(warningData.name in WARNING_NAMES){warningName=WARNING_NAMES[warningData.name]}var warning=warningPrefix+warningName;_this._emitWarning(groupPrefix,warning,warningData.threshold.value,warningData.values||warningData.value,wasCleared,warningData)};_this._reemitWarningCleared=function(warningData){_this._reemitWarning(warningData,true)};_this._isUnifiedPlanDefault=config.isUnifiedPlanDefault;_this._soundcache=config.soundcache;if(typeof config.onIgnore==="function"){_this._onIgnore=config.onIgnore}var message=options&&options.twimlParams||{};_this.customParameters=new Map(Object.entries(message).map(function(_a){var key=_a[0],val=_a[1];return[key,String(val)]}));Object.assign(_this._options,options);if(_this._options.callParameters){_this.parameters=_this._options.callParameters}if(_this._options.reconnectToken){_this._signalingReconnectToken=_this._options.reconnectToken}_this._voiceEventSidGenerator=_this._options.voiceEventSidGenerator||uuid_1.generateVoiceEventSid;_this._direction=_this.parameters.CallSid?Call.CallDirection.Incoming:Call.CallDirection.Outgoing;if(_this._direction===Call.CallDirection.Incoming&&_this.parameters){_this.callerInfo=_this.parameters.StirStatus?{isVerified:_this.parameters.StirStatus==="TN-Validation-Passed-A"}:null}else{_this.callerInfo=null}_this._mediaReconnectBackoff=new backoff_1.default(BACKOFF_CONFIG);_this._mediaReconnectBackoff.on("ready",function(){return _this._mediaHandler.iceRestart()});_this.outboundConnectionId=generateTempCallSid();var publisher=_this._publisher=config.publisher;if(_this._direction===Call.CallDirection.Incoming){publisher.info("connection","incoming",null,_this)}else{publisher.info("connection","outgoing",{preflight:_this._options.preflight},_this)}var monitor=_this._monitor=new(_this._options.StatsMonitor||statsMonitor_1.default);monitor.on("sample",_this._onRTCSample);monitor.disableWarnings();setTimeout(function(){return monitor.enableWarnings()},METRICS_DELAY);monitor.on("warning",function(data,wasCleared){if(data.name==="bytesSent"||data.name==="bytesReceived"){_this._onMediaFailure(Call.MediaFailure.LowBytes)}_this._reemitWarning(data,wasCleared)});monitor.on("warning-cleared",function(data){_this._reemitWarningCleared(data)});_this._mediaHandler=new _this._options.MediaHandler(config.audioHelper,config.pstream,{RTCPeerConnection:_this._options.RTCPeerConnection,codecPreferences:_this._options.codecPreferences,dscp:_this._options.dscp,forceAggressiveIceNomination:_this._options.forceAggressiveIceNomination,isUnifiedPlan:_this._isUnifiedPlanDefault,maxAverageBitrate:_this._options.maxAverageBitrate,preflight:_this._options.preflight});_this.on("volume",function(inputVolume,outputVolume){_this._inputVolumeStreak=_this._checkVolume(inputVolume,_this._inputVolumeStreak,_this._latestInputVolume,"input");_this._outputVolumeStreak=_this._checkVolume(outputVolume,_this._outputVolumeStreak,_this._latestOutputVolume,"output");_this._latestInputVolume=inputVolume;_this._latestOutputVolume=outputVolume});_this._mediaHandler.onaudio=function(remoteAudio){_this._log.debug("#audio");_this.emit("audio",remoteAudio)};_this._mediaHandler.onvolume=function(inputVolume,outputVolume,internalInputVolume,internalOutputVolume){monitor.addVolumes(internalInputVolume/255*32767,internalOutputVolume/255*32767);_this.emit("volume",inputVolume,outputVolume)};_this._mediaHandler.ondtlstransportstatechange=function(state){var level=state==="failed"?"error":"debug";_this._publisher.post(level,"dtls-transport-state",state,null,_this)};_this._mediaHandler.onpcconnectionstatechange=function(state){var level="debug";var dtlsTransport=_this._mediaHandler.getRTCDtlsTransport();if(state==="failed"){level=dtlsTransport&&dtlsTransport.state==="failed"?"error":"warning"}_this._publisher.post(level,"pc-connection-state",state,null,_this)};_this._mediaHandler.onicecandidate=function(candidate){var payload=new icecandidate_1.IceCandidate(candidate).toPayload();_this._publisher.debug("ice-candidate","ice-candidate",payload,_this)};_this._mediaHandler.onselectedcandidatepairchange=function(pair){var localCandidatePayload=new icecandidate_1.IceCandidate(pair.local).toPayload();var remoteCandidatePayload=new icecandidate_1.IceCandidate(pair.remote,true).toPayload();_this._publisher.debug("ice-candidate","selected-ice-candidate-pair",{local_candidate:localCandidatePayload,remote_candidate:remoteCandidatePayload},_this)};_this._mediaHandler.oniceconnectionstatechange=function(state){var level=state==="failed"?"error":"debug";_this._publisher.post(level,"ice-connection-state",state,null,_this)};_this._mediaHandler.onicegatheringfailure=function(type){_this._publisher.warn("ice-gathering-state",type,null,_this);_this._onMediaFailure(Call.MediaFailure.IceGatheringFailed)};_this._mediaHandler.onicegatheringstatechange=function(state){_this._publisher.debug("ice-gathering-state",state,null,_this)};_this._mediaHandler.onsignalingstatechange=function(state){_this._publisher.debug("signaling-state",state,null,_this)};_this._mediaHandler.ondisconnected=function(msg){_this._log.warn(msg);_this._publisher.warn("network-quality-warning-raised","ice-connectivity-lost",{message:msg},_this);_this._log.debug("#warning","ice-connectivity-lost");_this.emit("warning","ice-connectivity-lost");_this._onMediaFailure(Call.MediaFailure.ConnectionDisconnected)};_this._mediaHandler.onfailed=function(msg){_this._onMediaFailure(Call.MediaFailure.ConnectionFailed)};_this._mediaHandler.onconnected=function(){if(_this._status===Call.State.Reconnecting){_this._onMediaReconnected()}};_this._mediaHandler.onreconnected=function(msg){_this._log.info(msg);_this._publisher.info("network-quality-warning-cleared","ice-connectivity-lost",{message:msg},_this);_this._log.debug("#warning-cleared","ice-connectivity-lost");_this.emit("warning-cleared","ice-connectivity-lost");_this._onMediaReconnected()};_this._mediaHandler.onerror=function(e){if(e.disconnect===true){_this._disconnect(e.info&&e.info.message)}var error=e.info.twilioError||new errors_1.GeneralErrors.UnknownError(e.info.message);_this._log.error("Received an error from MediaStream:",e);_this._log.debug("#error",error);_this.emit("error",error)};_this._mediaHandler.onopen=function(){if(_this._status===Call.State.Open||_this._status===Call.State.Reconnecting){return}else if(_this._status===Call.State.Ringing||_this._status===Call.State.Connecting){_this.mute(_this._mediaHandler.isMuted);_this._mediaStatus=Call.State.Open;_this._maybeTransitionToOpen()}else{_this._mediaHandler.close()}};_this._mediaHandler.onclose=function(){_this._status=Call.State.Closed;if(_this._options.shouldPlayDisconnect&&_this._options.shouldPlayDisconnect()&&!_this._isCancelled&&!_this._isRejected){_this._soundcache.get(device_1.default.SoundName.Disconnect).play()}monitor.disable();_this._publishMetrics();if(!_this._isCancelled&&!_this._isRejected){_this._log.debug("#disconnect");_this.emit("disconnect",_this)}};_this._pstream=config.pstream;_this._pstream.on("ack",_this._onAck);_this._pstream.on("cancel",_this._onCancel);_this._pstream.on("error",_this._onSignalingError);_this._pstream.on("ringing",_this._onRinging);_this._pstream.on("transportClose",_this._onTransportClose);_this._pstream.on("connected",_this._onConnected);_this._pstream.on("message",_this._onMessageReceived);_this.on("error",function(error){_this._publisher.error("connection","error",{code:error.code,message:error.message},_this);if(_this._pstream&&_this._pstream.status==="disconnected"){_this._cleanupEventListeners()}});_this.on("disconnect",function(){_this._cleanupEventListeners()});return _this}Object.defineProperty(Call.prototype,"direction",{get:function(){return this._direction},enumerable:false,configurable:true});Object.defineProperty(Call.prototype,"codec",{get:function(){return this._codec},enumerable:false,configurable:true});Call.prototype._setInputTracksFromStream=function(stream){return this._mediaHandler.setInputTracksFromStream(stream)};Call.prototype._setSinkIds=function(sinkIds){return this._mediaHandler._setSinkIds(sinkIds)};Call.prototype.accept=function(options){var _this=this;if(this._status!==Call.State.Pending){return}this._log.debug(".accept",options);options=options||{};var rtcConfiguration=options.rtcConfiguration||this._options.rtcConfiguration;var rtcConstraints=options.rtcConstraints||this._options.rtcConstraints||{};var audioConstraints=rtcConstraints.audio||{audio:true};this._status=Call.State.Connecting;var connect=function(){if(_this._status!==Call.State.Connecting){_this._cleanupEventListeners();_this._mediaHandler.close();return}var onAnswer=function(pc,reconnectToken){var eventName=_this._direction===Call.CallDirection.Incoming?"accepted-by-local":"accepted-by-remote";_this._publisher.info("connection",eventName,null,_this);if(typeof reconnectToken==="string"){_this._signalingReconnectToken=reconnectToken}var _a=sdp_1.getPreferredCodecInfo(_this._mediaHandler.version.getSDP()),codecName=_a.codecName,codecParams=_a.codecParams;_this._publisher.info("settings","codec",{codec_params:codecParams,selected_codec:codecName},_this);_this._monitor.enable(pc)};var sinkIds=typeof _this._options.getSinkIds==="function"&&_this._options.getSinkIds();if(Array.isArray(sinkIds)){_this._mediaHandler._setSinkIds(sinkIds).catch(function(){})}_this._pstream.addListener("hangup",_this._onHangup);if(_this._direction===Call.CallDirection.Incoming){_this._isAnswered=true;_this._pstream.on("answer",_this._onAnswer);_this._mediaHandler.answerIncomingCall(_this.parameters.CallSid,_this._options.offerSdp,rtcConstraints,rtcConfiguration,onAnswer)}else{var params=Array.from(_this.customParameters.entries()).map(function(pair){return encodeURIComponent(pair[0])+"="+encodeURIComponent(pair[1])}).join("&");_this._pstream.on("answer",_this._onAnswer);_this._mediaHandler.makeOutgoingCall(_this._pstream.token,params,_this.outboundConnectionId,rtcConstraints,rtcConfiguration,onAnswer)}};if(this._options.beforeAccept){this._options.beforeAccept(this)}var inputStream=typeof this._options.getInputStream==="function"&&this._options.getInputStream();var promise=inputStream?this._mediaHandler.setInputTracksFromStream(inputStream):this._mediaHandler.openDefaultDeviceWithConstraints(audioConstraints);promise.then(function(){_this._publisher.info("get-user-media","succeeded",{data:{audioConstraints:audioConstraints}},_this);connect()},function(error){var twilioError;if(error.code===31208||["PermissionDeniedError","NotAllowedError"].indexOf(error.name)!==-1){twilioError=new errors_1.UserMediaErrors.PermissionDeniedError;_this._publisher.error("get-user-media","denied",{data:{audioConstraints:audioConstraints,error:error}},_this)}else{twilioError=new errors_1.UserMediaErrors.AcquisitionFailedError;_this._publisher.error("get-user-media","failed",{data:{audioConstraints:audioConstraints,error:error}},_this)}_this._disconnect();_this._log.debug("#error",error);_this.emit("error",twilioError)})};Call.prototype.disconnect=function(){this._log.debug(".disconnect");this._disconnect()};Call.prototype.getLocalStream=function(){return this._mediaHandler&&this._mediaHandler.stream};Call.prototype.getRemoteStream=function(){return this._mediaHandler&&this._mediaHandler._remoteStream};Call.prototype.ignore=function(){if(this._status!==Call.State.Pending){return}this._log.debug(".ignore");this._status=Call.State.Closed;this._mediaHandler.ignore(this.parameters.CallSid);this._publisher.info("connection","ignored-by-local",null,this);if(this._onIgnore){this._onIgnore()}};Call.prototype.isMuted=function(){return this._mediaHandler.isMuted};Call.prototype.mute=function(shouldMute){if(shouldMute===void 0){shouldMute=true}this._log.debug(".mute",shouldMute);var wasMuted=this._mediaHandler.isMuted;this._mediaHandler.mute(shouldMute);var isMuted=this._mediaHandler.isMuted;if(wasMuted!==isMuted){this._publisher.info("connection",isMuted?"muted":"unmuted",null,this);this._log.debug("#mute",isMuted);this.emit("mute",isMuted,this)}};Call.prototype.postFeedback=function(score,issue){if(typeof score==="undefined"||score===null){return this._postFeedbackDeclined()}if(!Object.values(Call.FeedbackScore).includes(score)){throw new errors_1.InvalidArgumentError("Feedback score must be one of: "+Object.values(Call.FeedbackScore))}if(typeof issue!=="undefined"&&issue!==null&&!Object.values(Call.FeedbackIssue).includes(issue)){throw new errors_1.InvalidArgumentError("Feedback issue must be one of: "+Object.values(Call.FeedbackIssue))}return this._publisher.info("feedback","received",{issue_name:issue,quality_score:score},this,true)};Call.prototype.reject=function(){if(this._status!==Call.State.Pending){return}this._log.debug(".reject");this._isRejected=true;this._pstream.reject(this.parameters.CallSid);this._mediaHandler.reject(this.parameters.CallSid);this._publisher.info("connection","rejected-by-local",null,this);this._cleanupEventListeners();this._mediaHandler.close();this._status=Call.State.Closed;this._log.debug("#reject");this.emit("reject")};Call.prototype.sendDigits=function(digits){var _this=this;this._log.debug(".sendDigits",digits);if(digits.match(/[^0-9*#w]/)){throw new errors_1.InvalidArgumentError("Illegal character passed into sendDigits")}var customSounds=this._options.customSounds||{};var sequence=[];digits.split("").forEach(function(digit){var dtmf=digit!=="w"?"dtmf"+digit:"";if(dtmf==="dtmf*"){dtmf="dtmfs"}if(dtmf==="dtmf#"){dtmf="dtmfh"}sequence.push(dtmf)});var playNextDigit=function(){var digit=sequence.shift();if(digit){if(_this._options.dialtonePlayer&&!customSounds[digit]){_this._options.dialtonePlayer.play(digit)}else{_this._soundcache.get(digit).play()}}if(sequence.length){setTimeout(function(){return playNextDigit()},200)}};playNextDigit();var dtmfSender=this._mediaHandler.getOrCreateDTMFSender();function insertDTMF(dtmfs){if(!dtmfs.length){return}var dtmf=dtmfs.shift();if(dtmf&&dtmf.length){dtmfSender.insertDTMF(dtmf,DTMF_TONE_DURATION,DTMF_INTER_TONE_GAP)}setTimeout(insertDTMF.bind(null,dtmfs),DTMF_PAUSE_DURATION)}if(dtmfSender){if(!("canInsertDTMF"in dtmfSender)||dtmfSender.canInsertDTMF){this._log.info("Sending digits using RTCDTMFSender");insertDTMF(digits.split("w"));return}this._log.info("RTCDTMFSender cannot insert DTMF")}this._log.info("Sending digits over PStream");if(this._pstream!==null&&this._pstream.status!=="disconnected"){this._pstream.dtmf(this.parameters.CallSid,digits)}else{var error=new errors_1.GeneralErrors.ConnectionError("Could not send DTMF: Signaling channel is disconnected");this._log.debug("#error",error);this.emit("error",error)}};Call.prototype.sendMessage=function(message){this._log.debug(".sendMessage",JSON.stringify(message));var content=message.content,contentType=message.contentType,messageType=message.messageType;if(typeof content==="undefined"||content===null){throw new errors_1.InvalidArgumentError("`content` is empty")}if(typeof messageType!=="string"){throw new errors_1.InvalidArgumentError("`messageType` must be an enumeration value of `Call.MessageType` or "+"a string.")}if(messageType.length===0){throw new errors_1.InvalidArgumentError("`messageType` must be a non-empty string.")}if(this._pstream===null){throw new errors_1.InvalidStateError("Could not send CallMessage; Signaling channel is disconnected")}var callSid=this.parameters.CallSid;if(typeof this.parameters.CallSid==="undefined"){throw new errors_1.InvalidStateError("Could not send CallMessage; Call has no CallSid")}var voiceEventSid=this._voiceEventSidGenerator();this._messages.set(voiceEventSid,{content:content,contentType:contentType,messageType:messageType,voiceEventSid:voiceEventSid});this._pstream.sendMessage(callSid,content,contentType,messageType,voiceEventSid);return voiceEventSid};Call.prototype.status=function(){return this._status};Call.prototype._checkVolume=function(currentVolume,currentStreak,lastValue,direction){var wasWarningRaised=currentStreak>=10;var newStreak=0;if(lastValue===currentVolume){newStreak=currentStreak}if(newStreak>=10){this._emitWarning("audio-level-","constant-audio-"+direction+"-level",10,newStreak,false)}else if(wasWarningRaised){this._emitWarning("audio-level-","constant-audio-"+direction+"-level",10,newStreak,true)}return newStreak};Call.prototype._cleanupEventListeners=function(){var _this=this;var cleanup=function(){if(!_this._pstream){return}_this._pstream.removeListener("ack",_this._onAck);_this._pstream.removeListener("answer",_this._onAnswer);_this._pstream.removeListener("cancel",_this._onCancel);_this._pstream.removeListener("error",_this._onSignalingError);_this._pstream.removeListener("hangup",_this._onHangup);_this._pstream.removeListener("ringing",_this._onRinging);_this._pstream.removeListener("transportClose",_this._onTransportClose);_this._pstream.removeListener("connected",_this._onConnected);_this._pstream.removeListener("message",_this._onMessageReceived)};cleanup();setTimeout(cleanup,0)};Call.prototype._createMetricPayload=function(){var payload={call_sid:this.parameters.CallSid,dscp:!!this._options.dscp,sdk_version:constants_1.RELEASE_VERSION};if(this._options.gateway){payload.gateway=this._options.gateway}payload.direction=this._direction;return payload};Call.prototype._disconnect=function(message,wasRemote){message=typeof message==="string"?message:null;if(this._status!==Call.State.Open&&this._status!==Call.State.Connecting&&this._status!==Call.State.Reconnecting&&this._status!==Call.State.Ringing){return}this._log.info("Disconnecting...");if(this._pstream!==null&&this._pstream.status!=="disconnected"&&this._shouldSendHangup){var callsid=this.parameters.CallSid||this.outboundConnectionId;if(callsid){this._pstream.hangup(callsid,message)}}this._cleanupEventListeners();this._mediaHandler.close();if(!wasRemote){this._publisher.info("connection","disconnected-by-local",null,this)}};Call.prototype._maybeTransitionToOpen=function(){var wasConnected=this._wasConnected;if(this._isAnswered){this._onSignalingReconnected();this._signalingStatus=Call.State.Open;if(this._mediaHandler&&this._mediaHandler.status==="open"){this._status=Call.State.Open;if(!this._wasConnected){this._wasConnected=true;this._log.debug("#accept");this.emit("accept",this)}}}};Call.prototype._postFeedbackDeclined=function(){return this._publisher.info("feedback","received-none",null,this,true)};Call.prototype._publishMetrics=function(){var _this=this;if(this._metricsSamples.length===0){return}this._publisher.postMetrics("quality-metrics-samples","metrics-sample",this._metricsSamples.splice(0),this._createMetricPayload(),this).catch(function(e){_this._log.warn("Unable to post metrics to Insights. Received error:",e)})};Call.prototype._setCallSid=function(payload){var callSid=payload.callsid;if(!callSid){return}this.parameters.CallSid=callSid;this._mediaHandler.callSid=callSid};Call.toString=function(){return"[Twilio.Call class]"};return Call}(events_1.EventEmitter);(function(Call){var State;(function(State){State["Closed"]="closed";State["Connecting"]="connecting";State["Open"]="open";State["Pending"]="pending";State["Reconnecting"]="reconnecting";State["Ringing"]="ringing"})(State=Call.State||(Call.State={}));var FeedbackIssue;(function(FeedbackIssue){FeedbackIssue["AudioLatency"]="audio-latency";FeedbackIssue["ChoppyAudio"]="choppy-audio";FeedbackIssue["DroppedCall"]="dropped-call";FeedbackIssue["Echo"]="echo";FeedbackIssue["NoisyCall"]="noisy-call";FeedbackIssue["OneWayAudio"]="one-way-audio"})(FeedbackIssue=Call.FeedbackIssue||(Call.FeedbackIssue={}));var FeedbackScore;(function(FeedbackScore){FeedbackScore[FeedbackScore["One"]=1]="One";FeedbackScore[FeedbackScore["Two"]=2]="Two";FeedbackScore[FeedbackScore["Three"]=3]="Three";FeedbackScore[FeedbackScore["Four"]=4]="Four";FeedbackScore[FeedbackScore["Five"]=5]="Five"})(FeedbackScore=Call.FeedbackScore||(Call.FeedbackScore={}));var CallDirection;(function(CallDirection){CallDirection["Incoming"]="INCOMING";CallDirection["Outgoing"]="OUTGOING"})(CallDirection=Call.CallDirection||(Call.CallDirection={}));var Codec;(function(Codec){Codec["Opus"]="opus";Codec["PCMU"]="pcmu"})(Codec=Call.Codec||(Call.Codec={}));var IceGatheringFailureReason;(function(IceGatheringFailureReason){IceGatheringFailureReason["None"]="none";IceGatheringFailureReason["Timeout"]="timeout"})(IceGatheringFailureReason=Call.IceGatheringFailureReason||(Call.IceGatheringFailureReason={}));var MediaFailure;(function(MediaFailure){MediaFailure["ConnectionDisconnected"]="ConnectionDisconnected";MediaFailure["ConnectionFailed"]="ConnectionFailed";MediaFailure["IceGatheringFailed"]="IceGatheringFailed";MediaFailure["LowBytes"]="LowBytes"})(MediaFailure=Call.MediaFailure||(Call.MediaFailure={}));var MessageType;(function(MessageType){MessageType["UserDefinedMessage"]="user-defined-message"})(MessageType=Call.MessageType||(Call.MessageType={}))})(Call||(Call={}));function generateTempCallSid(){return"TJSxxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(c){var r=Math.random()*16|0;var v=c==="x"?r:r&3|8;return v.toString(16)})}exports.default=Call},{"./backoff":8,"./constants":10,"./device":12,"./errors":15,"./log":18,"./rtc":26,"./rtc/icecandidate":25,"./rtc/sdp":31,"./statsMonitor":35,"./util":36,"./uuid":37,events:39}],10:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});exports.SOUNDS_BASE_URL=exports.RELEASE_VERSION=exports.PACKAGE_NAME=exports.ECHO_TEST_DURATION=exports.COWBELL_AUDIO_URL=void 0;var PACKAGE_NAME="@twilio/voice-sdk";exports.PACKAGE_NAME=PACKAGE_NAME;var RELEASE_VERSION="2.10.1";exports.RELEASE_VERSION=RELEASE_VERSION;var SOUNDS_BASE_URL="https://sdk.twilio.com/js/client/sounds/releases/1.0.0";exports.SOUNDS_BASE_URL=SOUNDS_BASE_URL;var COWBELL_AUDIO_URL=SOUNDS_BASE_URL+"/cowbell.mp3?cache="+RELEASE_VERSION;exports.COWBELL_AUDIO_URL=COWBELL_AUDIO_URL;var ECHO_TEST_DURATION=2e4;exports.ECHO_TEST_DURATION=ECHO_TEST_DURATION},{}],11:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var Deferred=function(){function Deferred(){var _this=this;this._promise=new Promise(function(resolve,reject){_this._resolve=resolve;_this._reject=reject})}Object.defineProperty(Deferred.prototype,"promise",{get:function(){return this._promise},enumerable:false,configurable:true});Deferred.prototype.reject=function(reason){this._reject(reason)};Deferred.prototype.resolve=function(value){this._resolve(value)};return Deferred}();exports.default=Deferred},{}],12:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();var __assign=this&&this.__assign||function(){__assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)if(Object.prototype.hasOwnProperty.call(s,p))t[p]=s[p]}return t};return __assign.apply(this,arguments)};var __awaiter=this&&this.__awaiter||function(thisArg,_arguments,P,generator){function adopt(value){return value instanceof P?value:new P(function(resolve){resolve(value)})}return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value))}catch(e){reject(e)}}function rejected(value){try{step(generator["throw"](value))}catch(e){reject(e)}}function step(result){result.done?resolve(result.value):adopt(result.value).then(fulfilled,rejected)}step((generator=generator.apply(thisArg,_arguments||[])).next())})};var __generator=this&&this.__generator||function(thisArg,body){var _={label:0,sent:function(){if(t[0]&1)throw t[1];return t[1]},trys:[],ops:[]},f,y,t,g;return g={next:verb(0),throw:verb(1),return:verb(2)},typeof Symbol==="function"&&(g[Symbol.iterator]=function(){return this}),g;function verb(n){return function(v){return step([n,v])}}function step(op){if(f)throw new TypeError("Generator is already executing.");while(_)try{if(f=1,y&&(t=op[0]&2?y["return"]:op[0]?y["throw"]||((t=y["return"])&&t.call(y),0):y.next)&&!(t=t.call(y,op[1])).done)return t;if(y=0,t)op=[op[0]&2,t.value];switch(op[0]){case 0:case 1:t=op;break;case 4:_.label++;return{value:op[1],done:false};case 5:_.label++;y=op[1];op=[0];continue;case 7:op=_.ops.pop();_.trys.pop();continue;default:if(!(t=_.trys,t=t.length>0&&t[t.length-1])&&(op[0]===6||op[0]===2)){_=0;continue}if(op[0]===3&&(!t||op[1]>t[0]&&op[1]<t[3])){_.label=op[1];break}if(op[0]===6&&_.label<t[1]){_.label=t[1];t=op;break}if(t&&_.label<t[2]){_.label=t[2];_.ops.push(op);break}if(t[2])_.ops.pop();_.trys.pop();continue}op=body.call(thisArg,_)}catch(e){op=[6,e];y=0}finally{f=t=0}if(op[0]&5)throw op[1];return{value:op[0]?op[1]:void 0,done:true}}};Object.defineProperty(exports,"__esModule",{value:true});var events_1=require("events");var loglevel_1=require("loglevel");var audiohelper_1=require("./audiohelper");var audioprocessoreventobserver_1=require("./audioprocessoreventobserver");var call_1=require("./call");var C=require("./constants");var dialtonePlayer_1=require("./dialtonePlayer");var errors_1=require("./errors");var eventpublisher_1=require("./eventpublisher");var log_1=require("./log");var preflight_1=require("./preflight/preflight");var pstream_1=require("./pstream");var regions_1=require("./regions");var rtc=require("./rtc");var getusermedia_1=require("./rtc/getusermedia");var sound_1=require("./sound");var util_1=require("./util");var uuid_1=require("./uuid");var REGISTRATION_INTERVAL=3e4;var RINGTONE_PLAY_TIMEOUT=2e3;var PUBLISHER_PRODUCT_NAME="twilio-js-sdk";var INVALID_TOKEN_MESSAGE='Parameter "token" must be of type "string".';var Device=function(_super){__extends(Device,_super);function Device(token,options){var _a;if(options===void 0){options={}}var _this=_super.call(this)||this;_this._activeCall=null;_this._audio=null;_this._audioProcessorEventObserver=null;_this._callInputStream=null;_this._calls=[];_this._callSinkIds=["default"];_this._chunderURIs=[];_this._defaultOptions={allowIncomingWhileBusy:false,closeProtection:false,codecPreferences:[call_1.default.Codec.PCMU,call_1.default.Codec.Opus],dscp:true,enableImprovedSignalingErrorPrecision:false,forceAggressiveIceNomination:false,logLevel:loglevel_1.levels.ERROR,maxCallSignalingTimeoutMs:0,preflight:false,sounds:{},tokenRefreshMs:1e4,voiceEventSidGenerator:uuid_1.generateVoiceEventSid};_this._edge=null;_this._home=null;_this._identity=null;_this._log=new log_1.default("Device");_this._options={};_this._preferredURI=null;_this._publisher=null;_this._region=null;_this._regTimer=null;_this._shouldReRegister=false;_this._soundcache=new Map;_this._state=Device.State.Unregistered;_this._stateEventMapping=(_a={},_a[Device.State.Destroyed]=Device.EventName.Destroyed,_a[Device.State.Unregistered]=Device.EventName.Unregistered,_a[Device.State.Registering]=Device.EventName.Registering,_a[Device.State.Registered]=Device.EventName.Registered,_a);_this._stream=null;_this._streamConnectedPromise=null;_this._tokenWillExpireTimeout=null;_this._createDefaultPayload=function(call){var payload={aggressive_nomination:_this._options.forceAggressiveIceNomination,browser_extension:_this._isBrowserExtension,dscp:!!_this._options.dscp,ice_restart_enabled:true,platform:rtc.getMediaEngine(),sdk_version:C.RELEASE_VERSION};function setIfDefined(propertyName,value){if(value){payload[propertyName]=value}}if(call){var callSid=call.parameters.CallSid;setIfDefined("call_sid",/^TJ/.test(callSid)?undefined:callSid);setIfDefined("temp_call_sid",call.outboundConnectionId);setIfDefined("audio_codec",call.codec);payload.direction=call.direction}setIfDefined("gateway",_this._stream&&_this._stream.gateway);setIfDefined("region",_this._stream&&_this._stream.region);return payload};_this._onSignalingClose=function(){_this._stream=null;_this._streamConnectedPromise=null};_this._onSignalingConnected=function(payload){var _a;var region=regions_1.getRegionShortcode(payload.region);_this._edge=payload.edge||regions_1.regionToEdge[region]||payload.region;_this._region=region||payload.region;_this._home=payload.home;(_a=_this._publisher)===null||_a===void 0?void 0:_a.setHost(regions_1.createEventGatewayURI(payload.home));if(payload.token){_this._identity=payload.token.identity;if(typeof payload.token.ttl==="number"&&typeof _this._options.tokenRefreshMs==="number"){var ttlMs=payload.token.ttl*1e3;var timeoutMs=Math.max(0,ttlMs-_this._options.tokenRefreshMs);_this._tokenWillExpireTimeout=setTimeout(function(){_this._log.debug("#tokenWillExpire");_this.emit("tokenWillExpire",_this);if(_this._tokenWillExpireTimeout){clearTimeout(_this._tokenWillExpireTimeout);_this._tokenWillExpireTimeout=null}},timeoutMs)}}var preferredURIs=regions_1.getChunderURIs(_this._edge);if(preferredURIs.length>0){var preferredURI=preferredURIs[0];_this._preferredURI=regions_1.createSignalingEndpointURL(preferredURI)}else{_this._log.warn("Could not parse a preferred URI from the stream#connected event.")}if(_this._shouldReRegister){_this.register()}};_this._onSignalingError=function(payload){if(typeof payload!=="object"){return}var originalError=payload.error,callsid=payload.callsid;if(typeof originalError!=="object"){return}var call=typeof callsid==="string"&&_this._findCall(callsid)||undefined;var code=originalError.code,customMessage=originalError.message;var twilioError=originalError.twilioError;if(typeof code==="number"){if(code===31201){twilioError=new errors_1.AuthorizationErrors.AuthenticationFailed(originalError)}else if(code===31204){twilioError=new errors_1.AuthorizationErrors.AccessTokenInvalid(originalError)}else if(code===31205){_this._stopRegistrationTimer();twilioError=new errors_1.AuthorizationErrors.AccessTokenExpired(originalError)}else{var errorConstructor=errors_1.getPreciseSignalingErrorByCode(!!_this._options.enableImprovedSignalingErrorPrecision,code);if(typeof errorConstructor!=="undefined"){twilioError=new errorConstructor(originalError)}}}if(!twilioError){_this._log.error("Unknown signaling error: ",originalError);twilioError=new errors_1.GeneralErrors.UnknownError(customMessage,originalError)}_this._log.error("Received error: ",twilioError);_this._log.debug("#error",originalError);_this.emit(Device.EventName.Error,twilioError,call)};_this._onSignalingInvite=function(payload){return __awaiter(_this,void 0,void 0,function(){var wasBusy,callParameters,customParameters,call,play;var _this=this;var _a;return __generator(this,function(_b){switch(_b.label){case 0:wasBusy=!!this._activeCall;if(wasBusy&&!this._options.allowIncomingWhileBusy){this._log.info("Device busy; ignoring incoming invite");return[2]}if(!payload.callsid||!payload.sdp){this._log.debug("#error",payload);this.emit(Device.EventName.Error,new errors_1.ClientErrors.BadRequest("Malformed invite from gateway"));return[2]}callParameters=payload.parameters||{};callParameters.CallSid=callParameters.CallSid||payload.callsid;customParameters=Object.assign({},util_1.queryToJson(callParameters.Params));return[4,this._makeCall(customParameters,{callParameters:callParameters,enableImprovedSignalingErrorPrecision:!!this._options.enableImprovedSignalingErrorPrecision,offerSdp:payload.sdp,reconnectToken:payload.reconnect,voiceEventSidGenerator:this._options.voiceEventSidGenerator})];case 1:call=_b.sent();this._calls.push(call);call.once("accept",function(){_this._soundcache.get(Device.SoundName.Incoming).stop();_this._publishNetworkChange()});play=((_a=this._audio)===null||_a===void 0?void 0:_a.incoming())&&!wasBusy?function(){return _this._soundcache.get(Device.SoundName.Incoming).play()}:function(){return Promise.resolve()};this._showIncomingCall(call,play);return[2]}})})};_this._onSignalingOffline=function(){_this._log.info("Stream is offline");_this._edge=null;_this._region=null;_this._shouldReRegister=_this.state!==Device.State.Unregistered;_this._setState(Device.State.Unregistered)};_this._onSignalingReady=function(){_this._log.info("Stream is ready");_this._setState(Device.State.Registered)};_this._publishNetworkChange=function(){if(!_this._activeCall){return}if(_this._networkInformation){_this._publisher.info("network-information","network-change",{connection_type:_this._networkInformation.type,downlink:_this._networkInformation.downlink,downlinkMax:_this._networkInformation.downlinkMax,effective_type:_this._networkInformation.effectiveType,rtt:_this._networkInformation.rtt},_this._activeCall)}};_this._updateInputStream=function(inputStream){var call=_this._activeCall;if(call&&!inputStream){return Promise.reject(new errors_1.InvalidStateError("Cannot unset input device while a call is in progress."))}_this._callInputStream=inputStream;return call?call._setInputTracksFromStream(inputStream):Promise.resolve()};_this._updateSinkIds=function(type,sinkIds){var promise=type==="ringtone"?_this._updateRingtoneSinkIds(sinkIds):_this._updateSpeakerSinkIds(sinkIds);return promise.then(function(){_this._publisher.info("audio",type+"-devices-set",{audio_device_ids:sinkIds},_this._activeCall)},function(error){_this._publisher.error("audio",type+"-devices-set-failed",{audio_device_ids:sinkIds,message:error.message},_this._activeCall);throw error})};_this._setupLoglevel(options.logLevel);_this._logOptions("constructor",options);_this.updateToken(token);if(util_1.isLegacyEdge()){throw new errors_1.NotSupportedError("Microsoft Edge Legacy (https://support.microsoft.com/en-us/help/4533505/what-is-microsoft-edge-legacy) "+"is deprecated and will not be able to connect to Twilio to make or receive calls after September 1st, 2020. "+"Please see this documentation for a list of supported browsers "+"https://www.twilio.com/docs/voice/client/javascript#supported-browsers")}if(!Device.isSupported&&options.ignoreBrowserSupport){if(window&&window.location&&window.location.protocol==="http:"){throw new errors_1.NotSupportedError("twilio.js wasn't able to find WebRTC browser support.           This is most likely because this page is served over http rather than https,           which does not support WebRTC in many browsers. Please load this page over https and           try again.")}throw new errors_1.NotSupportedError("twilio.js 1.3+ SDKs require WebRTC browser support.         For more information, see <https://www.twilio.com/docs/api/client/twilio-js>.         If you have any questions about this announcement, please contact         Twilio Support at <<EMAIL>>.")}if(window){var root=window;var browser=root.msBrowser||root.browser||root.chrome;_this._isBrowserExtension=!!browser&&!!browser.runtime&&!!browser.runtime.id||!!root.safari&&!!root.safari.extension}if(_this._isBrowserExtension){_this._log.info("Running as browser extension.")}if(navigator){var n=navigator;_this._networkInformation=n.connection||n.mozConnection||n.webkitConnection}if(_this._networkInformation&&typeof _this._networkInformation.addEventListener==="function"){_this._networkInformation.addEventListener("change",_this._publishNetworkChange)}Device._getOrCreateAudioContext();if(Device._audioContext){if(!Device._dialtonePlayer){Device._dialtonePlayer=new dialtonePlayer_1.default(Device._audioContext)}}if(typeof Device._isUnifiedPlanDefault==="undefined"){Device._isUnifiedPlanDefault=typeof window!=="undefined"&&typeof RTCPeerConnection!=="undefined"&&typeof RTCRtpTransceiver!=="undefined"?util_1.isUnifiedPlanDefault(window,window.navigator,RTCPeerConnection,RTCRtpTransceiver):false}_this._boundDestroy=_this.destroy.bind(_this);_this._boundConfirmClose=_this._confirmClose.bind(_this);if(typeof window!=="undefined"&&window.addEventListener){window.addEventListener("unload",_this._boundDestroy);window.addEventListener("pagehide",_this._boundDestroy)}_this.updateOptions(options);return _this}Object.defineProperty(Device,"audioContext",{get:function(){return Device._audioContext},enumerable:false,configurable:true});Object.defineProperty(Device,"extension",{get:function(){var a=typeof document!=="undefined"?document.createElement("audio"):{canPlayType:false};var canPlayMp3;try{canPlayMp3=a.canPlayType&&!!a.canPlayType("audio/mpeg").replace(/no/,"")}catch(e){canPlayMp3=false}var canPlayVorbis;try{canPlayVorbis=a.canPlayType&&!!a.canPlayType("audio/ogg;codecs='vorbis'").replace(/no/,"")}catch(e){canPlayVorbis=false}return canPlayVorbis&&!canPlayMp3?"ogg":"mp3"},enumerable:false,configurable:true});Object.defineProperty(Device,"isSupported",{get:function(){return rtc.enabled()},enumerable:false,configurable:true});Object.defineProperty(Device,"packageName",{get:function(){return C.PACKAGE_NAME},enumerable:false,configurable:true});Device.runPreflight=function(token,options){return new preflight_1.PreflightTest(token,__assign({audioContext:Device._getOrCreateAudioContext()},options))};Device.toString=function(){return"[Twilio.Device class]"};Object.defineProperty(Device,"version",{get:function(){return C.RELEASE_VERSION},enumerable:false,configurable:true});Device._getOrCreateAudioContext=function(){if(!Device._audioContext){if(typeof AudioContext!=="undefined"){Device._audioContext=new AudioContext}else if(typeof webkitAudioContext!=="undefined"){Device._audioContext=new webkitAudioContext}}return Device._audioContext};Object.defineProperty(Device.prototype,"audio",{get:function(){return this._audio},enumerable:false,configurable:true});Device.prototype.connect=function(options){if(options===void 0){options={}}return __awaiter(this,void 0,void 0,function(){var activeCall,_a;return __generator(this,function(_b){switch(_b.label){case 0:this._log.debug(".connect",JSON.stringify(options&&options.params||{}),options);this._throwIfDestroyed();if(this._activeCall){throw new errors_1.InvalidStateError("A Call is already active")}_a=this;return[4,this._makeCall(options.params||{},{enableImprovedSignalingErrorPrecision:!!this._options.enableImprovedSignalingErrorPrecision,rtcConfiguration:options.rtcConfiguration,voiceEventSidGenerator:this._options.voiceEventSidGenerator})];case 1:activeCall=_a._activeCall=_b.sent();this._calls.splice(0).forEach(function(call){return call.ignore()});this._soundcache.get(Device.SoundName.Incoming).stop();activeCall.accept({rtcConstraints:options.rtcConstraints});this._publishNetworkChange();return[2,activeCall]}})})};Object.defineProperty(Device.prototype,"calls",{get:function(){return this._calls},enumerable:false,configurable:true});Device.prototype.destroy=function(){var _a;this._log.debug(".destroy");this.disconnectAll();this._stopRegistrationTimer();this._destroyStream();this._destroyPublisher();this._destroyAudioHelper();(_a=this._audioProcessorEventObserver)===null||_a===void 0?void 0:_a.destroy();if(this._networkInformation&&typeof this._networkInformation.removeEventListener==="function"){this._networkInformation.removeEventListener("change",this._publishNetworkChange)}if(typeof window!=="undefined"&&window.removeEventListener){window.removeEventListener("beforeunload",this._boundConfirmClose);window.removeEventListener("unload",this._boundDestroy);window.removeEventListener("pagehide",this._boundDestroy)}this._setState(Device.State.Destroyed);events_1.EventEmitter.prototype.removeAllListeners.call(this)};Device.prototype.disconnectAll=function(){this._log.debug(".disconnectAll");var calls=this._calls.splice(0);calls.forEach(function(call){return call.disconnect()});if(this._activeCall){this._activeCall.disconnect()}};Object.defineProperty(Device.prototype,"edge",{get:function(){return this._edge},enumerable:false,configurable:true});Object.defineProperty(Device.prototype,"home",{get:function(){return this._home},enumerable:false,configurable:true});Object.defineProperty(Device.prototype,"identity",{get:function(){return this._identity},enumerable:false,configurable:true});Object.defineProperty(Device.prototype,"isBusy",{get:function(){return!!this._activeCall},enumerable:false,configurable:true});Device.prototype.register=function(){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(_a){switch(_a.label){case 0:this._log.debug(".register");if(this.state!==Device.State.Unregistered){throw new errors_1.InvalidStateError('Attempt to register when device is in state "'+this.state+'". '+('Must be "'+Device.State.Unregistered+'".'))}this._shouldReRegister=false;this._setState(Device.State.Registering);return[4,this._streamConnectedPromise||this._setupStream()];case 1:_a.sent();return[4,this._sendPresence(true)];case 2:_a.sent();return[4,util_1.promisifyEvents(this,Device.State.Registered,Device.State.Unregistered)];case 3:_a.sent();return[2]}})})};Object.defineProperty(Device.prototype,"state",{get:function(){return this._state},enumerable:false,configurable:true});Object.defineProperty(Device.prototype,"token",{get:function(){return this._token},enumerable:false,configurable:true});Device.prototype.toString=function(){return"[Twilio.Device instance]"};Device.prototype.unregister=function(){return __awaiter(this,void 0,void 0,function(){var stream,streamOfflinePromise;return __generator(this,function(_a){switch(_a.label){case 0:this._log.debug(".unregister");if(this.state!==Device.State.Registered){throw new errors_1.InvalidStateError('Attempt to unregister when device is in state "'+this.state+'". '+('Must be "'+Device.State.Registered+'".'))}this._shouldReRegister=false;return[4,this._streamConnectedPromise];case 1:stream=_a.sent();streamOfflinePromise=new Promise(function(resolve){stream.on("offline",resolve)});return[4,this._sendPresence(false)];case 2:_a.sent();return[4,streamOfflinePromise];case 3:_a.sent();return[2]}})})};Device.prototype.updateOptions=function(options){if(options===void 0){options={}}this._logOptions("updateOptions",options);if(this.state===Device.State.Destroyed){throw new errors_1.InvalidStateError('Attempt to "updateOptions" when device is in state "'+this.state+'".')}this._options=__assign(__assign(__assign({},this._defaultOptions),this._options),options);var originalChunderURIs=new Set(this._chunderURIs);var chunderw=typeof this._options.chunderw==="string"?[this._options.chunderw]:Array.isArray(this._options.chunderw)&&this._options.chunderw;var newChunderURIs=this._chunderURIs=(chunderw||regions_1.getChunderURIs(this._options.edge)).map(regions_1.createSignalingEndpointURL);var hasChunderURIsChanged=originalChunderURIs.size!==newChunderURIs.length;if(!hasChunderURIsChanged){for(var _i=0,newChunderURIs_1=newChunderURIs;_i<newChunderURIs_1.length;_i++){var uri=newChunderURIs_1[_i];if(!originalChunderURIs.has(uri)){hasChunderURIsChanged=true;break}}}if(this.isBusy&&hasChunderURIsChanged){throw new errors_1.InvalidStateError("Cannot change Edge while on an active Call")}this._setupLoglevel(this._options.logLevel);if(this._options.dscp){if(!this._options.rtcConstraints){this._options.rtcConstraints={}}this._options.rtcConstraints.optional=[{googDscp:true}]}for(var _a=0,_b=Object.keys(Device._defaultSounds);_a<_b.length;_a++){var name_1=_b[_a];var soundDef=Device._defaultSounds[name_1];var defaultUrl=C.SOUNDS_BASE_URL+"/"+soundDef.filename+"."+Device.extension+("?cache="+C.RELEASE_VERSION);var soundUrl=this._options.sounds&&this._options.sounds[name_1]||defaultUrl;var sound=new(this._options.Sound||sound_1.default)(name_1,soundUrl,{audioContext:this._options.disableAudioContextSounds?null:Device.audioContext,maxDuration:soundDef.maxDuration,shouldLoop:soundDef.shouldLoop});this._soundcache.set(name_1,sound)}this._setupAudioHelper();this._setupPublisher();if(hasChunderURIsChanged&&this._streamConnectedPromise){this._setupStream()}if(typeof window!=="undefined"&&typeof window.addEventListener==="function"&&this._options.closeProtection){window.removeEventListener("beforeunload",this._boundConfirmClose);window.addEventListener("beforeunload",this._boundConfirmClose)}};Device.prototype.updateToken=function(token){this._log.debug(".updateToken");if(this.state===Device.State.Destroyed){throw new errors_1.InvalidStateError('Attempt to "updateToken" when device is in state "'+this.state+'".')}if(typeof token!=="string"){throw new errors_1.InvalidArgumentError(INVALID_TOKEN_MESSAGE)}this._token=token;if(this._stream){this._stream.setToken(this._token)}if(this._publisher){this._publisher.setToken(this._token)}};Device.prototype._confirmClose=function(event){if(!this._activeCall){return""}var closeProtection=this._options.closeProtection||false;var confirmationMsg=typeof closeProtection!=="string"?"A call is currently in-progress. Leaving or reloading this page will end the call.":closeProtection;(event||window.event).returnValue=confirmationMsg;return confirmationMsg};Device.prototype._destroyAudioHelper=function(){if(!this._audio){return}this._audio._destroy();this._audio=null};Device.prototype._destroyPublisher=function(){if(!this._publisher){return}this._publisher=null};Device.prototype._destroyStream=function(){if(this._stream){this._stream.removeListener("close",this._onSignalingClose);this._stream.removeListener("connected",this._onSignalingConnected);this._stream.removeListener("error",this._onSignalingError);this._stream.removeListener("invite",this._onSignalingInvite);this._stream.removeListener("offline",this._onSignalingOffline);this._stream.removeListener("ready",this._onSignalingReady);this._stream.destroy();this._stream=null}this._onSignalingOffline();this._streamConnectedPromise=null};Device.prototype._findCall=function(callSid){return this._calls.find(function(call){return call.parameters.CallSid===callSid||call.outboundConnectionId===callSid})||null};Device.prototype._logOptions=function(caller,options){if(options===void 0){options={}}var userOptions=["allowIncomingWhileBusy","appName","appVersion","closeProtection","codecPreferences","disableAudioContextSounds","dscp","edge","enableImprovedSignalingErrorPrecision","forceAggressiveIceNomination","logLevel","maxAverageBitrate","maxCallSignalingTimeoutMs","sounds","tokenRefreshMs"];var userOptionOverrides=["RTCPeerConnection","enumerateDevices","getUserMedia"];if(typeof options==="object"){var toLog_1=__assign({},options);Object.keys(toLog_1).forEach(function(key){if(!userOptions.includes(key)&&!userOptionOverrides.includes(key)){delete toLog_1[key]}if(userOptionOverrides.includes(key)){toLog_1[key]=true}});this._log.debug("."+caller,JSON.stringify(toLog_1))}};Device.prototype._makeCall=function(twimlParams,options){return __awaiter(this,void 0,void 0,function(){var config,maybeUnsetPreferredUri,call;var _a;var _this=this;return __generator(this,function(_b){switch(_b.label){case 0:if(typeof Device._isUnifiedPlanDefault==="undefined"){throw new errors_1.InvalidStateError("Device has not been initialized.")}_a={audioHelper:this._audio,isUnifiedPlanDefault:Device._isUnifiedPlanDefault,onIgnore:function(){_this._soundcache.get(Device.SoundName.Incoming).stop()}};return[4,this._streamConnectedPromise||this._setupStream()];case 1:config=(_a.pstream=_b.sent(),_a.publisher=this._publisher,_a.soundcache=this._soundcache,_a);options=Object.assign({MediaStream:this._options.MediaStream||rtc.PeerConnection,RTCPeerConnection:this._options.RTCPeerConnection,beforeAccept:function(currentCall){if(!_this._activeCall||_this._activeCall===currentCall){return}_this._activeCall.disconnect();_this._removeCall(_this._activeCall)},codecPreferences:this._options.codecPreferences,customSounds:this._options.sounds,dialtonePlayer:Device._dialtonePlayer,dscp:this._options.dscp,forceAggressiveIceNomination:this._options.forceAggressiveIceNomination,getInputStream:function(){return _this._options.fileInputStream||_this._callInputStream},getSinkIds:function(){return _this._callSinkIds},maxAverageBitrate:this._options.maxAverageBitrate,preflight:this._options.preflight,rtcConstraints:this._options.rtcConstraints,shouldPlayDisconnect:function(){var _a;return(_a=_this._audio)===null||_a===void 0?void 0:_a.disconnect()},twimlParams:twimlParams,voiceEventSidGenerator:this._options.voiceEventSidGenerator},options);maybeUnsetPreferredUri=function(){if(!_this._stream){_this._log.warn("UnsetPreferredUri called without a stream");return}if(_this._activeCall===null&&_this._calls.length===0){_this._stream.updatePreferredURI(null)}};call=new(this._options.Call||call_1.default)(config,options);this._publisher.info("settings","init",{RTCPeerConnection:!!this._options.RTCPeerConnection,enumerateDevices:!!this._options.enumerateDevices,getUserMedia:!!this._options.getUserMedia},call);call.once("accept",function(){var _a,_b,_c;_this._stream.updatePreferredURI(_this._preferredURI);_this._removeCall(call);_this._activeCall=call;if(_this._audio){_this._audio._maybeStartPollingVolume()}if(call.direction===call_1.default.CallDirection.Outgoing&&((_a=_this._audio)===null||_a===void 0?void 0:_a.outgoing())){_this._soundcache.get(Device.SoundName.Outgoing).play()}var data={edge:_this._edge||_this._region};if(_this._options.edge){data["selected_edge"]=Array.isArray(_this._options.edge)?_this._options.edge:[_this._options.edge]}_this._publisher.info("settings","edge",data,call);if((_b=_this._audio)===null||_b===void 0?void 0:_b.processedStream){(_c=_this._audioProcessorEventObserver)===null||_c===void 0?void 0:_c.emit("enabled")}});call.addListener("error",function(error){if(call.status()==="closed"){_this._removeCall(call);maybeUnsetPreferredUri()}if(_this._audio){_this._audio._maybeStopPollingVolume()}_this._maybeStopIncomingSound()});call.once("cancel",function(){_this._log.info("Canceled: "+call.parameters.CallSid);_this._removeCall(call);maybeUnsetPreferredUri();if(_this._audio){_this._audio._maybeStopPollingVolume()}_this._maybeStopIncomingSound()});call.once("disconnect",function(){if(_this._audio){_this._audio._maybeStopPollingVolume()}_this._removeCall(call);maybeUnsetPreferredUri();_this._maybeStopIncomingSound()});call.once("reject",function(){_this._log.info("Rejected: "+call.parameters.CallSid);if(_this._audio){_this._audio._maybeStopPollingVolume()}_this._removeCall(call);maybeUnsetPreferredUri();_this._maybeStopIncomingSound()});call.on("transportClose",function(){if(call.status()!==call_1.default.State.Pending){return}if(_this._audio){_this._audio._maybeStopPollingVolume()}_this._removeCall(call);_this._maybeStopIncomingSound()});return[2,call]}})})};Device.prototype._maybeStopIncomingSound=function(){if(!this._calls.length){this._soundcache.get(Device.SoundName.Incoming).stop()}};Device.prototype._removeCall=function(call){if(this._activeCall===call){this._activeCall=null}for(var i=this._calls.length-1;i>=0;i--){if(call===this._calls[i]){this._calls.splice(i,1)}}};Device.prototype._sendPresence=function(presence){return __awaiter(this,void 0,void 0,function(){var stream;return __generator(this,function(_a){switch(_a.label){case 0:return[4,this._streamConnectedPromise];case 1:stream=_a.sent();if(!stream){return[2]}stream.register({audio:presence});if(presence){this._startRegistrationTimer()}else{this._stopRegistrationTimer()}return[2]}})})};Device.prototype._setState=function(state){if(state===this.state){return}this._state=state;var name=this._stateEventMapping[state];this._log.debug("#"+name);this.emit(name)};Device.prototype._setupAudioHelper=function(){var _this=this;if(!this._audioProcessorEventObserver){this._audioProcessorEventObserver=new audioprocessoreventobserver_1.AudioProcessorEventObserver;this._audioProcessorEventObserver.on("event",function(_a){var name=_a.name,group=_a.group;_this._publisher.info(group,name,{},_this._activeCall)})}var audioOptions={audioContext:Device.audioContext,audioProcessorEventObserver:this._audioProcessorEventObserver,enumerateDevices:this._options.enumerateDevices,getUserMedia:this._options.getUserMedia||getusermedia_1.default};if(this._audio){this._log.info("Found existing audio helper; updating options...");this._audio._updateUserOptions(audioOptions);return}this._audio=new(this._options.AudioHelper||audiohelper_1.default)(this._updateSinkIds,this._updateInputStream,audioOptions);this._audio.on("deviceChange",function(lostActiveDevices){var activeCall=_this._activeCall;var deviceIds=lostActiveDevices.map(function(device){return device.deviceId});_this._publisher.info("audio","device-change",{lost_active_device_ids:deviceIds},activeCall);if(activeCall){activeCall["_mediaHandler"]._onInputDevicesChanged()}})};Device.prototype._setupLoglevel=function(logLevel){var level=typeof logLevel==="number"||typeof logLevel==="string"?logLevel:loglevel_1.levels.ERROR;this._log.setDefaultLevel(level);this._log.info("Set logger default level to",level)};Device.prototype._setupPublisher=function(){var _this=this;if(this._publisher){this._log.info("Found existing publisher; destroying...");this._destroyPublisher()}var publisherOptions={defaultPayload:this._createDefaultPayload,metadata:{app_name:this._options.appName,app_version:this._options.appVersion}};if(this._options.eventgw){publisherOptions.host=this._options.eventgw}if(this._home){publisherOptions.host=regions_1.createEventGatewayURI(this._home)}this._publisher=new(this._options.Publisher||eventpublisher_1.default)(PUBLISHER_PRODUCT_NAME,this.token,publisherOptions);if(this._options.publishEvents===false){this._publisher.disable()}else{this._publisher.on("error",function(error){_this._log.warn("Cannot connect to insights.",error)})}return this._publisher};Device.prototype._setupStream=function(){var _this=this;if(this._stream){this._log.info("Found existing stream; destroying...");this._destroyStream()}this._log.info("Setting up VSP");this._stream=new(this._options.PStream||pstream_1.default)(this.token,this._chunderURIs,{backoffMaxMs:this._options.backoffMaxMs,maxPreferredDurationMs:this._options.maxCallSignalingTimeoutMs});this._stream.addListener("close",this._onSignalingClose);this._stream.addListener("connected",this._onSignalingConnected);this._stream.addListener("error",this._onSignalingError);this._stream.addListener("invite",this._onSignalingInvite);this._stream.addListener("offline",this._onSignalingOffline);this._stream.addListener("ready",this._onSignalingReady);return this._streamConnectedPromise=util_1.promisifyEvents(this._stream,"connected","close").then(function(){return _this._stream})};Device.prototype._showIncomingCall=function(call,play){var _this=this;var timeout;return Promise.race([play(),new Promise(function(resolve,reject){timeout=setTimeout(function(){var msg="Playing incoming ringtone took too long; it might not play. Continuing execution...";reject(new Error(msg))},RINGTONE_PLAY_TIMEOUT)})]).catch(function(reason){_this._log.warn(reason.message)}).then(function(){clearTimeout(timeout);_this._log.debug("#incoming",JSON.stringify({customParameters:call.customParameters,parameters:call.parameters}));_this.emit(Device.EventName.Incoming,call)})};Device.prototype._startRegistrationTimer=function(){var _this=this;this._stopRegistrationTimer();this._regTimer=setTimeout(function(){_this._sendPresence(true)},REGISTRATION_INTERVAL)};Device.prototype._stopRegistrationTimer=function(){if(this._regTimer){clearTimeout(this._regTimer)}};Device.prototype._throwIfDestroyed=function(){if(this.state===Device.State.Destroyed){throw new errors_1.InvalidStateError("Device has been destroyed.")}};Device.prototype._updateRingtoneSinkIds=function(sinkIds){return Promise.resolve(this._soundcache.get(Device.SoundName.Incoming).setSinkIds(sinkIds))};Device.prototype._updateSpeakerSinkIds=function(sinkIds){Array.from(this._soundcache.entries()).filter(function(entry){return entry[0]!==Device.SoundName.Incoming}).forEach(function(entry){return entry[1].setSinkIds(sinkIds)});this._callSinkIds=sinkIds;var call=this._activeCall;return call?call._setSinkIds(sinkIds):Promise.resolve()};Device._defaultSounds={disconnect:{filename:"disconnect",maxDuration:3e3},dtmf0:{filename:"dtmf-0",maxDuration:1e3},dtmf1:{filename:"dtmf-1",maxDuration:1e3},dtmf2:{filename:"dtmf-2",maxDuration:1e3},dtmf3:{filename:"dtmf-3",maxDuration:1e3},dtmf4:{filename:"dtmf-4",maxDuration:1e3},dtmf5:{filename:"dtmf-5",maxDuration:1e3},dtmf6:{filename:"dtmf-6",maxDuration:1e3},dtmf7:{filename:"dtmf-7",maxDuration:1e3},dtmf8:{filename:"dtmf-8",maxDuration:1e3},dtmf9:{filename:"dtmf-9",maxDuration:1e3},dtmfh:{filename:"dtmf-hash",maxDuration:1e3},dtmfs:{filename:"dtmf-star",maxDuration:1e3},incoming:{filename:"incoming",shouldLoop:true},outgoing:{filename:"outgoing",maxDuration:3e3}};return Device}(events_1.EventEmitter);(function(Device){var EventName;(function(EventName){EventName["Error"]="error";EventName["Incoming"]="incoming";EventName["Destroyed"]="destroyed";EventName["Unregistered"]="unregistered";EventName["Registering"]="registering";EventName["Registered"]="registered";EventName["TokenWillExpire"]="tokenWillExpire"})(EventName=Device.EventName||(Device.EventName={}));var State;(function(State){State["Destroyed"]="destroyed";State["Unregistered"]="unregistered";State["Registering"]="registering";State["Registered"]="registered"})(State=Device.State||(Device.State={}));var SoundName;(function(SoundName){SoundName["Incoming"]="incoming";SoundName["Outgoing"]="outgoing";SoundName["Disconnect"]="disconnect";SoundName["Dtmf0"]="dtmf0";SoundName["Dtmf1"]="dtmf1";SoundName["Dtmf2"]="dtmf2";SoundName["Dtmf3"]="dtmf3";SoundName["Dtmf4"]="dtmf4";SoundName["Dtmf5"]="dtmf5";SoundName["Dtmf6"]="dtmf6";SoundName["Dtmf7"]="dtmf7";SoundName["Dtmf8"]="dtmf8";SoundName["Dtmf9"]="dtmf9";SoundName["DtmfS"]="dtmfs";SoundName["DtmfH"]="dtmfh"})(SoundName=Device.SoundName||(Device.SoundName={}))})(Device||(Device={}));exports.default=Device},{"./audiohelper":3,"./audioprocessoreventobserver":7,"./call":9,"./constants":10,"./dialtonePlayer":13,"./errors":15,"./eventpublisher":17,"./log":18,"./preflight/preflight":20,"./pstream":21,"./regions":22,"./rtc":26,"./rtc/getusermedia":24,"./sound":34,"./util":36,"./uuid":37,events:39,loglevel:43}],13:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var errors_1=require("./errors");var bandFrequencies={dtmf0:[1360,960],dtmf1:[1230,720],dtmf2:[1360,720],dtmf3:[1480,720],dtmf4:[1230,790],dtmf5:[1360,790],dtmf6:[1480,790],dtmf7:[1230,870],dtmf8:[1360,870],dtmf9:[1480,870],dtmfh:[1480,960],dtmfs:[1230,960]};var DialtonePlayer=function(){function DialtonePlayer(_context){var _this=this;this._context=_context;this._gainNodes=[];this._gainNodes=[this._context.createGain(),this._context.createGain()];this._gainNodes.forEach(function(gainNode){gainNode.connect(_this._context.destination);gainNode.gain.value=.1;_this._gainNodes.push(gainNode)})}DialtonePlayer.prototype.cleanup=function(){this._gainNodes.forEach(function(gainNode){gainNode.disconnect()})};DialtonePlayer.prototype.play=function(sound){var _this=this;var frequencies=bandFrequencies[sound];if(!frequencies){throw new errors_1.InvalidArgumentError("Invalid DTMF sound name")}var oscillators=[this._context.createOscillator(),this._context.createOscillator()];oscillators.forEach(function(oscillator,i){oscillator.type="sine";oscillator.frequency.value=frequencies[i];oscillator.connect(_this._gainNodes[i]);oscillator.start();oscillator.stop(_this._context.currentTime+.1);oscillator.addEventListener("ended",function(){return oscillator.disconnect()})})};return DialtonePlayer}();exports.default=DialtonePlayer},{"./errors":15}],14:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();Object.defineProperty(exports,"__esModule",{value:true});exports.errorsByCode=exports.MediaErrors=exports.SignalingErrors=exports.UserMediaErrors=exports.MalformedRequestErrors=exports.GeneralErrors=exports.SIPServerErrors=exports.ClientErrors=exports.SignatureValidationErrors=exports.AuthorizationErrors=exports.TwilioError=void 0;var twilioError_1=require("./twilioError");exports.TwilioError=twilioError_1.default;var AuthorizationErrors;(function(AuthorizationErrors){var AccessTokenInvalid=function(_super){__extends(AccessTokenInvalid,_super);function AccessTokenInvalid(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=20101;_this.description="Invalid access token";_this.explanation="Twilio was unable to validate your Access Token";_this.name="AccessTokenInvalid";_this.solutions=[];Object.setPrototypeOf(_this,AuthorizationErrors.AccessTokenInvalid.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return AccessTokenInvalid}(twilioError_1.default);AuthorizationErrors.AccessTokenInvalid=AccessTokenInvalid;var AccessTokenExpired=function(_super){__extends(AccessTokenExpired,_super);function AccessTokenExpired(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=20104;_this.description="Access token expired or expiration date invalid";_this.explanation="The Access Token provided to the Twilio API has expired, the expiration time specified in the token was invalid, or the expiration time specified was too far in the future";_this.name="AccessTokenExpired";_this.solutions=[];Object.setPrototypeOf(_this,AuthorizationErrors.AccessTokenExpired.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return AccessTokenExpired}(twilioError_1.default);AuthorizationErrors.AccessTokenExpired=AccessTokenExpired;var AuthenticationFailed=function(_super){__extends(AuthenticationFailed,_super);function AuthenticationFailed(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=20151;_this.description="Authentication Failed";_this.explanation="The Authentication with the provided JWT failed";_this.name="AuthenticationFailed";_this.solutions=[];Object.setPrototypeOf(_this,AuthorizationErrors.AuthenticationFailed.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return AuthenticationFailed}(twilioError_1.default);AuthorizationErrors.AuthenticationFailed=AuthenticationFailed})(AuthorizationErrors=exports.AuthorizationErrors||(exports.AuthorizationErrors={}));var SignatureValidationErrors;(function(SignatureValidationErrors){var AccessTokenSignatureValidationFailed=function(_super){__extends(AccessTokenSignatureValidationFailed,_super);function AccessTokenSignatureValidationFailed(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=["The access token has an invalid Account SID, API Key, or API Key Secret."];_this.code=31202;_this.description="Signature validation failed.";_this.explanation="The provided access token failed signature validation.";_this.name="AccessTokenSignatureValidationFailed";_this.solutions=["Ensure the Account SID, API Key, and API Key Secret are valid when generating your access token."];Object.setPrototypeOf(_this,SignatureValidationErrors.AccessTokenSignatureValidationFailed.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return AccessTokenSignatureValidationFailed}(twilioError_1.default);SignatureValidationErrors.AccessTokenSignatureValidationFailed=AccessTokenSignatureValidationFailed})(SignatureValidationErrors=exports.SignatureValidationErrors||(exports.SignatureValidationErrors={}));var ClientErrors;(function(ClientErrors){var BadRequest=function(_super){__extends(BadRequest,_super);function BadRequest(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31400;_this.description="Bad Request (HTTP/SIP)";_this.explanation="The request could not be understood due to malformed syntax.";_this.name="BadRequest";_this.solutions=[];Object.setPrototypeOf(_this,ClientErrors.BadRequest.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return BadRequest}(twilioError_1.default);ClientErrors.BadRequest=BadRequest;var NotFound=function(_super){__extends(NotFound,_super);function NotFound(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=["The outbound call was made to an invalid phone number.","The TwiML application sid is missing a Voice URL."];_this.code=31404;_this.description="Not Found (HTTP/SIP)";_this.explanation="The server has not found anything matching the request.";_this.name="NotFound";_this.solutions=["Ensure the phone number dialed is valid.","Ensure the TwiML application is configured correctly with a Voice URL link."];Object.setPrototypeOf(_this,ClientErrors.NotFound.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return NotFound}(twilioError_1.default);ClientErrors.NotFound=NotFound;var TemporarilyUnavailable=function(_super){__extends(TemporarilyUnavailable,_super);function TemporarilyUnavailable(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31480;_this.description="Temporarily Unavailable (SIP)";_this.explanation="The callee is currently unavailable.";_this.name="TemporarilyUnavailable";_this.solutions=[];Object.setPrototypeOf(_this,ClientErrors.TemporarilyUnavailable.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return TemporarilyUnavailable}(twilioError_1.default);ClientErrors.TemporarilyUnavailable=TemporarilyUnavailable;var BusyHere=function(_super){__extends(BusyHere,_super);function BusyHere(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31486;_this.description="Busy Here (SIP)";_this.explanation="The callee is busy.";_this.name="BusyHere";_this.solutions=[];Object.setPrototypeOf(_this,ClientErrors.BusyHere.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return BusyHere}(twilioError_1.default);ClientErrors.BusyHere=BusyHere})(ClientErrors=exports.ClientErrors||(exports.ClientErrors={}));var SIPServerErrors;(function(SIPServerErrors){var Decline=function(_super){__extends(Decline,_super);function Decline(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31603;_this.description="Decline (SIP)";_this.explanation="The callee does not wish to participate in the call.";_this.name="Decline";_this.solutions=[];Object.setPrototypeOf(_this,SIPServerErrors.Decline.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return Decline}(twilioError_1.default);SIPServerErrors.Decline=Decline})(SIPServerErrors=exports.SIPServerErrors||(exports.SIPServerErrors={}));var GeneralErrors;(function(GeneralErrors){var UnknownError=function(_super){__extends(UnknownError,_super);function UnknownError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31e3;_this.description="Unknown Error";_this.explanation="An unknown error has occurred. See error details for more information.";_this.name="UnknownError";_this.solutions=[];Object.setPrototypeOf(_this,GeneralErrors.UnknownError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return UnknownError}(twilioError_1.default);GeneralErrors.UnknownError=UnknownError;var ApplicationNotFoundError=function(_super){__extends(ApplicationNotFoundError,_super);function ApplicationNotFoundError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31001;_this.description="Application Not Found";_this.explanation="";_this.name="ApplicationNotFoundError";_this.solutions=[];Object.setPrototypeOf(_this,GeneralErrors.ApplicationNotFoundError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return ApplicationNotFoundError}(twilioError_1.default);GeneralErrors.ApplicationNotFoundError=ApplicationNotFoundError;var ConnectionDeclinedError=function(_super){__extends(ConnectionDeclinedError,_super);function ConnectionDeclinedError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31002;_this.description="Connection Declined";_this.explanation="";_this.name="ConnectionDeclinedError";_this.solutions=[];Object.setPrototypeOf(_this,GeneralErrors.ConnectionDeclinedError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return ConnectionDeclinedError}(twilioError_1.default);GeneralErrors.ConnectionDeclinedError=ConnectionDeclinedError;var ConnectionTimeoutError=function(_super){__extends(ConnectionTimeoutError,_super);function ConnectionTimeoutError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31003;_this.description="Connection Timeout";_this.explanation="The server could not produce a response within a suitable amount of time.";_this.name="ConnectionTimeoutError";_this.solutions=[];Object.setPrototypeOf(_this,GeneralErrors.ConnectionTimeoutError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return ConnectionTimeoutError}(twilioError_1.default);GeneralErrors.ConnectionTimeoutError=ConnectionTimeoutError;var ConnectionError=function(_super){__extends(ConnectionError,_super);function ConnectionError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31005;_this.description="Connection error";_this.explanation="A connection error occurred during the call";_this.name="ConnectionError";_this.solutions=[];Object.setPrototypeOf(_this,GeneralErrors.ConnectionError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return ConnectionError}(twilioError_1.default);GeneralErrors.ConnectionError=ConnectionError;var CallCancelledError=function(_super){__extends(CallCancelledError,_super);function CallCancelledError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=["The incoming call was cancelled because it was not answered in time or it was accepted/rejected by another application instance registered with the same identity."];_this.code=31008;_this.description="Call cancelled";_this.explanation="Unable to answer because the call has ended";_this.name="CallCancelledError";_this.solutions=[];Object.setPrototypeOf(_this,GeneralErrors.CallCancelledError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return CallCancelledError}(twilioError_1.default);GeneralErrors.CallCancelledError=CallCancelledError;var TransportError=function(_super){__extends(TransportError,_super);function TransportError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31009;_this.description="Transport error";_this.explanation="No transport available to send or receive messages";_this.name="TransportError";_this.solutions=[];Object.setPrototypeOf(_this,GeneralErrors.TransportError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return TransportError}(twilioError_1.default);GeneralErrors.TransportError=TransportError})(GeneralErrors=exports.GeneralErrors||(exports.GeneralErrors={}));var MalformedRequestErrors;(function(MalformedRequestErrors){var MalformedRequestError=function(_super){__extends(MalformedRequestError,_super);function MalformedRequestError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=["Invalid content or MessageType passed to sendMessage method."];_this.code=31100;_this.description="The request had malformed syntax.";_this.explanation="The request could not be understood due to malformed syntax.";_this.name="MalformedRequestError";_this.solutions=["Ensure content and MessageType passed to sendMessage method are valid."];Object.setPrototypeOf(_this,MalformedRequestErrors.MalformedRequestError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return MalformedRequestError}(twilioError_1.default);MalformedRequestErrors.MalformedRequestError=MalformedRequestError;var MissingParameterArrayError=function(_super){__extends(MissingParameterArrayError,_super);function MissingParameterArrayError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31101;_this.description="Missing parameter array in request";_this.explanation="";_this.name="MissingParameterArrayError";_this.solutions=[];Object.setPrototypeOf(_this,MalformedRequestErrors.MissingParameterArrayError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return MissingParameterArrayError}(twilioError_1.default);MalformedRequestErrors.MissingParameterArrayError=MissingParameterArrayError;var AuthorizationTokenMissingError=function(_super){__extends(AuthorizationTokenMissingError,_super);function AuthorizationTokenMissingError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31102;_this.description="Authorization token missing in request.";_this.explanation="";_this.name="AuthorizationTokenMissingError";_this.solutions=[];Object.setPrototypeOf(_this,MalformedRequestErrors.AuthorizationTokenMissingError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return AuthorizationTokenMissingError}(twilioError_1.default);MalformedRequestErrors.AuthorizationTokenMissingError=AuthorizationTokenMissingError;var MaxParameterLengthExceededError=function(_super){__extends(MaxParameterLengthExceededError,_super);function MaxParameterLengthExceededError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31103;_this.description="Maximum parameter length has been exceeded.";_this.explanation="Length of parameters cannot exceed MAX_PARAM_LENGTH.";_this.name="MaxParameterLengthExceededError";_this.solutions=[];Object.setPrototypeOf(_this,MalformedRequestErrors.MaxParameterLengthExceededError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return MaxParameterLengthExceededError}(twilioError_1.default);MalformedRequestErrors.MaxParameterLengthExceededError=MaxParameterLengthExceededError;var InvalidBridgeTokenError=function(_super){__extends(InvalidBridgeTokenError,_super);function InvalidBridgeTokenError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31104;_this.description="Invalid bridge token";_this.explanation="";_this.name="InvalidBridgeTokenError";_this.solutions=[];Object.setPrototypeOf(_this,MalformedRequestErrors.InvalidBridgeTokenError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return InvalidBridgeTokenError}(twilioError_1.default);MalformedRequestErrors.InvalidBridgeTokenError=InvalidBridgeTokenError;var InvalidClientNameError=function(_super){__extends(InvalidClientNameError,_super);function InvalidClientNameError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=["Client name contains invalid characters."];_this.code=31105;_this.description="Invalid client name";_this.explanation="Client name should not contain control, space, delims, or unwise characters.";_this.name="InvalidClientNameError";_this.solutions=["Make sure that client name does not contain any of the invalid characters."];Object.setPrototypeOf(_this,MalformedRequestErrors.InvalidClientNameError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return InvalidClientNameError}(twilioError_1.default);MalformedRequestErrors.InvalidClientNameError=InvalidClientNameError;var ReconnectParameterInvalidError=function(_super){__extends(ReconnectParameterInvalidError,_super);function ReconnectParameterInvalidError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31107;_this.description="The reconnect parameter is invalid";_this.explanation="";_this.name="ReconnectParameterInvalidError";_this.solutions=[];Object.setPrototypeOf(_this,MalformedRequestErrors.ReconnectParameterInvalidError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return ReconnectParameterInvalidError}(twilioError_1.default);MalformedRequestErrors.ReconnectParameterInvalidError=ReconnectParameterInvalidError})(MalformedRequestErrors=exports.MalformedRequestErrors||(exports.MalformedRequestErrors={}));(function(AuthorizationErrors){var AuthorizationError=function(_super){__extends(AuthorizationError,_super);function AuthorizationError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31201;_this.description="Authorization error";_this.explanation="The request requires user authentication. The server understood the request, but is refusing to fulfill it.";_this.name="AuthorizationError";_this.solutions=[];Object.setPrototypeOf(_this,AuthorizationErrors.AuthorizationError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return AuthorizationError}(twilioError_1.default);AuthorizationErrors.AuthorizationError=AuthorizationError;var NoValidAccountError=function(_super){__extends(NoValidAccountError,_super);function NoValidAccountError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31203;_this.description="No valid account";_this.explanation="";_this.name="NoValidAccountError";_this.solutions=[];Object.setPrototypeOf(_this,AuthorizationErrors.NoValidAccountError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return NoValidAccountError}(twilioError_1.default);AuthorizationErrors.NoValidAccountError=NoValidAccountError;var InvalidJWTTokenError=function(_super){__extends(InvalidJWTTokenError,_super);function InvalidJWTTokenError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31204;_this.description="Invalid JWT token";_this.explanation="";_this.name="InvalidJWTTokenError";_this.solutions=[];Object.setPrototypeOf(_this,AuthorizationErrors.InvalidJWTTokenError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return InvalidJWTTokenError}(twilioError_1.default);AuthorizationErrors.InvalidJWTTokenError=InvalidJWTTokenError;var JWTTokenExpiredError=function(_super){__extends(JWTTokenExpiredError,_super);function JWTTokenExpiredError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31205;_this.description="JWT token expired";_this.explanation="";_this.name="JWTTokenExpiredError";_this.solutions=[];Object.setPrototypeOf(_this,AuthorizationErrors.JWTTokenExpiredError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return JWTTokenExpiredError}(twilioError_1.default);AuthorizationErrors.JWTTokenExpiredError=JWTTokenExpiredError;var RateExceededError=function(_super){__extends(RateExceededError,_super);function RateExceededError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=["Rate limit exceeded."];_this.code=31206;_this.description="Rate exceeded authorized limit.";_this.explanation="The request performed exceeds the authorized limit.";_this.name="RateExceededError";_this.solutions=["Ensure message send rate does not exceed authorized limits."];Object.setPrototypeOf(_this,AuthorizationErrors.RateExceededError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return RateExceededError}(twilioError_1.default);AuthorizationErrors.RateExceededError=RateExceededError;var JWTTokenExpirationTooLongError=function(_super){__extends(JWTTokenExpirationTooLongError,_super);function JWTTokenExpirationTooLongError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=31207;_this.description="JWT token expiration too long";_this.explanation="";_this.name="JWTTokenExpirationTooLongError";_this.solutions=[];Object.setPrototypeOf(_this,AuthorizationErrors.JWTTokenExpirationTooLongError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return JWTTokenExpirationTooLongError}(twilioError_1.default);AuthorizationErrors.JWTTokenExpirationTooLongError=JWTTokenExpirationTooLongError;var PayloadSizeExceededError=function(_super){__extends(PayloadSizeExceededError,_super);function PayloadSizeExceededError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=["The payload size of Call Message Event exceeds the authorized limit."];_this.code=31209;_this.description="Call Message Event Payload size exceeded authorized limit.";_this.explanation="The request performed to send a Call Message Event exceeds the payload size authorized limit";_this.name="PayloadSizeExceededError";_this.solutions=["Reduce payload size of Call Message Event to be within the authorized limit and try again."];Object.setPrototypeOf(_this,AuthorizationErrors.PayloadSizeExceededError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return PayloadSizeExceededError}(twilioError_1.default);AuthorizationErrors.PayloadSizeExceededError=PayloadSizeExceededError})(AuthorizationErrors=exports.AuthorizationErrors||(exports.AuthorizationErrors={}));var UserMediaErrors;(function(UserMediaErrors){var PermissionDeniedError=function(_super){__extends(PermissionDeniedError,_super);function PermissionDeniedError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=["The user denied the getUserMedia request.","The browser denied the getUserMedia request."];_this.code=31401;_this.description="UserMedia Permission Denied Error";_this.explanation="The browser or end-user denied permissions to user media. Therefore we were unable to acquire input audio.";_this.name="PermissionDeniedError";_this.solutions=["The user should accept the request next time prompted. If the browser saved the deny, the user should change that permission in their browser.","The user should to verify that the browser has permission to access the microphone at this address."];Object.setPrototypeOf(_this,UserMediaErrors.PermissionDeniedError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return PermissionDeniedError}(twilioError_1.default);UserMediaErrors.PermissionDeniedError=PermissionDeniedError;var AcquisitionFailedError=function(_super){__extends(AcquisitionFailedError,_super);function AcquisitionFailedError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=["NotFoundError - The deviceID specified was not found.","The getUserMedia constraints were overconstrained and no devices matched."];_this.code=31402;_this.description="UserMedia Acquisition Failed Error";_this.explanation="The browser and end-user allowed permissions, however getting the media failed. Usually this is due to bad constraints, but can sometimes fail due to browser, OS or hardware issues.";_this.name="AcquisitionFailedError";_this.solutions=["Ensure the deviceID being specified exists.","Try acquiring media with fewer constraints."];Object.setPrototypeOf(_this,UserMediaErrors.AcquisitionFailedError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return AcquisitionFailedError}(twilioError_1.default);UserMediaErrors.AcquisitionFailedError=AcquisitionFailedError})(UserMediaErrors=exports.UserMediaErrors||(exports.UserMediaErrors={}));var SignalingErrors;(function(SignalingErrors){var ConnectionError=function(_super){__extends(ConnectionError,_super);function ConnectionError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=[];_this.code=53e3;_this.description="Signaling connection error";_this.explanation="Raised whenever a signaling connection error occurs that is not covered by a more specific error code.";_this.name="ConnectionError";_this.solutions=[];Object.setPrototypeOf(_this,SignalingErrors.ConnectionError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return ConnectionError}(twilioError_1.default);SignalingErrors.ConnectionError=ConnectionError;var ConnectionDisconnected=function(_super){__extends(ConnectionDisconnected,_super);function ConnectionDisconnected(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=["The device running your application lost its Internet connection."];_this.code=53001;_this.description="Signaling connection disconnected";_this.explanation="Raised whenever the signaling connection is unexpectedly disconnected.";_this.name="ConnectionDisconnected";_this.solutions=["Ensure the device running your application has access to a stable Internet connection."];Object.setPrototypeOf(_this,SignalingErrors.ConnectionDisconnected.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return ConnectionDisconnected}(twilioError_1.default);SignalingErrors.ConnectionDisconnected=ConnectionDisconnected})(SignalingErrors=exports.SignalingErrors||(exports.SignalingErrors={}));var MediaErrors;(function(MediaErrors){var ClientLocalDescFailed=function(_super){__extends(ClientLocalDescFailed,_super);function ClientLocalDescFailed(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=["The Client may not be using a supported WebRTC implementation.","The Client may not have the necessary resources to create or apply a new media description."];_this.code=53400;_this.description="Client is unable to create or apply a local media description";_this.explanation="Raised whenever a Client is unable to create or apply a local media description.";_this.name="ClientLocalDescFailed";_this.solutions=["If you are experiencing this error using the JavaScript SDK, ensure you are running it with a supported WebRTC implementation."];Object.setPrototypeOf(_this,MediaErrors.ClientLocalDescFailed.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return ClientLocalDescFailed}(twilioError_1.default);MediaErrors.ClientLocalDescFailed=ClientLocalDescFailed;var ClientRemoteDescFailed=function(_super){__extends(ClientRemoteDescFailed,_super);function ClientRemoteDescFailed(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=["The Client may not be using a supported WebRTC implementation.","The Client may be connecting peer-to-peer with another Participant that is not using a supported WebRTC implementation.","The Client may not have the necessary resources to apply a new media description."];_this.code=53402;_this.description="Client is unable to apply a remote media description";_this.explanation="Raised whenever the Client receives a remote media description but is unable to apply it.";_this.name="ClientRemoteDescFailed";_this.solutions=["If you are experiencing this error using the JavaScript SDK, ensure you are running it with a supported WebRTC implementation."];Object.setPrototypeOf(_this,MediaErrors.ClientRemoteDescFailed.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return ClientRemoteDescFailed}(twilioError_1.default);MediaErrors.ClientRemoteDescFailed=ClientRemoteDescFailed;var ConnectionError=function(_super){__extends(ConnectionError,_super);function ConnectionError(messageOrError,error){var _this=_super.call(this,messageOrError,error)||this;_this.causes=["The Client was unable to establish a media connection.","A media connection which was active failed liveliness checks."];_this.code=53405;_this.description="Media connection failed";_this.explanation="Raised by the Client or Server whenever a media connection fails.";_this.name="ConnectionError";_this.solutions=["If the problem persists, try connecting to another region.","Check your Client's network connectivity.","If you've provided custom ICE Servers then ensure that the URLs and credentials are valid."];Object.setPrototypeOf(_this,MediaErrors.ConnectionError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return ConnectionError}(twilioError_1.default);MediaErrors.ConnectionError=ConnectionError})(MediaErrors=exports.MediaErrors||(exports.MediaErrors={}));exports.errorsByCode=new Map([[20101,AuthorizationErrors.AccessTokenInvalid],[20104,AuthorizationErrors.AccessTokenExpired],[20151,AuthorizationErrors.AuthenticationFailed],[31202,SignatureValidationErrors.AccessTokenSignatureValidationFailed],[31400,ClientErrors.BadRequest],[31404,ClientErrors.NotFound],[31480,ClientErrors.TemporarilyUnavailable],[31486,ClientErrors.BusyHere],[31603,SIPServerErrors.Decline],[31e3,GeneralErrors.UnknownError],[31001,GeneralErrors.ApplicationNotFoundError],[31002,GeneralErrors.ConnectionDeclinedError],[31003,GeneralErrors.ConnectionTimeoutError],[31005,GeneralErrors.ConnectionError],[31008,GeneralErrors.CallCancelledError],[31009,GeneralErrors.TransportError],[31100,MalformedRequestErrors.MalformedRequestError],[31101,MalformedRequestErrors.MissingParameterArrayError],[31102,MalformedRequestErrors.AuthorizationTokenMissingError],[31103,MalformedRequestErrors.MaxParameterLengthExceededError],[31104,MalformedRequestErrors.InvalidBridgeTokenError],[31105,MalformedRequestErrors.InvalidClientNameError],[31107,MalformedRequestErrors.ReconnectParameterInvalidError],[31201,AuthorizationErrors.AuthorizationError],[31203,AuthorizationErrors.NoValidAccountError],[31204,AuthorizationErrors.InvalidJWTTokenError],[31205,AuthorizationErrors.JWTTokenExpiredError],[31206,AuthorizationErrors.RateExceededError],[31207,AuthorizationErrors.JWTTokenExpirationTooLongError],[31209,AuthorizationErrors.PayloadSizeExceededError],[31401,UserMediaErrors.PermissionDeniedError],[31402,UserMediaErrors.AcquisitionFailedError],[53e3,SignalingErrors.ConnectionError],[53001,SignalingErrors.ConnectionDisconnected],[53400,MediaErrors.ClientLocalDescFailed],[53402,MediaErrors.ClientRemoteDescFailed],[53405,MediaErrors.ConnectionError]]);Object.freeze(exports.errorsByCode)},{"./twilioError":16}],15:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();Object.defineProperty(exports,"__esModule",{value:true});exports.UserMediaErrors=exports.TwilioError=exports.SIPServerErrors=exports.SignatureValidationErrors=exports.SignalingErrors=exports.MediaErrors=exports.MalformedRequestErrors=exports.GeneralErrors=exports.ClientErrors=exports.AuthorizationErrors=exports.hasErrorByCode=exports.getErrorByCode=exports.NotSupportedError=exports.InvalidStateError=exports.InvalidArgumentError=exports.getPreciseSignalingErrorByCode=void 0;var generated_1=require("./generated");Object.defineProperty(exports,"AuthorizationErrors",{enumerable:true,get:function(){return generated_1.AuthorizationErrors}});Object.defineProperty(exports,"ClientErrors",{enumerable:true,get:function(){return generated_1.ClientErrors}});Object.defineProperty(exports,"GeneralErrors",{enumerable:true,get:function(){return generated_1.GeneralErrors}});Object.defineProperty(exports,"MalformedRequestErrors",{enumerable:true,get:function(){return generated_1.MalformedRequestErrors}});Object.defineProperty(exports,"MediaErrors",{enumerable:true,get:function(){return generated_1.MediaErrors}});Object.defineProperty(exports,"SignalingErrors",{enumerable:true,get:function(){return generated_1.SignalingErrors}});Object.defineProperty(exports,"SignatureValidationErrors",{enumerable:true,get:function(){return generated_1.SignatureValidationErrors}});Object.defineProperty(exports,"SIPServerErrors",{enumerable:true,get:function(){return generated_1.SIPServerErrors}});Object.defineProperty(exports,"TwilioError",{enumerable:true,get:function(){return generated_1.TwilioError}});Object.defineProperty(exports,"UserMediaErrors",{enumerable:true,get:function(){return generated_1.UserMediaErrors}});var PRECISE_SIGNALING_ERROR_CODES=new Set([31001,31002,31003,31101,31102,31103,31104,31105,31107,31201,31202,31203,31204,31205,31207,31404,31480,31486,31603]);function getPreciseSignalingErrorByCode(enableImprovedSignalingErrorPrecision,errorCode){if(typeof errorCode!=="number"){return}if(!hasErrorByCode(errorCode)){return}var shouldTransform=enableImprovedSignalingErrorPrecision?true:!PRECISE_SIGNALING_ERROR_CODES.has(errorCode);if(!shouldTransform){return}return getErrorByCode(errorCode)}exports.getPreciseSignalingErrorByCode=getPreciseSignalingErrorByCode;var InvalidArgumentError=function(_super){__extends(InvalidArgumentError,_super);function InvalidArgumentError(message){var _this=_super.call(this,message)||this;_this.name="InvalidArgumentError";return _this}return InvalidArgumentError}(Error);exports.InvalidArgumentError=InvalidArgumentError;var InvalidStateError=function(_super){__extends(InvalidStateError,_super);function InvalidStateError(message){var _this=_super.call(this,message)||this;_this.name="InvalidStateError";return _this}return InvalidStateError}(Error);exports.InvalidStateError=InvalidStateError;var NotSupportedError=function(_super){__extends(NotSupportedError,_super);function NotSupportedError(message){var _this=_super.call(this,message)||this;_this.name="NotSupportedError";return _this}return NotSupportedError}(Error);exports.NotSupportedError=NotSupportedError;function getErrorByCode(code){var error=generated_1.errorsByCode.get(code);if(!error){throw new InvalidArgumentError("Error code "+code+" not found")}return error}exports.getErrorByCode=getErrorByCode;function hasErrorByCode(code){return generated_1.errorsByCode.has(code)}exports.hasErrorByCode=hasErrorByCode},{"./generated":14}],16:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();Object.defineProperty(exports,"__esModule",{value:true});var TwilioError=function(_super){__extends(TwilioError,_super);function TwilioError(messageOrError,error){var _this=_super.call(this)||this;Object.setPrototypeOf(_this,TwilioError.prototype);var message=typeof messageOrError==="string"?messageOrError:_this.explanation;var originalError=typeof messageOrError==="object"?messageOrError:error;_this.message=_this.name+" ("+_this.code+"): "+message;_this.originalError=originalError;return _this}return TwilioError}(Error);exports.default=TwilioError},{}],17:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();Object.defineProperty(exports,"__esModule",{value:true});var events_1=require("events");var log_1=require("./log");var request_1=require("./request");var EventPublisher=function(_super){__extends(EventPublisher,_super);function EventPublisher(productName,token,options){var _this=_super.call(this)||this;if(!(_this instanceof EventPublisher)){return new EventPublisher(productName,token,options)}options=Object.assign({defaultPayload:function(){return{}}},options);var defaultPayload=options.defaultPayload;if(typeof defaultPayload!=="function"){defaultPayload=function(){return Object.assign({},options.defaultPayload)}}var isEnabled=true;var metadata=Object.assign({app_name:undefined,app_version:undefined},options.metadata);Object.defineProperties(_this,{_defaultPayload:{value:defaultPayload},_host:{value:options.host,writable:true},_isEnabled:{get:function(){return isEnabled},set:function(_isEnabled){isEnabled=_isEnabled}},_log:{value:new log_1.default("EventPublisher")},_request:{value:options.request||request_1.default,writable:true},_token:{value:token,writable:true},isEnabled:{enumerable:true,get:function(){return isEnabled}},metadata:{enumerable:true,get:function(){return metadata}},productName:{enumerable:true,value:productName},token:{enumerable:true,get:function(){return this._token}}});return _this}return EventPublisher}(events_1.EventEmitter);EventPublisher.prototype._post=function _post(endpointName,level,group,name,payload,connection,force){var _this=this;if(!this.isEnabled&&!force||!this._host){this._log.debug("Publishing cancelled",JSON.stringify({isEnabled:this.isEnabled,force:force,host:this._host}));return Promise.resolve()}if(!connection||(!connection.parameters||!connection.parameters.CallSid)&&!connection.outboundConnectionId){if(!connection){this._log.debug("Publishing cancelled. Missing connection object")}else{this._log.debug("Publishing cancelled. Missing connection info",JSON.stringify({outboundConnectionId:connection.outboundConnectionId,parameters:connection.parameters}))}return Promise.resolve()}var event={group:group,level:level.toUpperCase(),name:name,payload:payload&&payload.forEach?payload.slice(0):Object.assign(this._defaultPayload(connection),payload),payload_type:"application/json",private:false,publisher:this.productName,timestamp:(new Date).toISOString()};if(this.metadata){event.publisher_metadata=this.metadata}if(endpointName==="EndpointEvents"){this._log.debug("Publishing insights",JSON.stringify({endpointName:endpointName,event:event,force:force,host:this._host}))}var requestParams={body:event,headers:{"Content-Type":"application/json","X-Twilio-Token":this.token},url:"https://"+this._host+"/v4/"+endpointName};return new Promise(function(resolve,reject){_this._request.post(requestParams,function(err){if(err){_this.emit("error",err);reject(err)}else{resolve()}})}).catch(function(e){_this._log.error("Unable to post "+group+" "+name+" event to Insights. Received error: "+e)})};EventPublisher.prototype.post=function post(level,group,name,payload,connection,force){return this._post("EndpointEvents",level,group,name,payload,connection,force)};EventPublisher.prototype.debug=function debug(group,name,payload,connection){return this.post("debug",group,name,payload,connection)};EventPublisher.prototype.info=function info(group,name,payload,connection){return this.post("info",group,name,payload,connection)};EventPublisher.prototype.warn=function warn(group,name,payload,connection){return this.post("warning",group,name,payload,connection)};EventPublisher.prototype.error=function error(group,name,payload,connection){return this.post("error",group,name,payload,connection)};EventPublisher.prototype.postMetrics=function postMetrics(group,name,metrics,customFields,connection){var _this=this;return new Promise(function(resolve){var samples=metrics.map(formatMetric).map(function(sample){return Object.assign(sample,customFields)});resolve(_this._post("EndpointMetrics","info",group,name,samples,connection))})};EventPublisher.prototype.setHost=function setHost(host){this._host=host};EventPublisher.prototype.setToken=function setToken(token){this._token=token};EventPublisher.prototype.enable=function enable(){this._isEnabled=true};EventPublisher.prototype.disable=function disable(){this._isEnabled=false};function formatMetric(sample){return{audio_codec:sample.codecName,audio_level_in:sample.audioInputLevel,audio_level_out:sample.audioOutputLevel,bytes_received:sample.bytesReceived,bytes_sent:sample.bytesSent,call_volume_input:sample.inputVolume,call_volume_output:sample.outputVolume,jitter:sample.jitter,mos:sample.mos&&Math.round(sample.mos*100)/100,packets_lost:sample.packetsLost,packets_lost_fraction:sample.packetsLostFraction&&Math.round(sample.packetsLostFraction*100)/100,packets_received:sample.packetsReceived,rtt:sample.rtt,timestamp:new Date(sample.timestamp).toISOString(),total_bytes_received:sample.totals.bytesReceived,total_bytes_sent:sample.totals.bytesSent,total_packets_lost:sample.totals.packetsLost,total_packets_received:sample.totals.packetsReceived,total_packets_sent:sample.totals.packetsSent}}exports.default=EventPublisher},{"./log":18,"./request":23,events:39}],18:[function(require,module,exports){"use strict";var __spreadArrays=this&&this.__spreadArrays||function(){for(var s=0,i=0,il=arguments.length;i<il;i++)s+=arguments[i].length;for(var r=Array(s),k=0,i=0;i<il;i++)for(var a=arguments[i],j=0,jl=a.length;j<jl;j++,k++)r[k]=a[j];return r};Object.defineProperty(exports,"__esModule",{value:true});exports.Logger=void 0;var LogLevelModule=require("loglevel");var constants_1=require("./constants");var Log=function(){function Log(tag,options){this._log=Log.getLogLevelInstance(options);this._prefix="[TwilioVoice]["+tag+"]"}Log.getLogLevelInstance=function(options){if(!Log.loglevelInstance){try{Log.loglevelInstance=(options&&options.LogLevelModule?options.LogLevelModule:LogLevelModule).getLogger(constants_1.PACKAGE_NAME)}catch(_a){console.warn("Cannot create custom logger");Log.loglevelInstance=console}}return Log.loglevelInstance};Log.prototype.debug=function(){var _a;var args=[];for(var _i=0;_i<arguments.length;_i++){args[_i]=arguments[_i]}(_a=this._log).debug.apply(_a,__spreadArrays([this._prefix],args))};Log.prototype.error=function(){var _a;var args=[];for(var _i=0;_i<arguments.length;_i++){args[_i]=arguments[_i]}(_a=this._log).error.apply(_a,__spreadArrays([this._prefix],args))};Log.prototype.info=function(){var _a;var args=[];for(var _i=0;_i<arguments.length;_i++){args[_i]=arguments[_i]}(_a=this._log).info.apply(_a,__spreadArrays([this._prefix],args))};Log.prototype.setDefaultLevel=function(level){if(this._log.setDefaultLevel){this._log.setDefaultLevel(level)}else{console.warn("Logger cannot setDefaultLevel")}};Log.prototype.warn=function(){var _a;var args=[];for(var _i=0;_i<arguments.length;_i++){args[_i]=arguments[_i]}(_a=this._log).warn.apply(_a,__spreadArrays([this._prefix],args))};Log.levels=LogLevelModule.levels;return Log}();exports.Logger=Log.getLogLevelInstance();exports.default=Log},{"./constants":10,loglevel:43}],19:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var constants_1=require("./constants");var errors_1=require("./errors");var log_1=require("./log");var DEFAULT_TEST_SOUND_URL=constants_1.SOUNDS_BASE_URL+"/outgoing.mp3";var OutputDeviceCollection=function(){function OutputDeviceCollection(_name,_availableDevices,_beforeChange,_isSupported){this._name=_name;this._availableDevices=_availableDevices;this._beforeChange=_beforeChange;this._isSupported=_isSupported;this._activeDevices=new Set;this._log=new log_1.default("OutputDeviceCollection")}OutputDeviceCollection.prototype.delete=function(device){this._log.debug(".delete",device);var wasDeleted=!!this._activeDevices.delete(device);var defaultDevice=this._availableDevices.get("default")||Array.from(this._availableDevices.values())[0];if(!this._activeDevices.size&&defaultDevice){this._activeDevices.add(defaultDevice)}var deviceIds=Array.from(this._activeDevices.values()).map(function(deviceInfo){return deviceInfo.deviceId});this._beforeChange(this._name,deviceIds);return!!wasDeleted};OutputDeviceCollection.prototype.get=function(){return this._activeDevices};OutputDeviceCollection.prototype.set=function(deviceIdOrIds){var _this=this;this._log.debug(".set",deviceIdOrIds);if(!this._isSupported){return Promise.reject(new errors_1.NotSupportedError("This browser does not support audio output selection"))}var deviceIds=Array.isArray(deviceIdOrIds)?deviceIdOrIds:[deviceIdOrIds];if(!deviceIds.length){return Promise.reject(new errors_1.InvalidArgumentError("Must specify at least one device to set"))}var missingIds=[];var devices=deviceIds.map(function(id){var device=_this._availableDevices.get(id);if(!device){missingIds.push(id)}return device});if(missingIds.length){return Promise.reject(new errors_1.InvalidArgumentError("Devices not found: "+missingIds.join(", ")))}return new Promise(function(resolve){resolve(_this._beforeChange(_this._name,deviceIds))}).then(function(){_this._activeDevices.clear();devices.forEach(_this._activeDevices.add,_this._activeDevices)})};OutputDeviceCollection.prototype.test=function(soundUrl){if(soundUrl===void 0){soundUrl=DEFAULT_TEST_SOUND_URL}if(!this._isSupported){return Promise.reject(new errors_1.NotSupportedError("This browser does not support audio output selection"))}if(!this._activeDevices.size){return Promise.reject(new errors_1.InvalidStateError("No active output devices to test"))}return Promise.all(Array.from(this._activeDevices).map(function(device){var el;return new Promise(function(resolve){el=new Audio(soundUrl);el.oncanplay=resolve}).then(function(){return el.setSinkId(device.deviceId).then(function(){return el.play()})})}))};return OutputDeviceCollection}();exports.default=OutputDeviceCollection},{"./constants":10,"./errors":15,"./log":18}],20:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();var __assign=this&&this.__assign||function(){__assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)if(Object.prototype.hasOwnProperty.call(s,p))t[p]=s[p]}return t};return __assign.apply(this,arguments)};var __awaiter=this&&this.__awaiter||function(thisArg,_arguments,P,generator){function adopt(value){return value instanceof P?value:new P(function(resolve){resolve(value)})}return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value))}catch(e){reject(e)}}function rejected(value){try{step(generator["throw"](value))}catch(e){reject(e)}}function step(result){result.done?resolve(result.value):adopt(result.value).then(fulfilled,rejected)}step((generator=generator.apply(thisArg,_arguments||[])).next())})};var __generator=this&&this.__generator||function(thisArg,body){var _={label:0,sent:function(){if(t[0]&1)throw t[1];return t[1]},trys:[],ops:[]},f,y,t,g;return g={next:verb(0),throw:verb(1),return:verb(2)},typeof Symbol==="function"&&(g[Symbol.iterator]=function(){return this}),g;function verb(n){return function(v){return step([n,v])}}function step(op){if(f)throw new TypeError("Generator is already executing.");while(_)try{if(f=1,y&&(t=op[0]&2?y["return"]:op[0]?y["throw"]||((t=y["return"])&&t.call(y),0):y.next)&&!(t=t.call(y,op[1])).done)return t;if(y=0,t)op=[op[0]&2,t.value];switch(op[0]){case 0:case 1:t=op;break;case 4:_.label++;return{value:op[1],done:false};case 5:_.label++;y=op[1];op=[0];continue;case 7:op=_.ops.pop();_.trys.pop();continue;default:if(!(t=_.trys,t=t.length>0&&t[t.length-1])&&(op[0]===6||op[0]===2)){_=0;continue}if(op[0]===3&&(!t||op[1]>t[0]&&op[1]<t[3])){_.label=op[1];break}if(op[0]===6&&_.label<t[1]){_.label=t[1];t=op;break}if(t&&_.label<t[2]){_.label=t[2];_.ops.push(op);break}if(t[2])_.ops.pop();_.trys.pop();continue}op=body.call(thisArg,_)}catch(e){op=[6,e];y=0}finally{f=t=0}if(op[0]&5)throw op[1];return{value:op[0]?op[1]:void 0,done:true}}};Object.defineProperty(exports,"__esModule",{value:true});exports.PreflightTest=void 0;var events_1=require("events");var call_1=require("../call");var device_1=require("../device");var errors_1=require("../errors");var log_1=require("../log");var stats_1=require("../rtc/stats");var constants_1=require("../constants");var PreflightTest=function(_super){__extends(PreflightTest,_super);function PreflightTest(token,options){var _this=_super.call(this)||this;_this._hasInsightsErrored=false;_this._log=new log_1.default("PreflightTest");_this._networkTiming={};_this._options={codecPreferences:[call_1.default.Codec.PCMU,call_1.default.Codec.Opus],edge:"roaming",fakeMicInput:false,logLevel:"error",signalingTimeoutMs:1e4};_this._status=PreflightTest.Status.Connecting;Object.assign(_this._options,options);_this._samples=[];_this._warnings=[];_this._startTime=Date.now();_this._initDevice(token,__assign(__assign({},_this._options),{fileInputStream:_this._options.fakeMicInput?_this._getStreamFromFile():undefined}));var userOptions=["codecPreferences","edge","fakeMicInput","logLevel","signalingTimeoutMs"];var userOptionOverrides=["audioContext","deviceFactory","fileInputStream","getRTCIceCandidateStatsReport","iceServers","rtcConfiguration"];if(typeof options==="object"){var toLog_1=__assign({},options);Object.keys(toLog_1).forEach(function(key){if(!userOptions.includes(key)&&!userOptionOverrides.includes(key)){delete toLog_1[key]}if(userOptionOverrides.includes(key)){toLog_1[key]=true}});_this._log.debug(".constructor",JSON.stringify(toLog_1))}return _this}PreflightTest.prototype.stop=function(){var _this=this;this._log.debug(".stop");var error=new errors_1.GeneralErrors.CallCancelledError;if(this._device){this._device.once(device_1.default.EventName.Unregistered,function(){return _this._onFailed(error)});this._device.destroy()}else{this._onFailed(error)}};PreflightTest.prototype._emitWarning=function(name,description,rtcWarning){var warning={name:name,description:description};if(rtcWarning){warning.rtcWarning=rtcWarning}this._warnings.push(warning);this._log.debug("#"+PreflightTest.Events.Warning,JSON.stringify(warning));this.emit(PreflightTest.Events.Warning,warning)};PreflightTest.prototype._getCallQuality=function(mos){if(mos>4.2){return PreflightTest.CallQuality.Excellent}else if(mos>=4.1&&mos<=4.2){return PreflightTest.CallQuality.Great}else if(mos>=3.7&&mos<=4){return PreflightTest.CallQuality.Good}else if(mos>=3.1&&mos<=3.6){return PreflightTest.CallQuality.Fair}else{return PreflightTest.CallQuality.Degraded}};PreflightTest.prototype._getReport=function(){var stats=this._getRTCStats();var testTiming={start:this._startTime};if(this._endTime){testTiming.end=this._endTime;testTiming.duration=this._endTime-this._startTime}var report={callSid:this._callSid,edge:this._edge,iceCandidateStats:this._rtcIceCandidateStatsReport.iceCandidateStats,networkTiming:this._networkTiming,samples:this._samples,selectedEdge:this._options.edge,stats:stats,testTiming:testTiming,totals:this._getRTCSampleTotals(),warnings:this._warnings};var selectedIceCandidatePairStats=this._rtcIceCandidateStatsReport.selectedIceCandidatePairStats;if(selectedIceCandidatePairStats){report.selectedIceCandidatePairStats=selectedIceCandidatePairStats;report.isTurnRequired=selectedIceCandidatePairStats.localCandidate.candidateType==="relay"||selectedIceCandidatePairStats.remoteCandidate.candidateType==="relay"}if(stats){report.callQuality=this._getCallQuality(stats.mos.average)}return report};PreflightTest.prototype._getRTCSampleTotals=function(){if(!this._latestSample){return}return __assign({},this._latestSample.totals)};PreflightTest.prototype._getRTCStats=function(){var firstMosSampleIdx=this._samples.findIndex(function(sample){return typeof sample.mos==="number"&&sample.mos>0});var samples=firstMosSampleIdx>=0?this._samples.slice(firstMosSampleIdx):[];if(!samples||!samples.length){return}return["jitter","mos","rtt"].reduce(function(statObj,stat){var _a;var values=samples.map(function(s){return s[stat]});return __assign(__assign({},statObj),(_a={},_a[stat]={average:Number((values.reduce(function(total,value){return total+value})/values.length).toPrecision(5)),max:Math.max.apply(Math,values),min:Math.min.apply(Math,values)},_a))},{})};PreflightTest.prototype._getStreamFromFile=function(){var audioContext=this._options.audioContext;if(!audioContext){throw new errors_1.NotSupportedError("Cannot fake input audio stream: AudioContext is not supported by this browser.")}var audioEl=new Audio(constants_1.COWBELL_AUDIO_URL);audioEl.addEventListener("canplaythrough",function(){return audioEl.play()});if(typeof audioEl.setAttribute==="function"){audioEl.setAttribute("crossorigin","anonymous")}var src=audioContext.createMediaElementSource(audioEl);var dest=audioContext.createMediaStreamDestination();src.connect(dest);return dest.stream};PreflightTest.prototype._initDevice=function(token,options){var _this=this;try{this._device=new(options.deviceFactory||device_1.default)(token,{codecPreferences:options.codecPreferences,edge:options.edge,fileInputStream:options.fileInputStream,logLevel:options.logLevel,preflight:true});this._device.once(device_1.default.EventName.Registered,function(){_this._onDeviceRegistered()});this._device.once(device_1.default.EventName.Error,function(error){_this._onDeviceError(error)});this._device.register()}catch(error){setTimeout(function(){_this._onFailed(error)});return}this._signalingTimeoutTimer=setTimeout(function(){_this._onDeviceError(new errors_1.SignalingErrors.ConnectionError("WebSocket Connection Timeout"))},options.signalingTimeoutMs)};PreflightTest.prototype._onDeviceError=function(error){this._device.destroy();this._onFailed(error)};PreflightTest.prototype._onDeviceRegistered=function(){return __awaiter(this,void 0,void 0,function(){var _a,audio,publisher;var _this=this;return __generator(this,function(_b){switch(_b.label){case 0:clearTimeout(this._echoTimer);clearTimeout(this._signalingTimeoutTimer);_a=this;return[4,this._device.connect({rtcConfiguration:this._options.rtcConfiguration})];case 1:_a._call=_b.sent();this._networkTiming.signaling={start:Date.now()};this._setupCallHandlers(this._call);this._edge=this._device.edge||undefined;if(this._options.fakeMicInput){this._echoTimer=setTimeout(function(){return _this._device.disconnectAll()},constants_1.ECHO_TEST_DURATION);audio=this._device.audio;if(audio){audio.disconnect(false);audio.outgoing(false)}}this._call.once("disconnect",function(){_this._device.once(device_1.default.EventName.Unregistered,function(){return _this._onUnregistered()});_this._device.destroy()});publisher=this._call["_publisher"];publisher.on("error",function(){if(!_this._hasInsightsErrored){_this._emitWarning("insights-connection-error","Received an error when attempting to connect to Insights gateway")}_this._hasInsightsErrored=true});return[2]}})})};PreflightTest.prototype._onFailed=function(error){clearTimeout(this._echoTimer);clearTimeout(this._signalingTimeoutTimer);this._releaseHandlers();this._endTime=Date.now();this._status=PreflightTest.Status.Failed;this._log.debug("#"+PreflightTest.Events.Failed,error);this.emit(PreflightTest.Events.Failed,error)};PreflightTest.prototype._onUnregistered=function(){var _this=this;setTimeout(function(){if(_this._status===PreflightTest.Status.Failed){return}clearTimeout(_this._echoTimer);clearTimeout(_this._signalingTimeoutTimer);_this._releaseHandlers();_this._endTime=Date.now();_this._status=PreflightTest.Status.Completed;_this._report=_this._getReport();_this._log.debug("#"+PreflightTest.Events.Completed,JSON.stringify(_this._report));_this.emit(PreflightTest.Events.Completed,_this._report)},10)};PreflightTest.prototype._releaseHandlers=function(){[this._device,this._call].forEach(function(emitter){if(emitter){emitter.eventNames().forEach(function(name){return emitter.removeAllListeners(name)})}})};PreflightTest.prototype._setupCallHandlers=function(call){var _this=this;if(this._options.fakeMicInput){call.once("volume",function(){call["_mediaHandler"].outputs.forEach(function(output){return output.audio.muted=true})})}call.on("warning",function(name,data){_this._emitWarning(name,"Received an RTCWarning. See .rtcWarning for the RTCWarning",data)});call.once("accept",function(){_this._callSid=call["_mediaHandler"].callSid;_this._status=PreflightTest.Status.Connected;_this._log.debug("#"+PreflightTest.Events.Connected);_this.emit(PreflightTest.Events.Connected)});call.on("sample",function(sample){return __awaiter(_this,void 0,void 0,function(){var _a;return __generator(this,function(_b){switch(_b.label){case 0:if(!!this._latestSample)return[3,2];_a=this;return[4,(this._options.getRTCIceCandidateStatsReport||stats_1.getRTCIceCandidateStatsReport)(call["_mediaHandler"].version.pc)];case 1:_a._rtcIceCandidateStatsReport=_b.sent();_b.label=2;case 2:this._latestSample=sample;this._samples.push(sample);this._log.debug("#"+PreflightTest.Events.Sample,JSON.stringify(sample));this.emit(PreflightTest.Events.Sample,sample);return[2]}})})});[{reportLabel:"peerConnection",type:"pcconnection"},{reportLabel:"ice",type:"iceconnection"},{reportLabel:"dtls",type:"dtlstransport"},{reportLabel:"signaling",type:"signaling"}].forEach(function(_a){var type=_a.type,reportLabel=_a.reportLabel;var handlerName="on"+type+"statechange";var originalHandler=call["_mediaHandler"][handlerName];call["_mediaHandler"][handlerName]=function(state){var timing=_this._networkTiming[reportLabel]=_this._networkTiming[reportLabel]||{start:0};if(state==="connecting"||state==="checking"){timing.start=Date.now()}else if((state==="connected"||state==="stable")&&!timing.duration){timing.end=Date.now();timing.duration=timing.end-timing.start}originalHandler(state)}})};Object.defineProperty(PreflightTest.prototype,"callSid",{get:function(){return this._callSid},enumerable:false,configurable:true});Object.defineProperty(PreflightTest.prototype,"endTime",{get:function(){return this._endTime},enumerable:false,configurable:true});Object.defineProperty(PreflightTest.prototype,"latestSample",{get:function(){return this._latestSample},enumerable:false,configurable:true});Object.defineProperty(PreflightTest.prototype,"report",{get:function(){return this._report},enumerable:false,configurable:true});Object.defineProperty(PreflightTest.prototype,"startTime",{get:function(){return this._startTime},enumerable:false,configurable:true});Object.defineProperty(PreflightTest.prototype,"status",{get:function(){return this._status},enumerable:false,configurable:true});return PreflightTest}(events_1.EventEmitter);exports.PreflightTest=PreflightTest;(function(PreflightTest){var CallQuality;(function(CallQuality){CallQuality["Excellent"]="excellent";CallQuality["Great"]="great";CallQuality["Good"]="good";CallQuality["Fair"]="fair";CallQuality["Degraded"]="degraded"})(CallQuality=PreflightTest.CallQuality||(PreflightTest.CallQuality={}));var Events;(function(Events){Events["Completed"]="completed";Events["Connected"]="connected";Events["Failed"]="failed";Events["Sample"]="sample";Events["Warning"]="warning"})(Events=PreflightTest.Events||(PreflightTest.Events={}));var Status;(function(Status){Status["Connecting"]="connecting";Status["Connected"]="connected";Status["Completed"]="completed";Status["Failed"]="failed"})(Status=PreflightTest.Status||(PreflightTest.Status={}))})(PreflightTest=exports.PreflightTest||(exports.PreflightTest={}));exports.PreflightTest=PreflightTest},{"../call":9,"../constants":10,"../device":12,"../errors":15,"../log":18,"../rtc/stats":32,events:39}],21:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();Object.defineProperty(exports,"__esModule",{value:true});var events_1=require("events");var C=require("./constants");var errors_1=require("./errors");var log_1=require("./log");var wstransport_1=require("./wstransport");var PSTREAM_VERSION="1.6";var PStream=function(_super){__extends(PStream,_super);function PStream(token,uris,options){var _this=_super.call(this)||this;if(!(_this instanceof PStream)){return new PStream(token,uris,options)}var defaults={TransportFactory:wstransport_1.default};options=options||{};for(var prop in defaults){if(prop in options){continue}options[prop]=defaults[prop]}_this.options=options;_this.token=token||"";_this.status="disconnected";_this.gateway=null;_this.region=null;_this._messageQueue=[];_this._preferredUri=null;_this._uris=uris;_this._handleTransportClose=_this._handleTransportClose.bind(_this);_this._handleTransportError=_this._handleTransportError.bind(_this);_this._handleTransportMessage=_this._handleTransportMessage.bind(_this);_this._handleTransportOpen=_this._handleTransportOpen.bind(_this);_this._log=new log_1.default("PStream");_this.on("error",function(){_this._log.warn("Unexpected error handled in pstream")});var self=_this;_this.addListener("ready",function(){self.status="ready"});_this.addListener("offline",function(){self.status="offline"});_this.addListener("close",function(){self._log.info('Received "close" from server. Destroying PStream...');self._destroy()});_this.transport=new _this.options.TransportFactory(_this._uris,{backoffMaxMs:_this.options.backoffMaxMs,maxPreferredDurationMs:_this.options.maxPreferredDurationMs});Object.defineProperties(_this,{uri:{enumerable:true,get:function(){return this.transport.uri}}});_this.transport.on("close",_this._handleTransportClose);_this.transport.on("error",_this._handleTransportError);_this.transport.on("message",_this._handleTransportMessage);_this.transport.on("open",_this._handleTransportOpen);_this.transport.open();return _this}return PStream}(events_1.EventEmitter);PStream.prototype._handleTransportClose=function(){this.emit("transportClose");if(this.status!=="disconnected"){if(this.status!=="offline"){this.emit("offline",this)}this.status="disconnected"}};PStream.prototype._handleTransportError=function(error){if(!error){this.emit("error",{error:{code:31e3,message:"Websocket closed without a provided reason",twilioError:new errors_1.SignalingErrors.ConnectionDisconnected}});return}this.emit("error",typeof error.code!=="undefined"?{error:error}:error)};PStream.prototype._handleTransportMessage=function(msg){if(!msg||!msg.data||typeof msg.data!=="string"){return}var _a=JSON.parse(msg.data),type=_a.type,_b=_a.payload,payload=_b===void 0?{}:_b;this.gateway=payload.gateway||this.gateway;this.region=payload.region||this.region;if(type==="error"&&payload.error){payload.error.twilioError=new errors_1.SignalingErrors.ConnectionError}this.emit(type,payload)};PStream.prototype._handleTransportOpen=function(){var _this=this;this.status="connected";this.setToken(this.token);this.emit("transportOpen");var messages=this._messageQueue.splice(0,this._messageQueue.length);messages.forEach(function(message){return _this._publish.apply(_this,message)})};PStream.toString=function(){return"[Twilio.PStream class]"};PStream.prototype.toString=function(){return"[Twilio.PStream instance]"};PStream.prototype.setToken=function(token){this._log.info("Setting token and publishing listen");this.token=token;var payload={browserinfo:getBrowserInfo(),token:token};this._publish("listen",payload)};PStream.prototype.sendMessage=function(callsid,content,contenttype,messagetype,voiceeventsid){if(contenttype===void 0){contenttype="application/json"}var payload={callsid:callsid,content:content,contenttype:contenttype,messagetype:messagetype,voiceeventsid:voiceeventsid};this._publish("message",payload,true)};PStream.prototype.register=function(mediaCapabilities){var regPayload={media:mediaCapabilities};this._publish("register",regPayload,true)};PStream.prototype.invite=function(sdp,callsid,preflight,params){var payload={callsid:callsid,preflight:!!preflight,sdp:sdp,twilio:params?{params:params}:{}};this._publish("invite",payload,true)};PStream.prototype.reconnect=function(sdp,callsid,reconnect,params){var payload={callsid:callsid,preflight:false,reconnect:reconnect,sdp:sdp,twilio:params?{params:params}:{}};this._publish("invite",payload,true)};PStream.prototype.answer=function(sdp,callsid){this._publish("answer",{sdp:sdp,callsid:callsid},true)};PStream.prototype.dtmf=function(callsid,digits){this._publish("dtmf",{callsid:callsid,dtmf:digits},true)};PStream.prototype.hangup=function(callsid,message){var payload=message?{callsid:callsid,message:message}:{callsid:callsid};this._publish("hangup",payload,true)};PStream.prototype.reject=function(callsid){this._publish("reject",{callsid:callsid},true)};PStream.prototype.reinvite=function(sdp,callsid){this._publish("reinvite",{sdp:sdp,callsid:callsid},false)};PStream.prototype._destroy=function(){this.transport.removeListener("close",this._handleTransportClose);this.transport.removeListener("error",this._handleTransportError);this.transport.removeListener("message",this._handleTransportMessage);this.transport.removeListener("open",this._handleTransportOpen);this.transport.close();this.emit("offline",this)};PStream.prototype.destroy=function(){this._log.info("PStream.destroy() called...");this._destroy();return this};PStream.prototype.updatePreferredURI=function(uri){this._preferredUri=uri;this.transport.updatePreferredURI(uri)};PStream.prototype.updateURIs=function(uris){this._uris=uris;this.transport.updateURIs(this._uris)};PStream.prototype.publish=function(type,payload){return this._publish(type,payload,true)};PStream.prototype._publish=function(type,payload,shouldRetry){var msg=JSON.stringify({payload:payload,type:type,version:PSTREAM_VERSION});var isSent=!!this.transport.send(msg);if(!isSent){this.emit("error",{error:{code:31009,message:"No transport available to send or receive messages",twilioError:new errors_1.GeneralErrors.TransportError}});if(shouldRetry){this._messageQueue.push([type,payload,true])}}};function getBrowserInfo(){var nav=typeof navigator!=="undefined"?navigator:{};var info={browser:{platform:nav.platform||"unknown",userAgent:nav.userAgent||"unknown"},p:"browser",plugin:"rtc",v:C.RELEASE_VERSION};return info}exports.default=PStream},{"./constants":10,"./errors":15,"./log":18,"./wstransport":38,events:39}],22:[function(require,module,exports){"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:true});exports.getRegionShortcode=exports.getChunderURIs=exports.createSignalingEndpointURL=exports.createEventGatewayURI=exports.defaultEdge=exports.regionToEdge=exports.regionShortcodes=exports.Region=exports.Edge=void 0;var errors_1=require("./errors");var Edge;(function(Edge){Edge["Sydney"]="sydney";Edge["SaoPaulo"]="sao-paulo";Edge["Dublin"]="dublin";Edge["Frankfurt"]="frankfurt";Edge["Tokyo"]="tokyo";Edge["Singapore"]="singapore";Edge["Ashburn"]="ashburn";Edge["Umatilla"]="umatilla";Edge["Roaming"]="roaming";Edge["AshburnIx"]="ashburn-ix";Edge["SanJoseIx"]="san-jose-ix";Edge["LondonIx"]="london-ix";Edge["FrankfurtIx"]="frankfurt-ix";Edge["SingaporeIx"]="singapore-ix";Edge["SydneyIx"]="sydney-ix";Edge["TokyoIx"]="tokyo-ix"})(Edge=exports.Edge||(exports.Edge={}));var Region;(function(Region){Region["Au1"]="au1";Region["Au1Ix"]="au1-ix";Region["Br1"]="br1";Region["De1"]="de1";Region["De1Ix"]="de1-ix";Region["Gll"]="gll";Region["Ie1"]="ie1";Region["Ie1Ix"]="ie1-ix";Region["Ie1Tnx"]="ie1-tnx";Region["Jp1"]="jp1";Region["Jp1Ix"]="jp1-ix";Region["Sg1"]="sg1";Region["Sg1Ix"]="sg1-ix";Region["Sg1Tnx"]="sg1-tnx";Region["Us1"]="us1";Region["Us1Ix"]="us1-ix";Region["Us1Tnx"]="us1-tnx";Region["Us2"]="us2";Region["Us2Ix"]="us2-ix";Region["Us2Tnx"]="us2-tnx"})(Region=exports.Region||(exports.Region={}));exports.regionShortcodes={ASIAPAC_SINGAPORE:Region.Sg1,ASIAPAC_SYDNEY:Region.Au1,ASIAPAC_TOKYO:Region.Jp1,EU_FRANKFURT:Region.De1,EU_IRELAND:Region.Ie1,SOUTH_AMERICA_SAO_PAULO:Region.Br1,US_EAST_VIRGINIA:Region.Us1,US_WEST_OREGON:Region.Us2};exports.regionToEdge=(_a={},_a[Region.Au1]=Edge.Sydney,_a[Region.Br1]=Edge.SaoPaulo,_a[Region.Ie1]=Edge.Dublin,_a[Region.De1]=Edge.Frankfurt,_a[Region.Jp1]=Edge.Tokyo,_a[Region.Sg1]=Edge.Singapore,_a[Region.Us1]=Edge.Ashburn,_a[Region.Us2]=Edge.Umatilla,_a[Region.Gll]=Edge.Roaming,_a[Region.Us1Ix]=Edge.AshburnIx,_a[Region.Us2Ix]=Edge.SanJoseIx,_a[Region.Ie1Ix]=Edge.LondonIx,_a[Region.De1Ix]=Edge.FrankfurtIx,_a[Region.Sg1Ix]=Edge.SingaporeIx,_a[Region.Au1Ix]=Edge.SydneyIx,_a[Region.Jp1Ix]=Edge.TokyoIx,_a[Region.Us1Tnx]=Edge.AshburnIx,_a[Region.Us2Tnx]=Edge.AshburnIx,_a[Region.Ie1Tnx]=Edge.LondonIx,_a[Region.Sg1Tnx]=Edge.SingaporeIx,_a);exports.defaultEdge=Edge.Roaming;var defaultEventGatewayURI="eventgw.twilio.com";function createChunderEdgeURI(edge){return"voice-js."+edge+".twilio.com"}function createEventGatewayURI(region){return region?"eventgw."+region+".twilio.com":defaultEventGatewayURI}exports.createEventGatewayURI=createEventGatewayURI;function createSignalingEndpointURL(uri){return"wss://"+uri+"/signal"}exports.createSignalingEndpointURL=createSignalingEndpointURL;function getChunderURIs(edge){if(!!edge&&typeof edge!=="string"&&!Array.isArray(edge)){throw new errors_1.InvalidArgumentError("If `edge` is provided, it must be of type `string` or an array of strings.")}var uris;if(edge){var edgeParams=Array.isArray(edge)?edge:[edge];uris=edgeParams.map(function(param){return createChunderEdgeURI(param)})}else{uris=[createChunderEdgeURI(exports.defaultEdge)]}return uris}exports.getChunderURIs=getChunderURIs;function getRegionShortcode(region){return exports.regionShortcodes[region]||null}exports.getRegionShortcode=getRegionShortcode},{"./errors":15}],23:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});function request(method,params,callback){var body=JSON.stringify(params.body||{});var headers=new Headers;params.headers=params.headers||[];Object.entries(params.headers).forEach(function(_a){var headerName=_a[0],headerBody=_a[1];return headers.append(headerName,headerBody)});fetch(params.url,{body:body,headers:headers,method:method}).then(function(response){return response.text()},callback).then(function(responseText){return callback(null,responseText)},callback)}var Request=request;Request.get=function get(params,callback){return new this("GET",params,callback)};Request.post=function post(params,callback){return new this("POST",params,callback)};exports.default=Request},{}],24:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var errors_1=require("../errors");var util=require("../util");function getUserMedia(constraints,options){options=options||{};options.util=options.util||util;options.navigator=options.navigator||(typeof navigator!=="undefined"?navigator:null);return new Promise(function(resolve,reject){if(!options.navigator){throw new errors_1.NotSupportedError("getUserMedia is not supported")}switch("function"){case typeof(options.navigator.mediaDevices&&options.navigator.mediaDevices.getUserMedia):return resolve(options.navigator.mediaDevices.getUserMedia(constraints));case typeof options.navigator.webkitGetUserMedia:return options.navigator.webkitGetUserMedia(constraints,resolve,reject);case typeof options.navigator.mozGetUserMedia:return options.navigator.mozGetUserMedia(constraints,resolve,reject);case typeof options.navigator.getUserMedia:return options.navigator.getUserMedia(constraints,resolve,reject);default:throw new errors_1.NotSupportedError("getUserMedia is not supported")}}).catch(function(e){throw options.util.isFirefox()&&e.name==="NotReadableError"?new errors_1.NotSupportedError("Firefox does not currently support opening multiple audio input tracks"+"simultaneously, even across different tabs.\n"+"Related Bugzilla thread: https://bugzilla.mozilla.org/show_bug.cgi?id=1299324"):e})}exports.default=getUserMedia},{"../errors":15,"../util":36}],25:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});exports.IceCandidate=void 0;var IceCandidate=function(){function IceCandidate(iceCandidate,isRemote){if(isRemote===void 0){isRemote=false}this.deleted=false;var cost;var parts=iceCandidate.candidate.split("network-cost ");if(parts[1]){cost=parseInt(parts[1],10)}this.candidateType=iceCandidate.type;this.ip=iceCandidate.ip||iceCandidate.address;this.isRemote=isRemote;this.networkCost=cost;this.port=iceCandidate.port;this.priority=iceCandidate.priority;this.protocol=iceCandidate.protocol;this.relatedAddress=iceCandidate.relatedAddress;this.relatedPort=iceCandidate.relatedPort;this.tcpType=iceCandidate.tcpType;this.transportId=iceCandidate.sdpMid}IceCandidate.prototype.toPayload=function(){return{candidate_type:this.candidateType,deleted:this.deleted,ip:this.ip,is_remote:this.isRemote,"network-cost":this.networkCost,port:this.port,priority:this.priority,protocol:this.protocol,related_address:this.relatedAddress,related_port:this.relatedPort,tcp_type:this.tcpType,transport_id:this.transportId}};return IceCandidate}();exports.IceCandidate=IceCandidate},{}],26:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});exports.PeerConnection=exports.getMediaEngine=exports.enabled=void 0;var peerconnection_1=require("./peerconnection");exports.PeerConnection=peerconnection_1.default;var rtcpc_1=require("./rtcpc");function enabled(){return rtcpc_1.default.test()}exports.enabled=enabled;function getMediaEngine(){return typeof RTCIceGatherer!=="undefined"?"ORTC":"WebRTC"}exports.getMediaEngine=getMediaEngine},{"./peerconnection":29,"./rtcpc":30}],27:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var OLD_MAX_VOLUME=32767;var NativeRTCStatsReport=typeof window!=="undefined"?window.RTCStatsReport:undefined;function MockRTCStatsReport(statsMap){if(!(this instanceof MockRTCStatsReport)){return new MockRTCStatsReport(statsMap)}var self=this;Object.defineProperties(this,{_map:{value:statsMap},size:{enumerable:true,get:function(){return self._map.size}}});this[Symbol.iterator]=statsMap[Symbol.iterator]}if(NativeRTCStatsReport){MockRTCStatsReport.prototype=Object.create(NativeRTCStatsReport.prototype);MockRTCStatsReport.prototype.constructor=MockRTCStatsReport}["entries","forEach","get","has","keys","values"].forEach(function(key){MockRTCStatsReport.prototype[key]=function(){var _a;var args=[];for(var _i=0;_i<arguments.length;_i++){args[_i]=arguments[_i]}return(_a=this._map)[key].apply(_a,args)}});MockRTCStatsReport.fromArray=function fromArray(array){return new MockRTCStatsReport(array.reduce(function(map,rtcStats){map.set(rtcStats.id,rtcStats);return map},new Map))};MockRTCStatsReport.fromRTCStatsResponse=function fromRTCStatsResponse(statsResponse){var activeCandidatePairId;var transportIds=new Map;var statsMap=statsResponse.result().reduce(function(map,report){var id=report.id;switch(report.type){case"googCertificate":map.set(id,createRTCCertificateStats(report));break;case"datachannel":map.set(id,createRTCDataChannelStats(report));break;case"googCandidatePair":if(getBoolean(report,"googActiveConnection")){activeCandidatePairId=id}map.set(id,createRTCIceCandidatePairStats(report));break;case"localcandidate":map.set(id,createRTCIceCandidateStats(report,false));break;case"remotecandidate":map.set(id,createRTCIceCandidateStats(report,true));break;case"ssrc":if(isPresent(report,"packetsReceived")){map.set("rtp-"+id,createRTCInboundRTPStreamStats(report))}else{map.set("rtp-"+id,createRTCOutboundRTPStreamStats(report))}map.set("track-"+id,createRTCMediaStreamTrackStats(report));map.set("codec-"+id,createRTCCodecStats(report));break;case"googComponent":var transportReport=createRTCTransportStats(report);transportIds.set(transportReport.selectedCandidatePairId,id);map.set(id,createRTCTransportStats(report));break}return map},new Map);if(activeCandidatePairId){var activeTransportId=transportIds.get(activeCandidatePairId);if(activeTransportId){statsMap.get(activeTransportId).dtlsState="connected"}}return new MockRTCStatsReport(statsMap)};function createRTCTransportStats(report){return{bytesReceived:undefined,bytesSent:undefined,dtlsState:undefined,id:report.id,localCertificateId:report.stat("localCertificateId"),remoteCertificateId:report.stat("remoteCertificateId"),rtcpTransportStatsId:undefined,selectedCandidatePairId:report.stat("selectedCandidatePairId"),timestamp:Date.parse(report.timestamp),type:"transport"}}function createRTCCodecStats(report){return{channels:undefined,clockRate:undefined,id:report.id,implementation:undefined,mimeType:report.stat("mediaType")+"/"+report.stat("googCodecName"),payloadType:undefined,sdpFmtpLine:undefined,timestamp:Date.parse(report.timestamp),type:"codec"}}function createRTCMediaStreamTrackStats(report){return{audioLevel:isPresent(report,"audioOutputLevel")?getInt(report,"audioOutputLevel")/OLD_MAX_VOLUME:(getInt(report,"audioInputLevel")||0)/OLD_MAX_VOLUME,detached:undefined,echoReturnLoss:getFloat(report,"googEchoCancellationReturnLoss"),echoReturnLossEnhancement:getFloat(report,"googEchoCancellationReturnLossEnhancement"),ended:undefined,frameHeight:isPresent(report,"googFrameHeightReceived")?getInt(report,"googFrameHeightReceived"):getInt(report,"googFrameHeightSent"),frameWidth:isPresent(report,"googFrameWidthReceived")?getInt(report,"googFrameWidthReceived"):getInt(report,"googFrameWidthSent"),framesCorrupted:undefined,framesDecoded:getInt(report,"framesDecoded"),framesDropped:undefined,framesPerSecond:undefined,framesReceived:undefined,framesSent:getInt(report,"framesEncoded"),fullFramesLost:undefined,id:report.id,kind:report.stat("mediaType"),partialFramesLost:undefined,remoteSource:undefined,ssrcIds:undefined,timestamp:Date.parse(report.timestamp),trackIdentifier:report.stat("googTrackId"),type:"track"}}function createRTCRTPStreamStats(report,isInbound){return{associateStatsId:undefined,codecId:"codec-"+report.id,firCount:isInbound?getInt(report,"googFirsSent"):undefined,id:report.id,isRemote:undefined,mediaType:report.stat("mediaType"),nackCount:isInbound?getInt(report,"googNacksSent"):getInt(report,"googNacksReceived"),pliCount:isInbound?getInt(report,"googPlisSent"):getInt(report,"googPlisReceived"),qpSum:getInt(report,"qpSum"),sliCount:undefined,ssrc:report.stat("ssrc"),timestamp:Date.parse(report.timestamp),trackId:"track-"+report.id,transportId:report.stat("transportId")}}function createRTCInboundRTPStreamStats(report){var rtp=createRTCRTPStreamStats(report,true);Object.assign(rtp,{burstDiscardCount:undefined,burstDiscardRate:undefined,burstLossCount:undefined,burstLossRate:undefined,burstPacketsDiscarded:undefined,burstPacketsLost:undefined,bytesReceived:getInt(report,"bytesReceived"),fractionLost:undefined,framesDecoded:getInt(report,"framesDecoded"),gapDiscardRate:undefined,gapLossRate:undefined,jitter:convertMsToSeconds(report.stat("googJitterReceived")),packetsDiscarded:undefined,packetsLost:getInt(report,"packetsLost"),packetsReceived:getInt(report,"packetsReceived"),packetsRepaired:undefined,roundTripTime:convertMsToSeconds(report.stat("googRtt")),type:"inbound-rtp"});return rtp}function createRTCOutboundRTPStreamStats(report){var rtp=createRTCRTPStreamStats(report,false);Object.assign(rtp,{bytesSent:getInt(report,"bytesSent"),framesEncoded:getInt(report,"framesEncoded"),packetsSent:getInt(report,"packetsSent"),remoteTimestamp:undefined,targetBitrate:undefined,type:"outbound-rtp"});return rtp}function createRTCIceCandidateStats(report,isRemote){return{candidateType:translateCandidateType(report.stat("candidateType")),deleted:undefined,id:report.id,ip:report.stat("ipAddress"),isRemote:isRemote,port:getInt(report,"portNumber"),priority:getFloat(report,"priority"),protocol:report.stat("transport"),relayProtocol:undefined,timestamp:Date.parse(report.timestamp),transportId:undefined,type:isRemote?"remote-candidate":"local-candidate",url:undefined}}function createRTCIceCandidatePairStats(report){return{availableIncomingBitrate:undefined,availableOutgoingBitrate:undefined,bytesReceived:getInt(report,"bytesReceived"),bytesSent:getInt(report,"bytesSent"),consentRequestsSent:getInt(report,"consentRequestsSent"),currentRoundTripTime:convertMsToSeconds(report.stat("googRtt")),id:report.id,lastPacketReceivedTimestamp:undefined,lastPacketSentTimestamp:undefined,localCandidateId:report.stat("localCandidateId"),nominated:undefined,priority:undefined,readable:undefined,remoteCandidateId:report.stat("remoteCandidateId"),requestsReceived:getInt(report,"requestsReceived"),requestsSent:getInt(report,"requestsSent"),responsesReceived:getInt(report,"responsesReceived"),responsesSent:getInt(report,"responsesSent"),retransmissionsReceived:undefined,retransmissionsSent:undefined,state:undefined,timestamp:Date.parse(report.timestamp),totalRoundTripTime:undefined,transportId:report.stat("googChannelId"),type:"candidate-pair",writable:getBoolean(report,"googWritable")}}function createRTCCertificateStats(report){return{base64Certificate:report.stat("googDerBase64"),fingerprint:report.stat("googFingerprint"),fingerprintAlgorithm:report.stat("googFingerprintAlgorithm"),id:report.id,issuerCertificateId:report.stat("googIssuerId"),timestamp:Date.parse(report.timestamp),type:"certificate"}}function createRTCDataChannelStats(report){return{bytesReceived:undefined,bytesSent:undefined,datachannelid:report.stat("datachannelid"),id:report.id,label:report.stat("label"),messagesReceived:undefined,messagesSent:undefined,protocol:report.stat("protocol"),state:report.stat("state"),timestamp:Date.parse(report.timestamp),transportId:report.stat("transportId"),type:"data-channel"}}function convertMsToSeconds(inMs){return isNaN(inMs)||inMs===""?undefined:parseInt(inMs,10)/1e3}function translateCandidateType(type){switch(type){case"peerreflexive":return"prflx";case"serverreflexive":return"srflx";case"host":case"relay":default:return type}}function getInt(report,statName){var stat=report.stat(statName);return isPresent(report,statName)?parseInt(stat,10):undefined}function getFloat(report,statName){var stat=report.stat(statName);return isPresent(report,statName)?parseFloat(stat):undefined}function getBoolean(report,statName){var stat=report.stat(statName);return isPresent(report,statName)?stat==="true"||stat===true:undefined}function isPresent(report,statName){var stat=report.stat(statName);return typeof stat!=="undefined"&&stat!==""}exports.default=MockRTCStatsReport},{}],28:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});exports.isNonNegativeNumber=exports.calculate=void 0;var r0=94.768;function calculate(rtt,jitter,fractionLost){if(typeof rtt!=="number"||typeof jitter!=="number"||typeof fractionLost!=="number"||!isNonNegativeNumber(rtt)||!isNonNegativeNumber(jitter)||!isNonNegativeNumber(fractionLost)){return null}var effectiveLatency=rtt+jitter*2+10;var rFactor=0;switch(true){case effectiveLatency<160:rFactor=r0-effectiveLatency/40;break;case effectiveLatency<1e3:rFactor=r0-(effectiveLatency-120)/10;break}switch(true){case fractionLost<=rFactor/2.5:rFactor=Math.max(rFactor-fractionLost*2.5,6.52);break;default:rFactor=0;break}var mos=1+.035*rFactor+7e-6*rFactor*(rFactor-60)*(100-rFactor);return mos}exports.calculate=calculate;function isNonNegativeNumber(n){return typeof n==="number"&&!isNaN(n)&&isFinite(n)&&n>=0}exports.isNonNegativeNumber=isNonNegativeNumber;exports.default={calculate:calculate,isNonNegativeNumber:isNonNegativeNumber}},{}],29:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var errors_1=require("../errors");var log_1=require("../log");var util=require("../util");var rtcpc_1=require("./rtcpc");var sdp_1=require("./sdp");var ICE_GATHERING_TIMEOUT=15e3;var ICE_GATHERING_FAIL_NONE="none";var ICE_GATHERING_FAIL_TIMEOUT="timeout";var INITIAL_ICE_CONNECTION_STATE="new";var VOLUME_INTERVAL_MS=50;function PeerConnection(audioHelper,pstream,options){if(!audioHelper||!pstream){throw new errors_1.InvalidArgumentError("Audiohelper, and pstream are required arguments")}if(!(this instanceof PeerConnection)){return new PeerConnection(audioHelper,pstream,options)}this._log=new log_1.default("PeerConnection");function noop(){this._log.warn("Unexpected noop call in peerconnection")}this.onaudio=noop;this.onopen=noop;this.onerror=noop;this.onclose=noop;this.ondisconnected=noop;this.onfailed=noop;this.onconnected=noop;this.onreconnected=noop;this.onsignalingstatechange=noop;this.ondtlstransportstatechange=noop;this.onicegatheringfailure=noop;this.onicegatheringstatechange=noop;this.oniceconnectionstatechange=noop;this.onpcconnectionstatechange=noop;this.onicecandidate=noop;this.onselectedcandidatepairchange=noop;this.onvolume=noop;this.version=null;this.pstream=pstream;this.stream=null;this.sinkIds=new Set(["default"]);this.outputs=new Map;this.status="connecting";this.callSid=null;this.isMuted=false;var AudioContext=typeof window!=="undefined"&&(window.AudioContext||window.webkitAudioContext);this._isSinkSupported=!!AudioContext&&typeof HTMLAudioElement!=="undefined"&&HTMLAudioElement.prototype.setSinkId;this._audioContext=AudioContext&&audioHelper._audioContext;this._audioHelper=audioHelper;this._hasIceCandidates=false;this._hasIceGatheringFailures=false;this._iceGatheringTimeoutId=null;this._masterAudio=null;this._masterAudioDeviceId=null;this._mediaStreamSource=null;this._dtmfSender=null;this._dtmfSenderUnsupported=false;this._callEvents=[];this._nextTimeToPublish=Date.now();this._onAnswerOrRinging=noop;this._onHangup=noop;this._remoteStream=null;this._shouldManageStream=true;this._iceState=INITIAL_ICE_CONNECTION_STATE;this._isUnifiedPlan=options.isUnifiedPlan;this.options=options=options||{};this.navigator=options.navigator||(typeof navigator!=="undefined"?navigator:null);this.util=options.util||util;this.codecPreferences=options.codecPreferences;return this}PeerConnection.prototype.uri=function(){return this._uri};PeerConnection.prototype.openDefaultDeviceWithConstraints=function(constraints){return this._audioHelper._openDefaultDeviceWithConstraints(constraints).then(this._setInputTracksFromStream.bind(this,false))};PeerConnection.prototype.setInputTracksFromStream=function(stream){var self=this;return this._setInputTracksFromStream(true,stream).then(function(){self._shouldManageStream=false})};PeerConnection.prototype._createAnalyser=function(audioContext,options){options=Object.assign({fftSize:32,smoothingTimeConstant:.3},options);var analyser=audioContext.createAnalyser();for(var field in options){analyser[field]=options[field]}return analyser};PeerConnection.prototype._setVolumeHandler=function(handler){this.onvolume=handler};PeerConnection.prototype._startPollingVolume=function(){if(!this._audioContext||!this.stream||!this._remoteStream){return}var audioContext=this._audioContext;var inputAnalyser=this._inputAnalyser=this._createAnalyser(audioContext);var inputBufferLength=inputAnalyser.frequencyBinCount;var inputDataArray=new Uint8Array(inputBufferLength);this._inputAnalyser2=this._createAnalyser(audioContext,{maxDecibels:0,minDecibels:-127,smoothingTimeConstant:0});var outputAnalyser=this._outputAnalyser=this._createAnalyser(audioContext);var outputBufferLength=outputAnalyser.frequencyBinCount;var outputDataArray=new Uint8Array(outputBufferLength);this._outputAnalyser2=this._createAnalyser(audioContext,{maxDecibels:0,minDecibels:-127,smoothingTimeConstant:0});this._updateInputStreamSource(this.stream);this._updateOutputStreamSource(this._remoteStream);var self=this;setTimeout(function emitVolume(){if(!self._audioContext){return}else if(self.status==="closed"){self._inputAnalyser.disconnect();self._outputAnalyser.disconnect();self._inputAnalyser2.disconnect();self._outputAnalyser2.disconnect();return}self._inputAnalyser.getByteFrequencyData(inputDataArray);var inputVolume=self.util.average(inputDataArray);self._inputAnalyser2.getByteFrequencyData(inputDataArray);var inputVolume2=self.util.average(inputDataArray);self._outputAnalyser.getByteFrequencyData(outputDataArray);var outputVolume=self.util.average(outputDataArray);self._outputAnalyser2.getByteFrequencyData(outputDataArray);var outputVolume2=self.util.average(outputDataArray);self.onvolume(inputVolume/255,outputVolume/255,inputVolume2,outputVolume2);setTimeout(emitVolume,VOLUME_INTERVAL_MS)},VOLUME_INTERVAL_MS)};PeerConnection.prototype._stopStream=function _stopStream(){if(!this._shouldManageStream){return}this._audioHelper._stopDefaultInputDeviceStream()};PeerConnection.prototype._updateInputStreamSource=function(stream){if(this._inputStreamSource){this._inputStreamSource.disconnect()}try{this._inputStreamSource=this._audioContext.createMediaStreamSource(stream);this._inputStreamSource.connect(this._inputAnalyser);this._inputStreamSource.connect(this._inputAnalyser2)}catch(ex){this._log.warn("Unable to update input MediaStreamSource",ex);this._inputStreamSource=null}};PeerConnection.prototype._updateOutputStreamSource=function(stream){if(this._outputStreamSource){this._outputStreamSource.disconnect()}try{this._outputStreamSource=this._audioContext.createMediaStreamSource(stream);this._outputStreamSource.connect(this._outputAnalyser);this._outputStreamSource.connect(this._outputAnalyser2)}catch(ex){this._log.warn("Unable to update output MediaStreamSource",ex);this._outputStreamSource=null}};PeerConnection.prototype._setInputTracksFromStream=function(shouldClone,newStream){return this._isUnifiedPlan?this._setInputTracksForUnifiedPlan(shouldClone,newStream):this._setInputTracksForPlanB(shouldClone,newStream)};PeerConnection.prototype._setInputTracksForPlanB=function(shouldClone,newStream){var _this=this;if(!newStream){return Promise.reject(new errors_1.InvalidArgumentError("Can not set input stream to null while in a call"))}if(!newStream.getAudioTracks().length){return Promise.reject(new errors_1.InvalidArgumentError("Supplied input stream has no audio tracks"))}var localStream=this.stream;if(!localStream){this.stream=shouldClone?cloneStream(newStream):newStream}else{this._stopStream();removeStream(this.version.pc,localStream);localStream.getAudioTracks().forEach(localStream.removeTrack,localStream);newStream.getAudioTracks().forEach(localStream.addTrack,localStream);addStream(this.version.pc,newStream);this._updateInputStreamSource(this.stream)}this.mute(this.isMuted);if(!this.version){return Promise.resolve(this.stream)}return new Promise(function(resolve,reject){_this.version.createOffer(_this.options.maxAverageBitrate,_this.codecPreferences,{audio:true},function(){_this.version.processAnswer(_this.codecPreferences,_this._answerSdp,function(){resolve(_this.stream)},reject)},reject)})};PeerConnection.prototype._setInputTracksForUnifiedPlan=function(shouldClone,newStream){var _this=this;if(!newStream){return Promise.reject(new errors_1.InvalidArgumentError("Can not set input stream to null while in a call"))}if(!newStream.getAudioTracks().length){return Promise.reject(new errors_1.InvalidArgumentError("Supplied input stream has no audio tracks"))}var localStream=this.stream;var getStreamPromise=function(){_this.mute(_this.isMuted);return Promise.resolve(_this.stream)};if(!localStream){this.stream=shouldClone?cloneStream(newStream):newStream}else{if(this._shouldManageStream){this._stopStream()}if(!this._sender){this._sender=this.version.pc.getSenders()[0]}return this._sender.replaceTrack(newStream.getAudioTracks()[0]).then(function(){_this._updateInputStreamSource(newStream);_this.stream=shouldClone?cloneStream(newStream):newStream;return getStreamPromise()})}return getStreamPromise()};PeerConnection.prototype._onInputDevicesChanged=function(){if(!this.stream){return}var activeInputWasLost=this.stream.getAudioTracks().every(function(track){return track.readyState==="ended"});if(activeInputWasLost&&this._shouldManageStream){this.openDefaultDeviceWithConstraints({audio:true})}};PeerConnection.prototype._onIceGatheringFailure=function(type){this._hasIceGatheringFailures=true;this.onicegatheringfailure(type)};PeerConnection.prototype._onMediaConnectionStateChange=function(newState){var previousState=this._iceState;if(previousState===newState||newState!=="connected"&&newState!=="disconnected"&&newState!=="failed"){return}this._iceState=newState;var message;switch(newState){case"connected":if(previousState==="disconnected"||previousState==="failed"){message="ICE liveliness check succeeded. Connection with Twilio restored";this._log.info(message);this.onreconnected(message)}else{message="Media connection established.";this._log.info(message);this.onconnected(message)}this._stopIceGatheringTimeout();this._hasIceGatheringFailures=false;break;case"disconnected":message="ICE liveliness check failed. May be having trouble connecting to Twilio";this._log.warn(message);this.ondisconnected(message);break;case"failed":message="Connection with Twilio was interrupted.";this._log.warn(message);this.onfailed(message);break}};PeerConnection.prototype._setSinkIds=function(sinkIds){if(!this._isSinkSupported){return Promise.reject(new errors_1.NotSupportedError("Audio output selection is not supported by this browser"))}this.sinkIds=new Set(sinkIds.forEach?sinkIds:[sinkIds]);return this.version?this._updateAudioOutputs():Promise.resolve()};PeerConnection.prototype._startIceGatheringTimeout=function startIceGatheringTimeout(){var _this=this;this._stopIceGatheringTimeout();this._iceGatheringTimeoutId=setTimeout(function(){_this._onIceGatheringFailure(ICE_GATHERING_FAIL_TIMEOUT)},ICE_GATHERING_TIMEOUT)};PeerConnection.prototype._stopIceGatheringTimeout=function stopIceGatheringTimeout(){clearInterval(this._iceGatheringTimeoutId)};PeerConnection.prototype._updateAudioOutputs=function updateAudioOutputs(){var addedOutputIds=Array.from(this.sinkIds).filter(function(id){return!this.outputs.has(id)},this);var removedOutputIds=Array.from(this.outputs.keys()).filter(function(id){return!this.sinkIds.has(id)},this);var self=this;var createOutputPromises=addedOutputIds.map(this._createAudioOutput,this);return Promise.all(createOutputPromises).then(function(){return Promise.all(removedOutputIds.map(self._removeAudioOutput,self))})};PeerConnection.prototype._createAudio=function createAudio(arr){var audio=new Audio(arr);this.onaudio(audio);return audio};PeerConnection.prototype._createAudioOutput=function createAudioOutput(id){var dest=null;if(this._mediaStreamSource){dest=this._audioContext.createMediaStreamDestination();this._mediaStreamSource.connect(dest)}var audio=this._createAudio();setAudioSource(audio,dest&&dest.stream?dest.stream:this.pcStream);var self=this;return audio.setSinkId(id).then(function(){return audio.play()}).then(function(){self.outputs.set(id,{audio:audio,dest:dest})})};PeerConnection.prototype._removeAudioOutputs=function removeAudioOutputs(){if(this._masterAudio&&typeof this._masterAudioDeviceId!=="undefined"){this._disableOutput(this,this._masterAudioDeviceId);this.outputs.delete(this._masterAudioDeviceId);this._masterAudioDeviceId=null;if(!this._masterAudio.paused){this._masterAudio.pause()}if(typeof this._masterAudio.srcObject!=="undefined"){this._masterAudio.srcObject=null}else{this._masterAudio.src=""}this._masterAudio=null}return Array.from(this.outputs.keys()).map(this._removeAudioOutput,this)};PeerConnection.prototype._disableOutput=function disableOutput(pc,id){var output=pc.outputs.get(id);if(!output){return}if(output.audio){output.audio.pause();output.audio.src=""}if(output.dest){output.dest.disconnect()}};PeerConnection.prototype._reassignMasterOutput=function reassignMasterOutput(pc,masterId){var masterOutput=pc.outputs.get(masterId);pc.outputs.delete(masterId);var self=this;var idToReplace=Array.from(pc.outputs.keys())[0]||"default";return masterOutput.audio.setSinkId(idToReplace).then(function(){self._disableOutput(pc,idToReplace);pc.outputs.set(idToReplace,masterOutput);pc._masterAudioDeviceId=idToReplace}).catch(function rollback(){pc.outputs.set(masterId,masterOutput);self._log.info("Could not reassign master output. Attempted to roll back.")})};PeerConnection.prototype._removeAudioOutput=function removeAudioOutput(id){if(this._masterAudioDeviceId===id){return this._reassignMasterOutput(this,id)}this._disableOutput(this,id);this.outputs.delete(id);return Promise.resolve()};PeerConnection.prototype._onAddTrack=function onAddTrack(pc,stream){var audio=pc._masterAudio=this._createAudio();setAudioSource(audio,stream);audio.play();var deviceId=Array.from(pc.outputs.keys())[0]||"default";pc._masterAudioDeviceId=deviceId;pc.outputs.set(deviceId,{audio:audio});try{pc._mediaStreamSource=pc._audioContext.createMediaStreamSource(stream)}catch(ex){this._log.warn("Unable to create a MediaStreamSource from onAddTrack",ex);this._mediaStreamSource=null}pc.pcStream=stream;pc._updateAudioOutputs()};PeerConnection.prototype._fallbackOnAddTrack=function fallbackOnAddTrack(pc,stream){var audio=document&&document.createElement("audio");audio.autoplay=true;if(!setAudioSource(audio,stream)){pc._log.info("Error attaching stream to element.")}pc.outputs.set("default",{audio:audio})};PeerConnection.prototype._setEncodingParameters=function(enableDscp){if(!enableDscp||!this._sender||typeof this._sender.getParameters!=="function"||typeof this._sender.setParameters!=="function"){return}var params=this._sender.getParameters();if(!params.priority&&!(params.encodings&&params.encodings.length)){return}params.priority="high";if(params.encodings&&params.encodings.length){params.encodings.forEach(function(encoding){encoding.priority="high";encoding.networkPriority="high"})}this._sender.setParameters(params)};PeerConnection.prototype._setupPeerConnection=function(rtcConstraints,rtcConfiguration){var _this=this;var self=this;var version=new(this.options.rtcpcFactory||rtcpc_1.default)({RTCPeerConnection:this.options.RTCPeerConnection});version.create(rtcConstraints,rtcConfiguration);addStream(version.pc,this.stream);var eventName="ontrack"in version.pc?"ontrack":"onaddstream";version.pc[eventName]=function(event){var stream=self._remoteStream=event.stream||event.streams[0];if(typeof version.pc.getSenders==="function"){_this._sender=version.pc.getSenders()[0]}if(self._isSinkSupported){self._onAddTrack(self,stream)}else{self._fallbackOnAddTrack(self,stream)}self._startPollingVolume()};return version};PeerConnection.prototype._maybeSetIceAggressiveNomination=function(sdp){return this.options.forceAggressiveIceNomination?sdp_1.setIceAggressiveNomination(sdp):sdp};PeerConnection.prototype._setupChannel=function(){var _this=this;var pc=this.version.pc;this.version.pc.onopen=function(){_this.status="open";_this.onopen()};this.version.pc.onstatechange=function(){if(_this.version.pc&&_this.version.pc.readyState==="stable"){_this.status="open";_this.onopen()}};this.version.pc.onsignalingstatechange=function(){var state=pc.signalingState;_this._log.info('signalingState is "'+state+'"');if(_this.version.pc&&_this.version.pc.signalingState==="stable"){_this.status="open";_this.onopen()}_this.onsignalingstatechange(pc.signalingState)};pc.onconnectionstatechange=function(event){var state=pc.connectionState;if(!state&&event&&event.target){var targetPc=event.target;state=targetPc.connectionState||targetPc.connectionState_;_this._log.info("pc.connectionState not detected. Using target PC. State="+state)}if(!state){_this._log.warn('onconnectionstatechange detected but state is "'+state+'"')}else{_this._log.info('pc.connectionState is "'+state+'"')}_this.onpcconnectionstatechange(state);_this._onMediaConnectionStateChange(state)};pc.onicecandidate=function(event){var candidate=event.candidate;if(candidate){_this._hasIceCandidates=true;_this.onicecandidate(candidate);_this._setupRTCIceTransportListener()}_this._log.info("ICE Candidate: "+JSON.stringify(candidate))};pc.onicegatheringstatechange=function(){var state=pc.iceGatheringState;if(state==="gathering"){_this._startIceGatheringTimeout()}else if(state==="complete"){_this._stopIceGatheringTimeout();if(!_this._hasIceCandidates){_this._onIceGatheringFailure(ICE_GATHERING_FAIL_NONE)}if(_this._hasIceCandidates&&_this._hasIceGatheringFailures){_this._startIceGatheringTimeout()}}_this._log.info('pc.iceGatheringState is "'+pc.iceGatheringState+'"');_this.onicegatheringstatechange(state)};pc.oniceconnectionstatechange=function(){_this._log.info('pc.iceConnectionState is "'+pc.iceConnectionState+'"');_this.oniceconnectionstatechange(pc.iceConnectionState);_this._onMediaConnectionStateChange(pc.iceConnectionState)}};PeerConnection.prototype._initializeMediaStream=function(rtcConstraints,rtcConfiguration){if(this.status==="open"){return false}if(this.pstream.status==="disconnected"){this.onerror({info:{code:31e3,message:"Cannot establish connection. Client is disconnected",twilioError:new errors_1.SignalingErrors.ConnectionDisconnected}});this.close();return false}this.version=this._setupPeerConnection(rtcConstraints,rtcConfiguration);this._setupChannel();return true};PeerConnection.prototype._removeReconnectionListeners=function(){if(this.pstream){this.pstream.removeListener("answer",this._onAnswerOrRinging);this.pstream.removeListener("hangup",this._onHangup)}};PeerConnection.prototype._setupRTCDtlsTransportListener=function(){var _this=this;var dtlsTransport=this.getRTCDtlsTransport();if(!dtlsTransport||dtlsTransport.onstatechange){return}var handler=function(){_this._log.info('dtlsTransportState is "'+dtlsTransport.state+'"');_this.ondtlstransportstatechange(dtlsTransport.state)};handler();dtlsTransport.onstatechange=handler};PeerConnection.prototype._setupRTCIceTransportListener=function(){var _this=this;var iceTransport=this._getRTCIceTransport();if(!iceTransport||iceTransport.onselectedcandidatepairchange){return}iceTransport.onselectedcandidatepairchange=function(){return _this.onselectedcandidatepairchange(iceTransport.getSelectedCandidatePair())}};PeerConnection.prototype.iceRestart=function(){var _this=this;this._log.info("Attempting to restart ICE...");this._hasIceCandidates=false;this.version.createOffer(this.options.maxAverageBitrate,this.codecPreferences,{iceRestart:true}).then(function(){_this._removeReconnectionListeners();_this._onAnswerOrRinging=function(payload){_this._removeReconnectionListeners();if(!payload.sdp||_this.version.pc.signalingState!=="have-local-offer"){var message="Invalid state or param during ICE Restart:"+("hasSdp:"+!!payload.sdp+", signalingState:"+_this.version.pc.signalingState);_this._log.warn(message);return}var sdp=_this._maybeSetIceAggressiveNomination(payload.sdp);_this._answerSdp=sdp;if(_this.status!=="closed"){_this.version.processAnswer(_this.codecPreferences,sdp,null,function(err){var message=err&&err.message?err.message:err;_this._log.error("Failed to process answer during ICE Restart. Error: "+message)})}};_this._onHangup=function(){_this._log.info("Received hangup during ICE Restart");_this._removeReconnectionListeners()};_this.pstream.on("answer",_this._onAnswerOrRinging);_this.pstream.on("hangup",_this._onHangup);_this.pstream.reinvite(_this.version.getSDP(),_this.callSid)}).catch(function(err){var message=err&&err.message?err.message:err;_this._log.error("Failed to createOffer during ICE Restart. Error: "+message);_this.onfailed(message)})};PeerConnection.prototype.makeOutgoingCall=function(token,params,callsid,rtcConstraints,rtcConfiguration,onMediaStarted){var _this=this;if(!this._initializeMediaStream(rtcConstraints,rtcConfiguration)){return}var self=this;this.callSid=callsid;function onAnswerSuccess(){if(self.options){self._setEncodingParameters(self.options.dscp)}onMediaStarted(self.version.pc)}function onAnswerError(err){var errMsg=err.message||err;self.onerror({info:{code:31e3,message:"Error processing answer: "+errMsg,twilioError:new errors_1.MediaErrors.ClientRemoteDescFailed}})}this._onAnswerOrRinging=function(payload){if(!payload.sdp){return}var sdp=_this._maybeSetIceAggressiveNomination(payload.sdp);self._answerSdp=sdp;if(self.status!=="closed"){self.version.processAnswer(_this.codecPreferences,sdp,onAnswerSuccess,onAnswerError)}self.pstream.removeListener("answer",self._onAnswerOrRinging);self.pstream.removeListener("ringing",self._onAnswerOrRinging)};this.pstream.on("answer",this._onAnswerOrRinging);this.pstream.on("ringing",this._onAnswerOrRinging);function onOfferSuccess(){if(self.status!=="closed"){self.pstream.invite(self.version.getSDP(),self.callSid,self.options.preflight,params);self._setupRTCDtlsTransportListener()}}function onOfferError(err){var errMsg=err.message||err;self.onerror({info:{code:31e3,message:"Error creating the offer: "+errMsg,twilioError:new errors_1.MediaErrors.ClientLocalDescFailed}})}this.version.createOffer(this.options.maxAverageBitrate,this.codecPreferences,{audio:true},onOfferSuccess,onOfferError)};PeerConnection.prototype.answerIncomingCall=function(callSid,sdp,rtcConstraints,rtcConfiguration,onMediaStarted){if(!this._initializeMediaStream(rtcConstraints,rtcConfiguration)){return}sdp=this._maybeSetIceAggressiveNomination(sdp);this._answerSdp=sdp.replace(/^a=setup:actpass$/gm,"a=setup:passive");this.callSid=callSid;var self=this;function onAnswerSuccess(){if(self.status!=="closed"){self.pstream.answer(self.version.getSDP(),callSid);if(self.options){self._setEncodingParameters(self.options.dscp)}onMediaStarted(self.version.pc);self._setupRTCDtlsTransportListener()}}function onAnswerError(err){var errMsg=err.message||err;self.onerror({info:{code:31e3,message:"Error creating the answer: "+errMsg,twilioError:new errors_1.MediaErrors.ClientRemoteDescFailed}})}this.version.processSDP(this.options.maxAverageBitrate,this.codecPreferences,sdp,{audio:true},onAnswerSuccess,onAnswerError)};PeerConnection.prototype.close=function(){if(this.version&&this.version.pc){if(this.version.pc.signalingState!=="closed"){this.version.pc.close()}this.version.pc=null}if(this.stream){this.mute(false);this._stopStream()}this.stream=null;this._removeReconnectionListeners();this._stopIceGatheringTimeout();Promise.all(this._removeAudioOutputs()).catch(function(){});if(this._mediaStreamSource){this._mediaStreamSource.disconnect()}if(this._inputAnalyser){this._inputAnalyser.disconnect()}if(this._outputAnalyser){this._outputAnalyser.disconnect()}if(this._inputAnalyser2){this._inputAnalyser2.disconnect()}if(this._outputAnalyser2){this._outputAnalyser2.disconnect()}this.status="closed";this.onclose()};PeerConnection.prototype.reject=function(callSid){this.callSid=callSid};PeerConnection.prototype.ignore=function(callSid){this.callSid=callSid};PeerConnection.prototype.mute=function(shouldMute){this.isMuted=shouldMute;if(!this.stream){return}if(this._sender&&this._sender.track){this._sender.track.enabled=!shouldMute}else{var audioTracks=typeof this.stream.getAudioTracks==="function"?this.stream.getAudioTracks():this.stream.audioTracks;audioTracks.forEach(function(track){track.enabled=!shouldMute})}};PeerConnection.prototype.getOrCreateDTMFSender=function getOrCreateDTMFSender(){if(this._dtmfSender||this._dtmfSenderUnsupported){return this._dtmfSender||null}var self=this;var pc=this.version.pc;if(!pc){this._log.warn("No RTCPeerConnection available to call createDTMFSender on");return null}if(typeof pc.getSenders==="function"&&(typeof RTCDTMFSender==="function"||typeof RTCDtmfSender==="function")){var chosenSender=pc.getSenders().find(function(sender){return sender.dtmf});if(chosenSender){this._log.info("Using RTCRtpSender#dtmf");this._dtmfSender=chosenSender.dtmf;return this._dtmfSender}}if(typeof pc.createDTMFSender==="function"&&typeof pc.getLocalStreams==="function"){var track=pc.getLocalStreams().map(function(stream){var tracks=self._getAudioTracks(stream);return tracks&&tracks[0]})[0];if(!track){this._log.warn("No local audio MediaStreamTrack available on the RTCPeerConnection to pass to createDTMFSender");return null}this._log.info("Creating RTCDTMFSender");this._dtmfSender=pc.createDTMFSender(track);return this._dtmfSender}this._log.info("RTCPeerConnection does not support RTCDTMFSender");this._dtmfSenderUnsupported=true;return null};PeerConnection.prototype.getRTCDtlsTransport=function getRTCDtlsTransport(){var sender=this.version&&this.version.pc&&typeof this.version.pc.getSenders==="function"&&this.version.pc.getSenders()[0];return sender&&sender.transport||null};PeerConnection.prototype._canStopMediaStreamTrack=function(){return typeof MediaStreamTrack.prototype.stop==="function"};PeerConnection.prototype._getAudioTracks=function(stream){return typeof stream.getAudioTracks==="function"?stream.getAudioTracks():stream.audioTracks};PeerConnection.prototype._getRTCIceTransport=function _getRTCIceTransport(){var dtlsTransport=this.getRTCDtlsTransport();return dtlsTransport&&dtlsTransport.iceTransport||null};PeerConnection.protocol=function(){return rtcpc_1.default.test()?new rtcpc_1.default:null}();function addStream(pc,stream){if(typeof pc.addTrack==="function"){stream.getAudioTracks().forEach(function(track){pc.addTrack(track,stream)})}else{pc.addStream(stream)}}function cloneStream(oldStream){var newStream=typeof MediaStream!=="undefined"?new MediaStream:new webkitMediaStream;oldStream.getAudioTracks().forEach(newStream.addTrack,newStream);return newStream}function removeStream(pc,stream){if(typeof pc.removeTrack==="function"){pc.getSenders().forEach(function(sender){pc.removeTrack(sender)})}else{pc.removeStream(stream)}}function setAudioSource(audio,stream){if(typeof audio.srcObject!=="undefined"){audio.srcObject=stream}else if(typeof audio.mozSrcObject!=="undefined"){audio.mozSrcObject=stream}else if(typeof audio.src!=="undefined"){var _window=audio.options.window||window;audio.src=(_window.URL||_window.webkitURL).createObjectURL(stream)}else{return false}return true}PeerConnection.enabled=rtcpc_1.default.test();exports.default=PeerConnection},{"../errors":15,"../log":18,"../util":36,"./rtcpc":30,"./sdp":31}],30:[function(require,module,exports){(function(global){(function(){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var log_1=require("../log");var util=require("../util");var sdp_1=require("./sdp");var RTCPeerConnectionShim=require("rtcpeerconnection-shim");function RTCPC(options){if(typeof window==="undefined"){this.log.info("No RTCPeerConnection implementation available. The window object was not found.");return}if(options&&options.RTCPeerConnection){this.RTCPeerConnection=options.RTCPeerConnection}else if(util.isLegacyEdge()){this.RTCPeerConnection=new RTCPeerConnectionShim(typeof window!=="undefined"?window:global)}else if(typeof window.RTCPeerConnection==="function"){this.RTCPeerConnection=window.RTCPeerConnection}else if(typeof window.webkitRTCPeerConnection==="function"){this.RTCPeerConnection=webkitRTCPeerConnection}else if(typeof window.mozRTCPeerConnection==="function"){this.RTCPeerConnection=mozRTCPeerConnection;window.RTCSessionDescription=mozRTCSessionDescription;window.RTCIceCandidate=mozRTCIceCandidate}else{this.log.info("No RTCPeerConnection implementation available")}}RTCPC.prototype.create=function(rtcConstraints,rtcConfiguration){this.log=new log_1.default("RTCPC");this.pc=new this.RTCPeerConnection(rtcConfiguration,rtcConstraints)};RTCPC.prototype.createModernConstraints=function(c){if(typeof c==="undefined"){return null}var nc=Object.assign({},c);if(typeof webkitRTCPeerConnection!=="undefined"&&!util.isLegacyEdge()){nc.mandatory={};if(typeof c.audio!=="undefined"){nc.mandatory.OfferToReceiveAudio=c.audio}if(typeof c.video!=="undefined"){nc.mandatory.OfferToReceiveVideo=c.video}}else{if(typeof c.audio!=="undefined"){nc.offerToReceiveAudio=c.audio}if(typeof c.video!=="undefined"){nc.offerToReceiveVideo=c.video}}delete nc.audio;delete nc.video;return nc};RTCPC.prototype.createOffer=function(maxAverageBitrate,codecPreferences,constraints,onSuccess,onError){var _this=this;constraints=this.createModernConstraints(constraints);return promisifyCreate(this.pc.createOffer,this.pc)(constraints).then(function(offer){if(!_this.pc){return Promise.resolve()}var sdp=sdp_1.setMaxAverageBitrate(offer.sdp,maxAverageBitrate);return promisifySet(_this.pc.setLocalDescription,_this.pc)(new RTCSessionDescription({sdp:sdp_1.setCodecPreferences(sdp,codecPreferences),type:"offer"}))}).then(onSuccess,onError)};RTCPC.prototype.createAnswer=function(maxAverageBitrate,codecPreferences,constraints,onSuccess,onError){var _this=this;constraints=this.createModernConstraints(constraints);return promisifyCreate(this.pc.createAnswer,this.pc)(constraints).then(function(answer){if(!_this.pc){return Promise.resolve()}var sdp=sdp_1.setMaxAverageBitrate(answer.sdp,maxAverageBitrate);return promisifySet(_this.pc.setLocalDescription,_this.pc)(new RTCSessionDescription({sdp:sdp_1.setCodecPreferences(sdp,codecPreferences),type:"answer"}))}).then(onSuccess,onError)};RTCPC.prototype.processSDP=function(maxAverageBitrate,codecPreferences,sdp,constraints,onSuccess,onError){var _this=this;sdp=sdp_1.setCodecPreferences(sdp,codecPreferences);var desc=new RTCSessionDescription({sdp:sdp,type:"offer"});return promisifySet(this.pc.setRemoteDescription,this.pc)(desc).then(function(){_this.createAnswer(maxAverageBitrate,codecPreferences,constraints,onSuccess,onError)})};RTCPC.prototype.getSDP=function(){return this.pc.localDescription.sdp};RTCPC.prototype.processAnswer=function(codecPreferences,sdp,onSuccess,onError){if(!this.pc){return Promise.resolve()}sdp=sdp_1.setCodecPreferences(sdp,codecPreferences);return promisifySet(this.pc.setRemoteDescription,this.pc)(new RTCSessionDescription({sdp:sdp,type:"answer"})).then(onSuccess,onError)};RTCPC.test=function(){if(typeof navigator==="object"){var getUserMedia=navigator.mediaDevices&&navigator.mediaDevices.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.getUserMedia;if(util.isLegacyEdge(navigator)){return false}if(getUserMedia&&typeof window.RTCPeerConnection==="function"){return true}else if(getUserMedia&&typeof window.webkitRTCPeerConnection==="function"){return true}else if(getUserMedia&&typeof window.mozRTCPeerConnection==="function"){try{var test_1=new window.mozRTCPeerConnection;if(typeof test_1.getLocalStreams!=="function"){return false}}catch(e){return false}return true}else if(typeof RTCIceGatherer!=="undefined"){return true}}return false};function promisify(fn,ctx,areCallbacksFirst,checkRval){return function(){var args=Array.prototype.slice.call(arguments);return new Promise(function(resolve){var returnValue=fn.apply(ctx,args);if(!checkRval){resolve(returnValue);return}if(typeof returnValue==="object"&&typeof returnValue.then==="function"){resolve(returnValue)}else{throw new Error}}).catch(function(){return new Promise(function(resolve,reject){fn.apply(ctx,areCallbacksFirst?[resolve,reject].concat(args):args.concat([resolve,reject]))})})}}function promisifyCreate(fn,ctx){return promisify(fn,ctx,true,true)}function promisifySet(fn,ctx){return promisify(fn,ctx,false,false)}exports.default=RTCPC}).call(this)}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{"../log":18,"../util":36,"./sdp":31,"rtcpeerconnection-shim":45}],31:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});exports.setMaxAverageBitrate=exports.setIceAggressiveNomination=exports.setCodecPreferences=exports.getPreferredCodecInfo=void 0;var util=require("../util");var ptToFixedBitrateAudioCodecName={0:"PCMU",8:"PCMA"};var defaultOpusId=111;var BITRATE_MAX=51e4;var BITRATE_MIN=6e3;function getPreferredCodecInfo(sdp){var _a=/a=rtpmap:(\d+) (\S+)/m.exec(sdp)||[null,"",""],codecId=_a[1],codecName=_a[2];var regex=new RegExp("a=fmtp:"+codecId+" (\\S+)","m");var _b=regex.exec(sdp)||[null,""],codecParams=_b[1];return{codecName:codecName,codecParams:codecParams}}exports.getPreferredCodecInfo=getPreferredCodecInfo;function setIceAggressiveNomination(sdp){if(!util.isChrome(window,window.navigator)){return sdp}return sdp.split("\n").filter(function(line){return line.indexOf("a=ice-lite")===-1}).join("\n")}exports.setIceAggressiveNomination=setIceAggressiveNomination;function setMaxAverageBitrate(sdp,maxAverageBitrate){if(typeof maxAverageBitrate!=="number"||maxAverageBitrate<BITRATE_MIN||maxAverageBitrate>BITRATE_MAX){return sdp}var matches=/a=rtpmap:(\d+) opus/m.exec(sdp);var opusId=matches&&matches.length?matches[1]:defaultOpusId;var regex=new RegExp("a=fmtp:"+opusId);var lines=sdp.split("\n").map(function(line){return regex.test(line)?line+(";maxaveragebitrate="+maxAverageBitrate):line});return lines.join("\n")}exports.setMaxAverageBitrate=setMaxAverageBitrate;function setCodecPreferences(sdp,preferredCodecs){var mediaSections=getMediaSections(sdp);var session=sdp.split("\r\nm=")[0];return[session].concat(mediaSections.map(function(section){if(!/^m=(audio|video)/.test(section)){return section}var kind=section.match(/^m=(audio|video)/)[1];var codecMap=createCodecMapForMediaSection(section);var payloadTypes=getReorderedPayloadTypes(codecMap,preferredCodecs);var newSection=setPayloadTypesInMediaSection(payloadTypes,section);var pcmaPayloadTypes=codecMap.get("pcma")||[];var pcmuPayloadTypes=codecMap.get("pcmu")||[];var fixedBitratePayloadTypes=kind==="audio"?new Set(pcmaPayloadTypes.concat(pcmuPayloadTypes)):new Set;return fixedBitratePayloadTypes.has(payloadTypes[0])?newSection.replace(/\r\nb=(AS|TIAS):([0-9]+)/g,""):newSection})).join("\r\n")}exports.setCodecPreferences=setCodecPreferences;function getMediaSections(sdp,kind,direction){return sdp.replace(/\r\n\r\n$/,"\r\n").split("\r\nm=").slice(1).map(function(mediaSection){return"m="+mediaSection}).filter(function(mediaSection){var kindPattern=new RegExp("m="+(kind||".*"),"gm");var directionPattern=new RegExp("a="+(direction||".*"),"gm");return kindPattern.test(mediaSection)&&directionPattern.test(mediaSection)})}function createCodecMapForMediaSection(section){return Array.from(createPtToCodecName(section)).reduce(function(codecMap,pair){var pt=pair[0];var codecName=pair[1];var pts=codecMap.get(codecName)||[];return codecMap.set(codecName,pts.concat(pt))},new Map)}function getReorderedPayloadTypes(codecMap,preferredCodecs){preferredCodecs=preferredCodecs.map(function(codecName){return codecName.toLowerCase()});var preferredPayloadTypes=util.flatMap(preferredCodecs,function(codecName){return codecMap.get(codecName)||[]});var remainingCodecs=util.difference(Array.from(codecMap.keys()),preferredCodecs);var remainingPayloadTypes=util.flatMap(remainingCodecs,function(codecName){return codecMap.get(codecName)});return preferredPayloadTypes.concat(remainingPayloadTypes)}function setPayloadTypesInMediaSection(payloadTypes,section){var lines=section.split("\r\n");var mLine=lines[0];var otherLines=lines.slice(1);mLine=mLine.replace(/([0-9]+\s?)+$/,payloadTypes.join(" "));return[mLine].concat(otherLines).join("\r\n")}function createPtToCodecName(mediaSection){return getPayloadTypesInMediaSection(mediaSection).reduce(function(ptToCodecName,pt){var rtpmapPattern=new RegExp("a=rtpmap:"+pt+" ([^/]+)");var matches=mediaSection.match(rtpmapPattern);var codecName=matches?matches[1].toLowerCase():ptToFixedBitrateAudioCodecName[pt]?ptToFixedBitrateAudioCodecName[pt].toLowerCase():"";return ptToCodecName.set(pt,codecName)},new Map)}function getPayloadTypesInMediaSection(section){var mLine=section.split("\r\n")[0];var matches=mLine.match(/([0-9]+)/g);if(!matches){return[]}return matches.slice(1).map(function(match){return parseInt(match,10)})}},{"../util":36}],32:[function(require,module,exports){"use strict";var __spreadArrays=this&&this.__spreadArrays||function(){for(var s=0,i=0,il=arguments.length;i<il;i++)s+=arguments[i].length;for(var r=Array(s),k=0,i=0;i<il;i++)for(var a=arguments[i],j=0,jl=a.length;j<jl;j++,k++)r[k]=a[j];return r};Object.defineProperty(exports,"__esModule",{value:true});exports.getRTCIceCandidateStatsReport=exports.getRTCStats=void 0;var errors_1=require("../errors");var mockrtcstatsreport_1=require("./mockrtcstatsreport");var ERROR_PEER_CONNECTION_NULL="PeerConnection is null";var ERROR_WEB_RTC_UNSUPPORTED="WebRTC statistics are unsupported";function findStatById(report,id){if(typeof report.get==="function"){return report.get(id)}return report.find(function(s){return s.id===id})}function getRTCStatsReport(peerConnection){if(!peerConnection){return Promise.reject(new errors_1.InvalidArgumentError(ERROR_PEER_CONNECTION_NULL))}if(typeof peerConnection.getStats!=="function"){return Promise.reject(new errors_1.NotSupportedError(ERROR_WEB_RTC_UNSUPPORTED))}var promise;try{promise=peerConnection.getStats()}catch(e){promise=new Promise(function(resolve){return peerConnection.getStats(resolve)}).then(mockrtcstatsreport_1.default.fromRTCStatsResponse)}return promise}function getRTCStats(peerConnection,options){options=Object.assign({createRTCSample:createRTCSample},options);return getRTCStatsReport(peerConnection).then(options.createRTCSample)}exports.getRTCStats=getRTCStats;function getRTCIceCandidateStatsReport(peerConnection){return getRTCStatsReport(peerConnection).then(function(report){var _a=Array.from(report.values()).reduce(function(rval,stat){["candidatePairs","localCandidates","remoteCandidates"].forEach(function(prop){if(!rval[prop]){rval[prop]=[]}});switch(stat.type){case"candidate-pair":rval.candidatePairs.push(stat);break;case"local-candidate":rval.localCandidates.push(stat);break;case"remote-candidate":rval.remoteCandidates.push(stat);break;case"transport":if(stat.selectedCandidatePairId){rval.transport=stat}break}return rval},{}),candidatePairs=_a.candidatePairs,localCandidates=_a.localCandidates,remoteCandidates=_a.remoteCandidates,transport=_a.transport;var selectedCandidatePairReport=candidatePairs.find(function(pair){return pair.selected||transport&&pair.id===transport.selectedCandidatePairId});var selectedIceCandidatePairStats;if(selectedCandidatePairReport){selectedIceCandidatePairStats={localCandidate:localCandidates.find(function(candidate){return candidate.id===selectedCandidatePairReport.localCandidateId}),remoteCandidate:remoteCandidates.find(function(candidate){return candidate.id===selectedCandidatePairReport.remoteCandidateId})}}return{iceCandidateStats:__spreadArrays(localCandidates,remoteCandidates),selectedIceCandidatePairStats:selectedIceCandidatePairStats}})}exports.getRTCIceCandidateStatsReport=getRTCIceCandidateStatsReport;function RTCSample(){}function createRTCSample(statsReport){var activeTransportId=null;var sample=new RTCSample;var fallbackTimestamp;Array.from(statsReport.values()).forEach(function(stats){if(stats.isRemote){return}var type=stats.type.replace("-","");fallbackTimestamp=fallbackTimestamp||stats.timestamp;if(stats.remoteId){var remote=findStatById(statsReport,stats.remoteId);if(remote&&remote.roundTripTime){sample.rtt=remote.roundTripTime*1e3}}switch(type){case"inboundrtp":sample.timestamp=sample.timestamp||stats.timestamp;sample.jitter=stats.jitter*1e3;sample.packetsLost=stats.packetsLost;sample.packetsReceived=stats.packetsReceived;sample.bytesReceived=stats.bytesReceived;break;case"outboundrtp":sample.timestamp=stats.timestamp;sample.packetsSent=stats.packetsSent;sample.bytesSent=stats.bytesSent;if(stats.codecId){var codec=findStatById(statsReport,stats.codecId);sample.codecName=codec?codec.mimeType&&codec.mimeType.match(/(.*\/)?(.*)/)[2]:stats.codecId}break;case"transport":activeTransportId=stats.id;break}});if(!sample.timestamp){sample.timestamp=fallbackTimestamp}var activeTransport=findStatById(statsReport,activeTransportId);if(!activeTransport){return sample}var selectedCandidatePair=findStatById(statsReport,activeTransport.selectedCandidatePairId);if(!selectedCandidatePair){return sample}var localCandidate=findStatById(statsReport,selectedCandidatePair.localCandidateId);var remoteCandidate=findStatById(statsReport,selectedCandidatePair.remoteCandidateId);if(!sample.rtt){sample.rtt=selectedCandidatePair&&selectedCandidatePair.currentRoundTripTime*1e3}Object.assign(sample,{localAddress:localCandidate&&(localCandidate.address||localCandidate.ip),remoteAddress:remoteCandidate&&(remoteCandidate.address||remoteCandidate.ip)});return sample}},{"../errors":15,"./mockrtcstatsreport":27}],33:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var MediaDeviceInfoShim=function(){function MediaDeviceInfoShim(options){Object.defineProperties(this,{deviceId:{get:function(){return options.deviceId}},groupId:{get:function(){return options.groupId}},kind:{get:function(){return options.kind}},label:{get:function(){return options.label}}})}return MediaDeviceInfoShim}();exports.default=MediaDeviceInfoShim},{}],34:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var asyncQueue_1=require("./asyncQueue");var audioplayer_1=require("./audioplayer/audioplayer");var errors_1=require("./errors");function Sound(name,url,options){if(!(this instanceof Sound)){return new Sound(name,url,options)}if(!name||!url){throw new errors_1.InvalidArgumentError("name and url are required arguments")}options=Object.assign({AudioFactory:typeof Audio!=="undefined"?Audio:null,maxDuration:0,shouldLoop:false},options);options.AudioPlayer=options.audioContext?audioplayer_1.default.bind(audioplayer_1.default,options.audioContext):options.AudioFactory;Object.defineProperties(this,{_Audio:{value:options.AudioPlayer},_activeEls:{value:new Map},_isSinkSupported:{value:options.AudioFactory!==null&&typeof options.AudioFactory.prototype.setSinkId==="function"},_maxDuration:{value:options.maxDuration},_maxDurationTimeout:{value:null,writable:true},_operations:{value:new asyncQueue_1.AsyncQueue},_playPromise:{value:null,writable:true},_shouldLoop:{value:options.shouldLoop},_sinkIds:{value:["default"]},isPlaying:{enumerable:true,get:function(){return!!this._playPromise}},name:{enumerable:true,value:name},url:{enumerable:true,value:url}});if(this._Audio){this._play(true,false)}}function destroyAudioElement(audioElement){if(audioElement){audioElement.pause();audioElement.src="";audioElement.srcObject=null;audioElement.load()}}Sound.prototype._playAudioElement=function _playAudioElement(sinkId,isMuted,shouldLoop){var _this=this;var audioElement=this._activeEls.get(sinkId);if(!audioElement){throw new errors_1.InvalidArgumentError('sinkId: "'+sinkId+"\" doesn't have an audio element")}audioElement.muted=!!isMuted;audioElement.loop=!!shouldLoop;return audioElement.play().then(function(){return audioElement}).catch(function(reason){destroyAudioElement(audioElement);_this._activeEls.delete(sinkId);throw reason})};Sound.prototype._play=function _play(forceIsMuted,forceShouldLoop){if(this.isPlaying){this._stop()}if(this._maxDuration>0){this._maxDurationTimeout=setTimeout(this._stop.bind(this),this._maxDuration)}forceShouldLoop=typeof forceShouldLoop==="boolean"?forceShouldLoop:this._shouldLoop;var self=this;var playPromise=this._playPromise=Promise.all(this._sinkIds.map(function createAudioElement(sinkId){if(!self._Audio){return Promise.resolve()}var audioElement=self._activeEls.get(sinkId);if(audioElement){return self._playAudioElement(sinkId,forceIsMuted,forceShouldLoop)}audioElement=new self._Audio(self.url);if(typeof audioElement.setAttribute==="function"){audioElement.setAttribute("crossorigin","anonymous")}return new Promise(function(resolve){audioElement.addEventListener("canplaythrough",resolve)}).then(function(){return(self._isSinkSupported?audioElement.setSinkId(sinkId):Promise.resolve()).then(function setSinkIdSuccess(){self._activeEls.set(sinkId,audioElement);if(!self._playPromise){return Promise.resolve()}return self._playAudioElement(sinkId,forceIsMuted,forceShouldLoop)})})}));return playPromise};Sound.prototype._stop=function _stop(){var _this=this;this._activeEls.forEach(function(audioEl,sinkId){if(_this._sinkIds.includes(sinkId)){audioEl.pause();audioEl.currentTime=0}else{destroyAudioElement(audioEl);_this._activeEls.delete(sinkId)}});clearTimeout(this._maxDurationTimeout);this._playPromise=null;this._maxDurationTimeout=null};Sound.prototype.setSinkIds=function setSinkIds(ids){if(!this._isSinkSupported){return}ids=ids.forEach?ids:[ids];[].splice.apply(this._sinkIds,[0,this._sinkIds.length].concat(ids))};Sound.prototype.stop=function stop(){var _this=this;this._operations.enqueue(function(){_this._stop();return Promise.resolve()})};Sound.prototype.play=function play(){var _this=this;return this._operations.enqueue(function(){return _this._play()})};exports.default=Sound},{"./asyncQueue":2,"./audioplayer/audioplayer":4,"./errors":15}],35:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();var __assign=this&&this.__assign||function(){__assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)if(Object.prototype.hasOwnProperty.call(s,p))t[p]=s[p]}return t};return __assign.apply(this,arguments)};var __spreadArrays=this&&this.__spreadArrays||function(){for(var s=0,i=0,il=arguments.length;i<il;i++)s+=arguments[i].length;for(var r=Array(s),k=0,i=0;i<il;i++)for(var a=arguments[i],j=0,jl=a.length;j<jl;j++,k++)r[k]=a[j];return r};Object.defineProperty(exports,"__esModule",{value:true});var events_1=require("events");var errors_1=require("./errors");var mos_1=require("./rtc/mos");var stats_1=require("./rtc/stats");var util_1=require("./util");var SAMPLE_COUNT_METRICS=5;var SAMPLE_COUNT_CLEAR=0;var SAMPLE_COUNT_RAISE=3;var SAMPLE_INTERVAL=1e3;var WARNING_TIMEOUT=5*1e3;var DEFAULT_THRESHOLDS={audioInputLevel:{minStandardDeviation:327.67,sampleCount:10},audioOutputLevel:{minStandardDeviation:327.67,sampleCount:10},bytesReceived:{clearCount:2,min:1,raiseCount:3,sampleCount:3},bytesSent:{clearCount:2,min:1,raiseCount:3,sampleCount:3},jitter:{max:30},mos:{min:3},packetsLostFraction:[{max:1},{clearValue:1,maxAverage:3,sampleCount:7}],rtt:{max:400}};function countHigh(max,values){return values.reduce(function(highCount,value){return highCount+=value>max?1:0},0)}function countLow(min,values){return values.reduce(function(lowCount,value){return lowCount+=value<min?1:0},0)}function calculateStandardDeviation(values){if(values.length<=0){return null}var valueAverage=values.reduce(function(partialSum,value){return partialSum+value},0)/values.length;var diffSquared=values.map(function(value){return Math.pow(value-valueAverage,2)});var stdDev=Math.sqrt(diffSquared.reduce(function(partialSum,value){return partialSum+value},0)/diffSquared.length);return stdDev}function flattenSamples(sampleSets){return sampleSets.reduce(function(flat,current){return __spreadArrays(flat,current)},[])}var StatsMonitor=function(_super){__extends(StatsMonitor,_super);function StatsMonitor(options){var _this=_super.call(this)||this;_this._activeWarnings=new Map;_this._currentStreaks=new Map;_this._inputVolumes=[];_this._outputVolumes=[];_this._sampleBuffer=[];_this._supplementalSampleBuffers={audioInputLevel:[],audioOutputLevel:[]};_this._warningsEnabled=true;options=options||{};_this._getRTCStats=options.getRTCStats||stats_1.getRTCStats;_this._mos=options.Mos||mos_1.default;_this._peerConnection=options.peerConnection;_this._thresholds=__assign(__assign({},DEFAULT_THRESHOLDS),options.thresholds);var thresholdSampleCounts=Object.values(_this._thresholds).map(function(threshold){return threshold.sampleCount}).filter(function(sampleCount){return!!sampleCount});_this._maxSampleCount=Math.max.apply(Math,__spreadArrays([SAMPLE_COUNT_METRICS],thresholdSampleCounts));if(_this._peerConnection){_this.enable(_this._peerConnection)}return _this}StatsMonitor.prototype.addVolumes=function(inputVolume,outputVolume){this._inputVolumes.push(inputVolume);this._outputVolumes.push(outputVolume)};StatsMonitor.prototype.disable=function(){if(this._sampleInterval){clearInterval(this._sampleInterval);delete this._sampleInterval}return this};StatsMonitor.prototype.disableWarnings=function(){if(this._warningsEnabled){this._activeWarnings.clear()}this._warningsEnabled=false;return this};StatsMonitor.prototype.enable=function(peerConnection){if(peerConnection){if(this._peerConnection&&peerConnection!==this._peerConnection){throw new errors_1.InvalidArgumentError("Attempted to replace an existing PeerConnection in StatsMonitor.enable")}this._peerConnection=peerConnection}if(!this._peerConnection){throw new errors_1.InvalidArgumentError("Can not enable StatsMonitor without a PeerConnection")}this._sampleInterval=this._sampleInterval||setInterval(this._fetchSample.bind(this),SAMPLE_INTERVAL);return this};StatsMonitor.prototype.enableWarnings=function(){this._warningsEnabled=true;return this};StatsMonitor.prototype.hasActiveWarning=function(statName,thresholdName){var warningId=statName+":"+thresholdName;return!!this._activeWarnings.get(warningId)};StatsMonitor.prototype._addSample=function(sample){var samples=this._sampleBuffer;samples.push(sample);if(samples.length>this._maxSampleCount){samples.splice(0,samples.length-this._maxSampleCount)}};StatsMonitor.prototype._clearWarning=function(statName,thresholdName,data){var warningId=statName+":"+thresholdName;var activeWarning=this._activeWarnings.get(warningId);if(!activeWarning||Date.now()-activeWarning.timeRaised<WARNING_TIMEOUT){return}this._activeWarnings.delete(warningId);this.emit("warning-cleared",__assign(__assign({},data),{name:statName,threshold:{name:thresholdName,value:this._thresholds[statName][thresholdName]}}))};StatsMonitor.prototype._createSample=function(stats,previousSample){var previousBytesSent=previousSample&&previousSample.totals.bytesSent||0;var previousBytesReceived=previousSample&&previousSample.totals.bytesReceived||0;var previousPacketsSent=previousSample&&previousSample.totals.packetsSent||0;var previousPacketsReceived=previousSample&&previousSample.totals.packetsReceived||0;var previousPacketsLost=previousSample&&previousSample.totals.packetsLost||0;var currentBytesSent=stats.bytesSent-previousBytesSent;var currentBytesReceived=stats.bytesReceived-previousBytesReceived;var currentPacketsSent=stats.packetsSent-previousPacketsSent;var currentPacketsReceived=stats.packetsReceived-previousPacketsReceived;var currentPacketsLost=stats.packetsLost-previousPacketsLost;var currentInboundPackets=currentPacketsReceived+currentPacketsLost;var currentPacketsLostFraction=currentInboundPackets>0?currentPacketsLost/currentInboundPackets*100:0;var totalInboundPackets=stats.packetsReceived+stats.packetsLost;var totalPacketsLostFraction=totalInboundPackets>0?stats.packetsLost/totalInboundPackets*100:100;var rttValue=typeof stats.rtt==="number"||!previousSample?stats.rtt:previousSample.rtt;var audioInputLevelValues=this._inputVolumes.splice(0);this._supplementalSampleBuffers.audioInputLevel.push(audioInputLevelValues);var audioOutputLevelValues=this._outputVolumes.splice(0);this._supplementalSampleBuffers.audioOutputLevel.push(audioOutputLevelValues);return{audioInputLevel:Math.round(util_1.average(audioInputLevelValues)),audioOutputLevel:Math.round(util_1.average(audioOutputLevelValues)),bytesReceived:currentBytesReceived,bytesSent:currentBytesSent,codecName:stats.codecName,jitter:stats.jitter,mos:this._mos.calculate(rttValue,stats.jitter,previousSample&&currentPacketsLostFraction),packetsLost:currentPacketsLost,packetsLostFraction:currentPacketsLostFraction,packetsReceived:currentPacketsReceived,packetsSent:currentPacketsSent,rtt:rttValue,timestamp:stats.timestamp,totals:{bytesReceived:stats.bytesReceived,bytesSent:stats.bytesSent,packetsLost:stats.packetsLost,packetsLostFraction:totalPacketsLostFraction,packetsReceived:stats.packetsReceived,packetsSent:stats.packetsSent}}};StatsMonitor.prototype._fetchSample=function(){var _this=this;this._getSample().then(function(sample){_this._addSample(sample);_this._raiseWarnings();_this.emit("sample",sample)}).catch(function(error){_this.disable();_this.emit("error",error)})};StatsMonitor.prototype._getSample=function(){var _this=this;return this._getRTCStats(this._peerConnection).then(function(stats){var previousSample=null;if(_this._sampleBuffer.length){previousSample=_this._sampleBuffer[_this._sampleBuffer.length-1]}return _this._createSample(stats,previousSample)})};StatsMonitor.prototype._raiseWarning=function(statName,thresholdName,data){var warningId=statName+":"+thresholdName;if(this._activeWarnings.has(warningId)){return}this._activeWarnings.set(warningId,{timeRaised:Date.now()});var thresholds=this._thresholds[statName];var thresholdValue;if(Array.isArray(thresholds)){var foundThreshold=thresholds.find(function(threshold){return thresholdName in threshold});if(foundThreshold){thresholdValue=foundThreshold[thresholdName]}}else{thresholdValue=this._thresholds[statName][thresholdName]}this.emit("warning",__assign(__assign({},data),{name:statName,threshold:{name:thresholdName,value:thresholdValue}}))};StatsMonitor.prototype._raiseWarnings=function(){var _this=this;if(!this._warningsEnabled){return}Object.keys(this._thresholds).forEach(function(name){return _this._raiseWarningsForStat(name)})};StatsMonitor.prototype._raiseWarningsForStat=function(statName){var _this=this;var limits=Array.isArray(this._thresholds[statName])?this._thresholds[statName]:[this._thresholds[statName]];limits.forEach(function(limit){var samples=_this._sampleBuffer;var clearCount=limit.clearCount||SAMPLE_COUNT_CLEAR;var raiseCount=limit.raiseCount||SAMPLE_COUNT_RAISE;var sampleCount=limit.sampleCount||_this._maxSampleCount;var relevantSamples=samples.slice(-sampleCount);var values=relevantSamples.map(function(sample){return sample[statName]});var containsNull=values.some(function(value){return typeof value==="undefined"||value===null});if(containsNull){return}var count;if(typeof limit.max==="number"){count=countHigh(limit.max,values);if(count>=raiseCount){_this._raiseWarning(statName,"max",{values:values,samples:relevantSamples})}else if(count<=clearCount){_this._clearWarning(statName,"max",{values:values,samples:relevantSamples})}}if(typeof limit.min==="number"){count=countLow(limit.min,values);if(count>=raiseCount){_this._raiseWarning(statName,"min",{values:values,samples:relevantSamples})}else if(count<=clearCount){_this._clearWarning(statName,"min",{values:values,samples:relevantSamples})}}if(typeof limit.maxDuration==="number"&&samples.length>1){relevantSamples=samples.slice(-2);var prevValue=relevantSamples[0][statName];var curValue=relevantSamples[1][statName];var prevStreak=_this._currentStreaks.get(statName)||0;var streak=prevValue===curValue?prevStreak+1:0;_this._currentStreaks.set(statName,streak);if(streak>=limit.maxDuration){_this._raiseWarning(statName,"maxDuration",{value:streak})}else if(streak===0){_this._clearWarning(statName,"maxDuration",{value:prevStreak})}}if(typeof limit.minStandardDeviation==="number"){var sampleSets=_this._supplementalSampleBuffers[statName];if(!sampleSets||sampleSets.length<limit.sampleCount){return}if(sampleSets.length>limit.sampleCount){sampleSets.splice(0,sampleSets.length-limit.sampleCount)}var flatSamples=flattenSamples(sampleSets.slice(-sampleCount));var stdDev=calculateStandardDeviation(flatSamples);if(typeof stdDev!=="number"){return}if(stdDev<limit.minStandardDeviation){_this._raiseWarning(statName,"minStandardDeviation",{value:stdDev})}else{_this._clearWarning(statName,"minStandardDeviation",{value:stdDev})}}[["maxAverage",function(x,y){return x>y}],["minAverage",function(x,y){return x<y}]].forEach(function(_a){var thresholdName=_a[0],comparator=_a[1];if(typeof limit[thresholdName]==="number"&&values.length>=sampleCount){var avg=util_1.average(values);if(comparator(avg,limit[thresholdName])){_this._raiseWarning(statName,thresholdName,{values:values,samples:relevantSamples})}else if(!comparator(avg,limit.clearValue||limit[thresholdName])){_this._clearWarning(statName,thresholdName,{values:values,samples:relevantSamples})}}})})};return StatsMonitor}(events_1.EventEmitter);exports.default=StatsMonitor},{"./errors":15,"./rtc/mos":28,"./rtc/stats":32,"./util":36,events:39}],36:[function(require,module,exports){(function(global){(function(){"use strict";Object.defineProperty(exports,"__esModule",{value:true});exports.promisifyEvents=exports.flatMap=exports.queryToJson=exports.isUnifiedPlanDefault=exports.isSafari=exports.isLegacyEdge=exports.isFirefox=exports.isChrome=exports.isElectron=exports.difference=exports.average=exports.Exception=void 0;function TwilioException(message){if(!(this instanceof TwilioException)){return new TwilioException(message)}this.message=message}TwilioException.prototype.toString=function(){return"Twilio.Exception: "+this.message};function average(values){return values&&values.length?values.reduce(function(t,v){return t+v})/values.length:0}exports.average=average;function difference(lefts,rights,getKey){getKey=getKey||function(a){return a};var rightKeys=new Set(rights.map(getKey));return lefts.filter(function(left){return!rightKeys.has(getKey(left))})}exports.difference=difference;function isElectron(navigator){return!!navigator.userAgent.match("Electron")}exports.isElectron=isElectron;function isChrome(window,navigator){var isCriOS=!!navigator.userAgent.match("CriOS");var isHeadlessChrome=!!navigator.userAgent.match("HeadlessChrome");var isGoogle=typeof window.chrome!=="undefined"&&navigator.vendor==="Google Inc."&&navigator.userAgent.indexOf("OPR")===-1&&navigator.userAgent.indexOf("Edge")===-1;return isCriOS||isElectron(navigator)||isGoogle||isHeadlessChrome}exports.isChrome=isChrome;function isFirefox(navigator){navigator=navigator||(typeof window==="undefined"?global.navigator:window.navigator);return!!navigator&&typeof navigator.userAgent==="string"&&/firefox|fxios/i.test(navigator.userAgent)}exports.isFirefox=isFirefox;function isLegacyEdge(navigator){navigator=navigator||(typeof window==="undefined"?global.navigator:window.navigator);return!!navigator&&typeof navigator.userAgent==="string"&&/edge\/\d+/i.test(navigator.userAgent)}exports.isLegacyEdge=isLegacyEdge;function isSafari(navigator){return!!navigator.vendor&&navigator.vendor.indexOf("Apple")!==-1&&navigator.userAgent&&navigator.userAgent.indexOf("CriOS")===-1&&navigator.userAgent.indexOf("FxiOS")===-1}exports.isSafari=isSafari;function isUnifiedPlanDefault(window,navigator,PeerConnection,RtpTransceiver){if(typeof window==="undefined"||typeof navigator==="undefined"||typeof PeerConnection==="undefined"||typeof RtpTransceiver==="undefined"||typeof PeerConnection.prototype==="undefined"||typeof RtpTransceiver.prototype==="undefined"){return false}if(isChrome(window,navigator)&&PeerConnection.prototype.addTransceiver){var pc=new PeerConnection;var isUnifiedPlan=true;try{pc.addTransceiver("audio")}catch(e){isUnifiedPlan=false}pc.close();return isUnifiedPlan}else if(isFirefox(navigator)){return true}else if(isSafari(navigator)){return"currentDirection"in RtpTransceiver.prototype}return false}exports.isUnifiedPlanDefault=isUnifiedPlanDefault;function queryToJson(params){if(!params){return""}return params.split("&").reduce(function(output,pair){var parts=pair.split("=");var key=parts[0];var value=decodeURIComponent((parts[1]||"").replace(/\+/g,"%20"));if(key){output[key]=value}return output},{})}exports.queryToJson=queryToJson;function flatMap(list,mapFn){var listArray=list instanceof Map||list instanceof Set?Array.from(list.values()):list;mapFn=mapFn||function(item){return item};return listArray.reduce(function(flattened,item){var mapped=mapFn(item);return flattened.concat(mapped)},[])}exports.flatMap=flatMap;function promisifyEvents(emitter,resolveEventName,rejectEventName){return new Promise(function(resolve,reject){function resolveHandler(){emitter.removeListener(rejectEventName,rejectHandler);resolve()}function rejectHandler(){emitter.removeListener(resolveEventName,resolveHandler);reject()}emitter.once(resolveEventName,resolveHandler);emitter.once(rejectEventName,rejectHandler)})}exports.promisifyEvents=promisifyEvents;var Exception=TwilioException;exports.Exception=Exception}).call(this)}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{}],37:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});exports.generateVoiceEventSid=void 0;var md5Lib=require("md5");var errors_1=require("../twilio/errors");var md5=typeof md5Lib==="function"?md5Lib:md5Lib.default;function generateUuid(){if(typeof window!=="object"){throw new errors_1.NotSupportedError("This platform is not supported.")}var crypto=window.crypto;if(typeof crypto!=="object"){throw new errors_1.NotSupportedError("The `crypto` module is not available on this platform.")}if(typeof(crypto.randomUUID||crypto.getRandomValues)==="undefined"){throw new errors_1.NotSupportedError("Neither `crypto.randomUUID` or `crypto.getRandomValues` are available "+"on this platform.")}var uInt32Arr=window.Uint32Array;if(typeof uInt32Arr==="undefined"){throw new errors_1.NotSupportedError("The `Uint32Array` module is not available on this platform.")}var generateRandomValues=typeof crypto.randomUUID==="function"?function(){return crypto.randomUUID()}:function(){return crypto.getRandomValues(new Uint32Array(32)).toString()};return md5(generateRandomValues())}function generateVoiceEventSid(){return"KX"+generateUuid()}exports.generateVoiceEventSid=generateVoiceEventSid},{"../twilio/errors":15,md5:44}],38:[function(require,module,exports){"use strict";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};return function(d,b){extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}}();var __assign=this&&this.__assign||function(){__assign=Object.assign||function(t){for(var s,i=1,n=arguments.length;i<n;i++){s=arguments[i];for(var p in s)if(Object.prototype.hasOwnProperty.call(s,p))t[p]=s[p]}return t};return __assign.apply(this,arguments)};Object.defineProperty(exports,"__esModule",{value:true});exports.WSTransportState=void 0;var events_1=require("events");var backoff_1=require("./backoff");var errors_1=require("./errors");var log_1=require("./log");var WebSocket=globalThis.WebSocket;var CONNECT_SUCCESS_TIMEOUT=1e4;var CONNECT_TIMEOUT=5e3;var HEARTBEAT_TIMEOUT=15e3;var MAX_PREFERRED_DURATION=15e3;var MAX_PRIMARY_DURATION=Infinity;var MAX_PREFERRED_DELAY=1e3;var MAX_PRIMARY_DELAY=2e4;var WSTransportState;(function(WSTransportState){WSTransportState["Connecting"]="connecting";WSTransportState["Closed"]="closed";WSTransportState["Open"]="open"})(WSTransportState=exports.WSTransportState||(exports.WSTransportState={}));var WSTransport=function(_super){__extends(WSTransport,_super);function WSTransport(uris,options){if(options===void 0){options={}}var _this=_super.call(this)||this;_this.state=WSTransportState.Closed;_this._backoffStartTime={preferred:null,primary:null};_this._connectedUri=null;_this._log=new log_1.default("WSTransport");_this._shouldFallback=false;_this._uriIndex=0;_this._moveUriIndex=function(){_this._uriIndex++;if(_this._uriIndex>=_this._uris.length){_this._uriIndex=0}};_this._onSocketClose=function(event){_this._log.error("Received websocket close event code: "+event.code+". Reason: "+event.reason);if(event.code===1006||event.code===1015){_this.emit("error",{code:31005,message:event.reason||"Websocket connection to Twilio's signaling servers were "+"unexpectedly ended. If this is happening consistently, there may "+"be an issue resolving the hostname provided. If a region or an "+"edge is being specified in Device setup, ensure it is valid.",twilioError:new errors_1.SignalingErrors.ConnectionError});var wasConnected=_this.state===WSTransportState.Open||_this._previousState===WSTransportState.Open;if(_this._shouldFallback||!wasConnected){_this._moveUriIndex()}_this._shouldFallback=true}_this._closeSocket()};_this._onSocketError=function(err){_this._log.error("WebSocket received error: "+err.message);_this.emit("error",{code:31e3,message:err.message||"WSTransport socket error",twilioError:new errors_1.SignalingErrors.ConnectionDisconnected})};_this._onSocketMessage=function(message){_this._setHeartbeatTimeout();if(_this._socket&&message.data==="\n"){_this._socket.send("\n");return}_this.emit("message",message)};_this._onSocketOpen=function(){_this._log.info("WebSocket opened successfully.");_this._timeOpened=Date.now();_this._shouldFallback=false;_this._setState(WSTransportState.Open);clearTimeout(_this._connectTimeout);_this._resetBackoffs();_this._setHeartbeatTimeout();_this.emit("open")};_this._options=__assign(__assign({},WSTransport.defaultConstructorOptions),options);_this._uris=uris;_this._backoff=_this._setupBackoffs();return _this}WSTransport.prototype.close=function(){this._log.info("WSTransport.close() called...");this._close()};WSTransport.prototype.open=function(){this._log.info("WSTransport.open() called...");if(this._socket&&(this._socket.readyState===WebSocket.CONNECTING||this._socket.readyState===WebSocket.OPEN)){this._log.info("WebSocket already open.");return}if(this._preferredUri){this._connect(this._preferredUri)}else{this._connect(this._uris[this._uriIndex])}};WSTransport.prototype.send=function(message){if(!this._socket||this._socket.readyState!==WebSocket.OPEN){return false}try{this._socket.send(message)}catch(e){this._log.error("Error while sending message:",e.message);this._closeSocket();return false}return true};WSTransport.prototype.updatePreferredURI=function(uri){this._preferredUri=uri};WSTransport.prototype.updateURIs=function(uris){if(typeof uris==="string"){uris=[uris]}this._uris=uris;this._uriIndex=0};WSTransport.prototype._close=function(){this._setState(WSTransportState.Closed);this._closeSocket()};WSTransport.prototype._closeSocket=function(){clearTimeout(this._connectTimeout);clearTimeout(this._heartbeatTimeout);this._log.info("Closing and cleaning up WebSocket...");if(!this._socket){this._log.info("No WebSocket to clean up.");return}this._socket.removeEventListener("close",this._onSocketClose);this._socket.removeEventListener("error",this._onSocketError);this._socket.removeEventListener("message",this._onSocketMessage);this._socket.removeEventListener("open",this._onSocketOpen);if(this._socket.readyState===WebSocket.CONNECTING||this._socket.readyState===WebSocket.OPEN){this._socket.close()}if(this._timeOpened&&Date.now()-this._timeOpened>CONNECT_SUCCESS_TIMEOUT){this._resetBackoffs()}if(this.state!==WSTransportState.Closed){this._performBackoff()}delete this._socket;this.emit("close")};WSTransport.prototype._connect=function(uri,retryCount){var _this=this;this._log.info(typeof retryCount==="number"?"Attempting to reconnect (retry #"+retryCount+")...":"Attempting to connect...");this._closeSocket();this._setState(WSTransportState.Connecting);this._connectedUri=uri;try{this._socket=new this._options.WebSocket(this._connectedUri)}catch(e){this._log.error("Could not connect to endpoint:",e.message);this._close();this.emit("error",{code:31e3,message:e.message||"Could not connect to "+this._connectedUri,twilioError:new errors_1.SignalingErrors.ConnectionDisconnected});return}this._socket.addEventListener("close",this._onSocketClose);this._socket.addEventListener("error",this._onSocketError);this._socket.addEventListener("message",this._onSocketMessage);this._socket.addEventListener("open",this._onSocketOpen);delete this._timeOpened;this._connectTimeout=setTimeout(function(){_this._log.info("WebSocket connection attempt timed out.");_this._moveUriIndex();_this._closeSocket()},this._options.connectTimeoutMs)};WSTransport.prototype._performBackoff=function(){if(this._preferredUri){this._log.info("Preferred URI set; backing off.");this._backoff.preferred.backoff()}else{this._log.info("Preferred URI not set; backing off.");this._backoff.primary.backoff()}};WSTransport.prototype._resetBackoffs=function(){this._backoff.preferred.reset();this._backoff.primary.reset();this._backoffStartTime.preferred=null;this._backoffStartTime.primary=null};WSTransport.prototype._setHeartbeatTimeout=function(){var _this=this;clearTimeout(this._heartbeatTimeout);this._heartbeatTimeout=setTimeout(function(){_this._log.info("No messages received in "+HEARTBEAT_TIMEOUT/1e3+" seconds. Reconnecting...");_this._shouldFallback=true;_this._closeSocket()},HEARTBEAT_TIMEOUT)};WSTransport.prototype._setState=function(state){this._previousState=this.state;this.state=state};WSTransport.prototype._setupBackoffs=function(){var _this=this;var preferredBackoffConfig={factor:2,jitter:.4,max:this._options.maxPreferredDelayMs,min:100};this._log.info("Initializing preferred transport backoff using config: ",preferredBackoffConfig);var preferredBackoff=new backoff_1.default(preferredBackoffConfig);preferredBackoff.on("backoff",function(attempt,delay){if(_this.state===WSTransportState.Closed){_this._log.info("Preferred backoff initiated but transport state is closed; not attempting a connection.");return}_this._log.info("Will attempt to reconnect Websocket to preferred URI in "+delay+"ms");if(attempt===0){_this._backoffStartTime.preferred=Date.now();_this._log.info("Preferred backoff start; "+_this._backoffStartTime.preferred)}});preferredBackoff.on("ready",function(attempt,_delay){if(_this.state===WSTransportState.Closed){_this._log.info("Preferred backoff ready but transport state is closed; not attempting a connection.");return}if(_this._backoffStartTime.preferred===null){_this._log.info("Preferred backoff start time invalid; not attempting a connection.");return}if(Date.now()-_this._backoffStartTime.preferred>_this._options.maxPreferredDurationMs){_this._log.info("Max preferred backoff attempt time exceeded; falling back to primary backoff.");_this._preferredUri=null;_this._backoff.primary.backoff();return}if(typeof _this._preferredUri!=="string"){_this._log.info("Preferred URI cleared; falling back to primary backoff.");_this._preferredUri=null;_this._backoff.primary.backoff();return}_this._connect(_this._preferredUri,attempt+1)});var primaryBackoffConfig={factor:2,jitter:.4,max:this._options.maxPrimaryDelayMs,min:this._uris&&this._uris.length>1?Math.floor(Math.random()*(5e3-1e3+1))+1e3:100};this._log.info("Initializing primary transport backoff using config: ",primaryBackoffConfig);var primaryBackoff=new backoff_1.default(primaryBackoffConfig);primaryBackoff.on("backoff",function(attempt,delay){if(_this.state===WSTransportState.Closed){_this._log.info("Primary backoff initiated but transport state is closed; not attempting a connection.");return}_this._log.info("Will attempt to reconnect WebSocket in "+delay+"ms");if(attempt===0){_this._backoffStartTime.primary=Date.now();_this._log.info("Primary backoff start; "+_this._backoffStartTime.primary)}});primaryBackoff.on("ready",function(attempt,_delay){if(_this.state===WSTransportState.Closed){_this._log.info("Primary backoff ready but transport state is closed; not attempting a connection.");return}if(_this._backoffStartTime.primary===null){_this._log.info("Primary backoff start time invalid; not attempting a connection.");return}if(Date.now()-_this._backoffStartTime.primary>_this._options.maxPrimaryDurationMs){_this._log.info("Max primary backoff attempt time exceeded; not attempting a connection.");return}_this._connect(_this._uris[_this._uriIndex],attempt+1)});return{preferred:preferredBackoff,primary:primaryBackoff}};Object.defineProperty(WSTransport.prototype,"uri",{get:function(){return this._connectedUri},enumerable:false,configurable:true});WSTransport.defaultConstructorOptions={WebSocket:WebSocket,connectTimeoutMs:CONNECT_TIMEOUT,maxPreferredDelayMs:MAX_PREFERRED_DELAY,maxPreferredDurationMs:MAX_PREFERRED_DURATION,maxPrimaryDelayMs:MAX_PRIMARY_DELAY,maxPrimaryDurationMs:MAX_PRIMARY_DURATION};return WSTransport}(events_1.EventEmitter);exports.default=WSTransport},{"./backoff":8,"./errors":15,"./log":18,events:39}],39:[function(require,module,exports){var objectCreate=Object.create||objectCreatePolyfill;var objectKeys=Object.keys||objectKeysPolyfill;var bind=Function.prototype.bind||functionBindPolyfill;function EventEmitter(){if(!this._events||!Object.prototype.hasOwnProperty.call(this,"_events")){this._events=objectCreate(null);this._eventsCount=0}this._maxListeners=this._maxListeners||undefined}module.exports=EventEmitter;EventEmitter.EventEmitter=EventEmitter;EventEmitter.prototype._events=undefined;EventEmitter.prototype._maxListeners=undefined;var defaultMaxListeners=10;var hasDefineProperty;try{var o={};if(Object.defineProperty)Object.defineProperty(o,"x",{value:0});hasDefineProperty=o.x===0}catch(err){hasDefineProperty=false}if(hasDefineProperty){Object.defineProperty(EventEmitter,"defaultMaxListeners",{enumerable:true,get:function(){return defaultMaxListeners},set:function(arg){if(typeof arg!=="number"||arg<0||arg!==arg)throw new TypeError('"defaultMaxListeners" must be a positive number');defaultMaxListeners=arg}})}else{EventEmitter.defaultMaxListeners=defaultMaxListeners}EventEmitter.prototype.setMaxListeners=function setMaxListeners(n){if(typeof n!=="number"||n<0||isNaN(n))throw new TypeError('"n" argument must be a positive number');this._maxListeners=n;return this};function $getMaxListeners(that){if(that._maxListeners===undefined)return EventEmitter.defaultMaxListeners;return that._maxListeners}EventEmitter.prototype.getMaxListeners=function getMaxListeners(){return $getMaxListeners(this)};function emitNone(handler,isFn,self){if(isFn)handler.call(self);else{var len=handler.length;var listeners=arrayClone(handler,len);for(var i=0;i<len;++i)listeners[i].call(self)}}function emitOne(handler,isFn,self,arg1){if(isFn)handler.call(self,arg1);else{var len=handler.length;var listeners=arrayClone(handler,len);for(var i=0;i<len;++i)listeners[i].call(self,arg1)}}function emitTwo(handler,isFn,self,arg1,arg2){if(isFn)handler.call(self,arg1,arg2);else{var len=handler.length;var listeners=arrayClone(handler,len);for(var i=0;i<len;++i)listeners[i].call(self,arg1,arg2)}}function emitThree(handler,isFn,self,arg1,arg2,arg3){if(isFn)handler.call(self,arg1,arg2,arg3);else{var len=handler.length;var listeners=arrayClone(handler,len);for(var i=0;i<len;++i)listeners[i].call(self,arg1,arg2,arg3)}}function emitMany(handler,isFn,self,args){if(isFn)handler.apply(self,args);else{var len=handler.length;var listeners=arrayClone(handler,len);for(var i=0;i<len;++i)listeners[i].apply(self,args)}}EventEmitter.prototype.emit=function emit(type){var er,handler,len,args,i,events;var doError=type==="error";events=this._events;if(events)doError=doError&&events.error==null;else if(!doError)return false;if(doError){if(arguments.length>1)er=arguments[1];if(er instanceof Error){throw er}else{var err=new Error('Unhandled "error" event. ('+er+")");err.context=er;throw err}return false}handler=events[type];if(!handler)return false;var isFn=typeof handler==="function";len=arguments.length;switch(len){case 1:emitNone(handler,isFn,this);break;case 2:emitOne(handler,isFn,this,arguments[1]);break;case 3:emitTwo(handler,isFn,this,arguments[1],arguments[2]);break;case 4:emitThree(handler,isFn,this,arguments[1],arguments[2],arguments[3]);break;default:args=new Array(len-1);for(i=1;i<len;i++)args[i-1]=arguments[i];emitMany(handler,isFn,this,args)}return true};function _addListener(target,type,listener,prepend){var m;var events;var existing;if(typeof listener!=="function")throw new TypeError('"listener" argument must be a function');events=target._events;if(!events){events=target._events=objectCreate(null);target._eventsCount=0}else{if(events.newListener){target.emit("newListener",type,listener.listener?listener.listener:listener);events=target._events}existing=events[type]}if(!existing){existing=events[type]=listener;++target._eventsCount}else{if(typeof existing==="function"){existing=events[type]=prepend?[listener,existing]:[existing,listener]}else{if(prepend){existing.unshift(listener)}else{existing.push(listener)}}if(!existing.warned){m=$getMaxListeners(target);if(m&&m>0&&existing.length>m){existing.warned=true;var w=new Error("Possible EventEmitter memory leak detected. "+existing.length+' "'+String(type)+'" listeners '+"added. Use emitter.setMaxListeners() to "+"increase limit.");w.name="MaxListenersExceededWarning";w.emitter=target;w.type=type;w.count=existing.length;if(typeof console==="object"&&console.warn){console.warn("%s: %s",w.name,w.message)}}}}return target}EventEmitter.prototype.addListener=function addListener(type,listener){return _addListener(this,type,listener,false)};EventEmitter.prototype.on=EventEmitter.prototype.addListener;EventEmitter.prototype.prependListener=function prependListener(type,listener){return _addListener(this,type,listener,true)};function onceWrapper(){if(!this.fired){this.target.removeListener(this.type,this.wrapFn);this.fired=true;switch(arguments.length){case 0:return this.listener.call(this.target);case 1:return this.listener.call(this.target,arguments[0]);case 2:return this.listener.call(this.target,arguments[0],arguments[1]);case 3:return this.listener.call(this.target,arguments[0],arguments[1],arguments[2]);default:var args=new Array(arguments.length);for(var i=0;i<args.length;++i)args[i]=arguments[i];this.listener.apply(this.target,args)}}}function _onceWrap(target,type,listener){var state={fired:false,wrapFn:undefined,target:target,type:type,listener:listener};var wrapped=bind.call(onceWrapper,state);wrapped.listener=listener;state.wrapFn=wrapped;return wrapped}EventEmitter.prototype.once=function once(type,listener){if(typeof listener!=="function")throw new TypeError('"listener" argument must be a function');this.on(type,_onceWrap(this,type,listener));return this};EventEmitter.prototype.prependOnceListener=function prependOnceListener(type,listener){if(typeof listener!=="function")throw new TypeError('"listener" argument must be a function');this.prependListener(type,_onceWrap(this,type,listener));return this};EventEmitter.prototype.removeListener=function removeListener(type,listener){var list,events,position,i,originalListener;if(typeof listener!=="function")throw new TypeError('"listener" argument must be a function');events=this._events;if(!events)return this;list=events[type];if(!list)return this;if(list===listener||list.listener===listener){if(--this._eventsCount===0)this._events=objectCreate(null);else{delete events[type];if(events.removeListener)this.emit("removeListener",type,list.listener||listener)}}else if(typeof list!=="function"){position=-1;for(i=list.length-1;i>=0;i--){if(list[i]===listener||list[i].listener===listener){originalListener=list[i].listener;position=i;break}}if(position<0)return this;if(position===0)list.shift();else spliceOne(list,position);if(list.length===1)events[type]=list[0];if(events.removeListener)this.emit("removeListener",type,originalListener||listener)}return this};EventEmitter.prototype.removeAllListeners=function removeAllListeners(type){var listeners,events,i;events=this._events;if(!events)return this;if(!events.removeListener){if(arguments.length===0){this._events=objectCreate(null);this._eventsCount=0}else if(events[type]){if(--this._eventsCount===0)this._events=objectCreate(null);else delete events[type]}return this}if(arguments.length===0){var keys=objectKeys(events);var key;for(i=0;i<keys.length;++i){key=keys[i];if(key==="removeListener")continue;this.removeAllListeners(key)}this.removeAllListeners("removeListener");this._events=objectCreate(null);this._eventsCount=0;return this}listeners=events[type];if(typeof listeners==="function"){this.removeListener(type,listeners)}else if(listeners){for(i=listeners.length-1;i>=0;i--){this.removeListener(type,listeners[i])}}return this};function _listeners(target,type,unwrap){var events=target._events;if(!events)return[];var evlistener=events[type];if(!evlistener)return[];if(typeof evlistener==="function")return unwrap?[evlistener.listener||evlistener]:[evlistener];return unwrap?unwrapListeners(evlistener):arrayClone(evlistener,evlistener.length)}EventEmitter.prototype.listeners=function listeners(type){return _listeners(this,type,true)};EventEmitter.prototype.rawListeners=function rawListeners(type){return _listeners(this,type,false)};EventEmitter.listenerCount=function(emitter,type){if(typeof emitter.listenerCount==="function"){return emitter.listenerCount(type)}else{return listenerCount.call(emitter,type)}};EventEmitter.prototype.listenerCount=listenerCount;function listenerCount(type){var events=this._events;if(events){var evlistener=events[type];if(typeof evlistener==="function"){return 1}else if(evlistener){return evlistener.length}}return 0}EventEmitter.prototype.eventNames=function eventNames(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]};function spliceOne(list,index){for(var i=index,k=i+1,n=list.length;k<n;i+=1,k+=1)list[i]=list[k];list.pop()}function arrayClone(arr,n){var copy=new Array(n);for(var i=0;i<n;++i)copy[i]=arr[i];return copy}function unwrapListeners(arr){var ret=new Array(arr.length);for(var i=0;i<ret.length;++i){ret[i]=arr[i].listener||arr[i]}return ret}function objectCreatePolyfill(proto){var F=function(){};F.prototype=proto;return new F}function objectKeysPolyfill(obj){var keys=[];for(var k in obj)if(Object.prototype.hasOwnProperty.call(obj,k)){keys.push(k)}return k}function functionBindPolyfill(context){var fn=this;return function(){return fn.apply(context,arguments)}}},{}],40:[function(require,module,exports){var charenc={utf8:{stringToBytes:function(str){return charenc.bin.stringToBytes(unescape(encodeURIComponent(str)))},bytesToString:function(bytes){return decodeURIComponent(escape(charenc.bin.bytesToString(bytes)))}},bin:{stringToBytes:function(str){for(var bytes=[],i=0;i<str.length;i++)bytes.push(str.charCodeAt(i)&255);return bytes},bytesToString:function(bytes){for(var str=[],i=0;i<bytes.length;i++)str.push(String.fromCharCode(bytes[i]));return str.join("")}}};module.exports=charenc},{}],41:[function(require,module,exports){(function(){var base64map="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",crypt={rotl:function(n,b){return n<<b|n>>>32-b},rotr:function(n,b){return n<<32-b|n>>>b},endian:function(n){if(n.constructor==Number){return crypt.rotl(n,8)&16711935|crypt.rotl(n,24)&4278255360}for(var i=0;i<n.length;i++)n[i]=crypt.endian(n[i]);return n},randomBytes:function(n){for(var bytes=[];n>0;n--)bytes.push(Math.floor(Math.random()*256));return bytes},bytesToWords:function(bytes){for(var words=[],i=0,b=0;i<bytes.length;i++,b+=8)words[b>>>5]|=bytes[i]<<24-b%32;return words},wordsToBytes:function(words){for(var bytes=[],b=0;b<words.length*32;b+=8)bytes.push(words[b>>>5]>>>24-b%32&255);return bytes},bytesToHex:function(bytes){for(var hex=[],i=0;i<bytes.length;i++){hex.push((bytes[i]>>>4).toString(16));hex.push((bytes[i]&15).toString(16))}return hex.join("")},hexToBytes:function(hex){for(var bytes=[],c=0;c<hex.length;c+=2)bytes.push(parseInt(hex.substr(c,2),16));return bytes},bytesToBase64:function(bytes){for(var base64=[],i=0;i<bytes.length;i+=3){var triplet=bytes[i]<<16|bytes[i+1]<<8|bytes[i+2];for(var j=0;j<4;j++)if(i*8+j*6<=bytes.length*8)base64.push(base64map.charAt(triplet>>>6*(3-j)&63));else base64.push("=")}return base64.join("")},base64ToBytes:function(base64){base64=base64.replace(/[^A-Z0-9+\/]/gi,"");for(var bytes=[],i=0,imod4=0;i<base64.length;imod4=++i%4){if(imod4==0)continue;bytes.push((base64map.indexOf(base64.charAt(i-1))&Math.pow(2,-2*imod4+8)-1)<<imod4*2|base64map.indexOf(base64.charAt(i))>>>6-imod4*2)}return bytes}};module.exports=crypt})()},{}],42:[function(require,module,exports){module.exports=function(obj){return obj!=null&&(isBuffer(obj)||isSlowBuffer(obj)||!!obj._isBuffer)};function isBuffer(obj){return!!obj.constructor&&typeof obj.constructor.isBuffer==="function"&&obj.constructor.isBuffer(obj)}function isSlowBuffer(obj){return typeof obj.readFloatLE==="function"&&typeof obj.slice==="function"&&isBuffer(obj.slice(0,0))}},{}],43:[function(require,module,exports){(function(root,definition){"use strict";if(typeof define==="function"&&define.amd){define(definition)}else if(typeof module==="object"&&module.exports){module.exports=definition()}else{root.log=definition()}})(this,function(){"use strict";var noop=function(){};var undefinedType="undefined";var isIE=typeof window!==undefinedType&&typeof window.navigator!==undefinedType&&/Trident\/|MSIE /.test(window.navigator.userAgent);var logMethods=["trace","debug","info","warn","error"];function bindMethod(obj,methodName){var method=obj[methodName];if(typeof method.bind==="function"){return method.bind(obj)}else{try{return Function.prototype.bind.call(method,obj)}catch(e){return function(){return Function.prototype.apply.apply(method,[obj,arguments])}}}}function traceForIE(){if(console.log){if(console.log.apply){console.log.apply(console,arguments)}else{Function.prototype.apply.apply(console.log,[console,arguments])}}if(console.trace)console.trace()}function realMethod(methodName){if(methodName==="debug"){methodName="log"}if(typeof console===undefinedType){return false}else if(methodName==="trace"&&isIE){return traceForIE}else if(console[methodName]!==undefined){return bindMethod(console,methodName)}else if(console.log!==undefined){return bindMethod(console,"log")}else{return noop}}function replaceLoggingMethods(level,loggerName){for(var i=0;i<logMethods.length;i++){var methodName=logMethods[i];this[methodName]=i<level?noop:this.methodFactory(methodName,level,loggerName)}this.log=this.debug}function enableLoggingWhenConsoleArrives(methodName,level,loggerName){return function(){if(typeof console!==undefinedType){replaceLoggingMethods.call(this,level,loggerName);this[methodName].apply(this,arguments)}}}function defaultMethodFactory(methodName,level,loggerName){return realMethod(methodName)||enableLoggingWhenConsoleArrives.apply(this,arguments)}function Logger(name,defaultLevel,factory){var self=this;var currentLevel;var storageKey="loglevel";if(name){storageKey+=":"+name}function persistLevelIfPossible(levelNum){var levelName=(logMethods[levelNum]||"silent").toUpperCase();if(typeof window===undefinedType)return;try{window.localStorage[storageKey]=levelName;return}catch(ignore){}try{window.document.cookie=encodeURIComponent(storageKey)+"="+levelName+";"}catch(ignore){}}function getPersistedLevel(){var storedLevel;if(typeof window===undefinedType)return;try{storedLevel=window.localStorage[storageKey]}catch(ignore){}if(typeof storedLevel===undefinedType){try{var cookie=window.document.cookie;var location=cookie.indexOf(encodeURIComponent(storageKey)+"=");if(location!==-1){storedLevel=/^([^;]+)/.exec(cookie.slice(location))[1]}}catch(ignore){}}if(self.levels[storedLevel]===undefined){storedLevel=undefined}return storedLevel}self.name=name;self.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5};self.methodFactory=factory||defaultMethodFactory;self.getLevel=function(){return currentLevel};self.setLevel=function(level,persist){if(typeof level==="string"&&self.levels[level.toUpperCase()]!==undefined){level=self.levels[level.toUpperCase()]}if(typeof level==="number"&&level>=0&&level<=self.levels.SILENT){currentLevel=level;if(persist!==false){persistLevelIfPossible(level)}replaceLoggingMethods.call(self,level,name);if(typeof console===undefinedType&&level<self.levels.SILENT){return"No console available for logging"}}else{throw"log.setLevel() called with invalid level: "+level}};self.setDefaultLevel=function(level){if(!getPersistedLevel()){self.setLevel(level,false)}};self.enableAll=function(persist){self.setLevel(self.levels.TRACE,persist)};self.disableAll=function(persist){self.setLevel(self.levels.SILENT,persist)};var initialLevel=getPersistedLevel();if(initialLevel==null){initialLevel=defaultLevel==null?"WARN":defaultLevel}self.setLevel(initialLevel,false)}var defaultLogger=new Logger;var _loggersByName={};defaultLogger.getLogger=function getLogger(name){if(typeof name!=="string"||name===""){throw new TypeError("You must supply a name when creating a logger.")}var logger=_loggersByName[name];if(!logger){logger=_loggersByName[name]=new Logger(name,defaultLogger.getLevel(),defaultLogger.methodFactory)}return logger};var _log=typeof window!==undefinedType?window.log:undefined;defaultLogger.noConflict=function(){if(typeof window!==undefinedType&&window.log===defaultLogger){window.log=_log}return defaultLogger};defaultLogger.getLoggers=function getLoggers(){return _loggersByName};return defaultLogger})},{}],44:[function(require,module,exports){(function(){var crypt=require("crypt"),utf8=require("charenc").utf8,isBuffer=require("is-buffer"),bin=require("charenc").bin,md5=function(message,options){if(message.constructor==String)if(options&&options.encoding==="binary")message=bin.stringToBytes(message);else message=utf8.stringToBytes(message);else if(isBuffer(message))message=Array.prototype.slice.call(message,0);else if(!Array.isArray(message)&&message.constructor!==Uint8Array)message=message.toString();var m=crypt.bytesToWords(message),l=message.length*8,a=1732584193,b=-271733879,c=-1732584194,d=271733878;for(var i=0;i<m.length;i++){m[i]=(m[i]<<8|m[i]>>>24)&16711935|(m[i]<<24|m[i]>>>8)&4278255360}m[l>>>5]|=128<<l%32;m[(l+64>>>9<<4)+14]=l;var FF=md5._ff,GG=md5._gg,HH=md5._hh,II=md5._ii;for(var i=0;i<m.length;i+=16){var aa=a,bb=b,cc=c,dd=d;a=FF(a,b,c,d,m[i+0],7,-680876936);d=FF(d,a,b,c,m[i+1],12,-389564586);c=FF(c,d,a,b,m[i+2],17,606105819);b=FF(b,c,d,a,m[i+3],22,-1044525330);a=FF(a,b,c,d,m[i+4],7,-176418897);d=FF(d,a,b,c,m[i+5],12,1200080426);c=FF(c,d,a,b,m[i+6],17,-1473231341);b=FF(b,c,d,a,m[i+7],22,-45705983);a=FF(a,b,c,d,m[i+8],7,1770035416);d=FF(d,a,b,c,m[i+9],12,-1958414417);c=FF(c,d,a,b,m[i+10],17,-42063);b=FF(b,c,d,a,m[i+11],22,-1990404162);a=FF(a,b,c,d,m[i+12],7,1804603682);d=FF(d,a,b,c,m[i+13],12,-40341101);c=FF(c,d,a,b,m[i+14],17,-1502002290);b=FF(b,c,d,a,m[i+15],22,1236535329);a=GG(a,b,c,d,m[i+1],5,-165796510);d=GG(d,a,b,c,m[i+6],9,-1069501632);c=GG(c,d,a,b,m[i+11],14,643717713);b=GG(b,c,d,a,m[i+0],20,-373897302);a=GG(a,b,c,d,m[i+5],5,-701558691);d=GG(d,a,b,c,m[i+10],9,38016083);c=GG(c,d,a,b,m[i+15],14,-660478335);b=GG(b,c,d,a,m[i+4],20,-405537848);a=GG(a,b,c,d,m[i+9],5,568446438);d=GG(d,a,b,c,m[i+14],9,-1019803690);c=GG(c,d,a,b,m[i+3],14,-187363961);b=GG(b,c,d,a,m[i+8],20,1163531501);a=GG(a,b,c,d,m[i+13],5,-1444681467);d=GG(d,a,b,c,m[i+2],9,-51403784);c=GG(c,d,a,b,m[i+7],14,1735328473);b=GG(b,c,d,a,m[i+12],20,-1926607734);a=HH(a,b,c,d,m[i+5],4,-378558);d=HH(d,a,b,c,m[i+8],11,-2022574463);c=HH(c,d,a,b,m[i+11],16,1839030562);b=HH(b,c,d,a,m[i+14],23,-35309556);a=HH(a,b,c,d,m[i+1],4,-1530992060);d=HH(d,a,b,c,m[i+4],11,1272893353);c=HH(c,d,a,b,m[i+7],16,-155497632);b=HH(b,c,d,a,m[i+10],23,-1094730640);a=HH(a,b,c,d,m[i+13],4,681279174);d=HH(d,a,b,c,m[i+0],11,-358537222);c=HH(c,d,a,b,m[i+3],16,-722521979);b=HH(b,c,d,a,m[i+6],23,76029189);a=HH(a,b,c,d,m[i+9],4,-640364487);d=HH(d,a,b,c,m[i+12],11,-421815835);c=HH(c,d,a,b,m[i+15],16,530742520);b=HH(b,c,d,a,m[i+2],23,-995338651);a=II(a,b,c,d,m[i+0],6,-198630844);d=II(d,a,b,c,m[i+7],10,1126891415);c=II(c,d,a,b,m[i+14],15,-1416354905);b=II(b,c,d,a,m[i+5],21,-57434055);a=II(a,b,c,d,m[i+12],6,1700485571);d=II(d,a,b,c,m[i+3],10,-1894986606);c=II(c,d,a,b,m[i+10],15,-1051523);b=II(b,c,d,a,m[i+1],21,-2054922799);a=II(a,b,c,d,m[i+8],6,1873313359);d=II(d,a,b,c,m[i+15],10,-30611744);c=II(c,d,a,b,m[i+6],15,-1560198380);b=II(b,c,d,a,m[i+13],21,1309151649);a=II(a,b,c,d,m[i+4],6,-145523070);d=II(d,a,b,c,m[i+11],10,-1120210379);c=II(c,d,a,b,m[i+2],15,718787259);b=II(b,c,d,a,m[i+9],21,-343485551);a=a+aa>>>0;b=b+bb>>>0;c=c+cc>>>0;d=d+dd>>>0}return crypt.endian([a,b,c,d])};md5._ff=function(a,b,c,d,x,s,t){var n=a+(b&c|~b&d)+(x>>>0)+t;return(n<<s|n>>>32-s)+b};md5._gg=function(a,b,c,d,x,s,t){var n=a+(b&d|c&~d)+(x>>>0)+t;return(n<<s|n>>>32-s)+b};md5._hh=function(a,b,c,d,x,s,t){var n=a+(b^c^d)+(x>>>0)+t;return(n<<s|n>>>32-s)+b};md5._ii=function(a,b,c,d,x,s,t){var n=a+(c^(b|~d))+(x>>>0)+t;return(n<<s|n>>>32-s)+b};md5._blocksize=16;md5._digestsize=16;module.exports=function(message,options){if(message===undefined||message===null)throw new Error("Illegal argument "+message);var digestbytes=crypt.wordsToBytes(md5(message,options));return options&&options.asBytes?digestbytes:options&&options.asString?bin.bytesToString(digestbytes):crypt.bytesToHex(digestbytes)}})()},{charenc:40,crypt:41,"is-buffer":42}],45:[function(require,module,exports){"use strict";var SDPUtils=require("sdp");function writeMediaSection(transceiver,caps,type,stream,dtlsRole){var sdp=SDPUtils.writeRtpDescription(transceiver.kind,caps);sdp+=SDPUtils.writeIceParameters(transceiver.iceGatherer.getLocalParameters());sdp+=SDPUtils.writeDtlsParameters(transceiver.dtlsTransport.getLocalParameters(),type==="offer"?"actpass":dtlsRole||"active");sdp+="a=mid:"+transceiver.mid+"\r\n";if(transceiver.rtpSender&&transceiver.rtpReceiver){sdp+="a=sendrecv\r\n"}else if(transceiver.rtpSender){sdp+="a=sendonly\r\n"}else if(transceiver.rtpReceiver){sdp+="a=recvonly\r\n"}else{sdp+="a=inactive\r\n"}if(transceiver.rtpSender){var trackId=transceiver.rtpSender._initialTrackId||transceiver.rtpSender.track.id;transceiver.rtpSender._initialTrackId=trackId;var msid="msid:"+(stream?stream.id:"-")+" "+trackId+"\r\n";sdp+="a="+msid;sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].ssrc+" "+msid;if(transceiver.sendEncodingParameters[0].rtx){sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].rtx.ssrc+" "+msid;sdp+="a=ssrc-group:FID "+transceiver.sendEncodingParameters[0].ssrc+" "+transceiver.sendEncodingParameters[0].rtx.ssrc+"\r\n"}}sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].ssrc+" cname:"+SDPUtils.localCName+"\r\n";if(transceiver.rtpSender&&transceiver.sendEncodingParameters[0].rtx){sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].rtx.ssrc+" cname:"+SDPUtils.localCName+"\r\n"}return sdp}function filterIceServers(iceServers,edgeVersion){var hasTurn=false;iceServers=JSON.parse(JSON.stringify(iceServers));return iceServers.filter(function(server){if(server&&(server.urls||server.url)){var urls=server.urls||server.url;if(server.url&&!server.urls){console.warn("RTCIceServer.url is deprecated! Use urls instead.")}var isString=typeof urls==="string";if(isString){urls=[urls]}urls=urls.filter(function(url){var validTurn=url.indexOf("turn:")===0&&url.indexOf("transport=udp")!==-1&&url.indexOf("turn:[")===-1&&!hasTurn;if(validTurn){hasTurn=true;return true}return url.indexOf("stun:")===0&&edgeVersion>=14393&&url.indexOf("?transport=udp")===-1});delete server.url;server.urls=isString?urls[0]:urls;return!!urls.length}})}function getCommonCapabilities(localCapabilities,remoteCapabilities){var commonCapabilities={codecs:[],headerExtensions:[],fecMechanisms:[]};var findCodecByPayloadType=function(pt,codecs){pt=parseInt(pt,10);for(var i=0;i<codecs.length;i++){if(codecs[i].payloadType===pt||codecs[i].preferredPayloadType===pt){return codecs[i]}}};var rtxCapabilityMatches=function(lRtx,rRtx,lCodecs,rCodecs){var lCodec=findCodecByPayloadType(lRtx.parameters.apt,lCodecs);var rCodec=findCodecByPayloadType(rRtx.parameters.apt,rCodecs);return lCodec&&rCodec&&lCodec.name.toLowerCase()===rCodec.name.toLowerCase()};localCapabilities.codecs.forEach(function(lCodec){for(var i=0;i<remoteCapabilities.codecs.length;i++){var rCodec=remoteCapabilities.codecs[i];if(lCodec.name.toLowerCase()===rCodec.name.toLowerCase()&&lCodec.clockRate===rCodec.clockRate){if(lCodec.name.toLowerCase()==="rtx"&&lCodec.parameters&&rCodec.parameters.apt){if(!rtxCapabilityMatches(lCodec,rCodec,localCapabilities.codecs,remoteCapabilities.codecs)){continue}}rCodec=JSON.parse(JSON.stringify(rCodec));rCodec.numChannels=Math.min(lCodec.numChannels,rCodec.numChannels);commonCapabilities.codecs.push(rCodec);rCodec.rtcpFeedback=rCodec.rtcpFeedback.filter(function(fb){for(var j=0;j<lCodec.rtcpFeedback.length;j++){if(lCodec.rtcpFeedback[j].type===fb.type&&lCodec.rtcpFeedback[j].parameter===fb.parameter){return true}}return false});break}}});localCapabilities.headerExtensions.forEach(function(lHeaderExtension){for(var i=0;i<remoteCapabilities.headerExtensions.length;i++){var rHeaderExtension=remoteCapabilities.headerExtensions[i];if(lHeaderExtension.uri===rHeaderExtension.uri){commonCapabilities.headerExtensions.push(rHeaderExtension);break}}});return commonCapabilities}function isActionAllowedInSignalingState(action,type,signalingState){return{offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[type][action].indexOf(signalingState)!==-1}function maybeAddCandidate(iceTransport,candidate){var alreadyAdded=iceTransport.getRemoteCandidates().find(function(remoteCandidate){return candidate.foundation===remoteCandidate.foundation&&candidate.ip===remoteCandidate.ip&&candidate.port===remoteCandidate.port&&candidate.priority===remoteCandidate.priority&&candidate.protocol===remoteCandidate.protocol&&candidate.type===remoteCandidate.type});if(!alreadyAdded){iceTransport.addRemoteCandidate(candidate)}return!alreadyAdded}function makeError(name,description){var e=new Error(description);e.name=name;return e}module.exports=function(window,edgeVersion){function addTrackToStreamAndFireEvent(track,stream){stream.addTrack(track);stream.dispatchEvent(new window.MediaStreamTrackEvent("addtrack",{track:track}))}function removeTrackFromStreamAndFireEvent(track,stream){stream.removeTrack(track);stream.dispatchEvent(new window.MediaStreamTrackEvent("removetrack",{track:track}))}function fireAddTrack(pc,track,receiver,streams){var trackEvent=new Event("track");trackEvent.track=track;trackEvent.receiver=receiver;trackEvent.transceiver={receiver:receiver};trackEvent.streams=streams;window.setTimeout(function(){pc._dispatchEvent("track",trackEvent)})}var RTCPeerConnection=function(config){var pc=this;var _eventTarget=document.createDocumentFragment();["addEventListener","removeEventListener","dispatchEvent"].forEach(function(method){pc[method]=_eventTarget[method].bind(_eventTarget)});this.canTrickleIceCandidates=null;this.needNegotiation=false;this.localStreams=[];this.remoteStreams=[];this.localDescription=null;this.remoteDescription=null;this.signalingState="stable";this.iceConnectionState="new";this.iceGatheringState="new";config=JSON.parse(JSON.stringify(config||{}));this.usingBundle=config.bundlePolicy==="max-bundle";if(config.rtcpMuxPolicy==="negotiate"){throw makeError("NotSupportedError","rtcpMuxPolicy 'negotiate' is not supported")}else if(!config.rtcpMuxPolicy){config.rtcpMuxPolicy="require"}switch(config.iceTransportPolicy){case"all":case"relay":break;default:config.iceTransportPolicy="all";break}switch(config.bundlePolicy){case"balanced":case"max-compat":case"max-bundle":break;default:config.bundlePolicy="balanced";break}config.iceServers=filterIceServers(config.iceServers||[],edgeVersion);this._iceGatherers=[];if(config.iceCandidatePoolSize){for(var i=config.iceCandidatePoolSize;i>0;i--){this._iceGatherers.push(new window.RTCIceGatherer({iceServers:config.iceServers,gatherPolicy:config.iceTransportPolicy}))}}else{config.iceCandidatePoolSize=0}this._config=config;this.transceivers=[];this._sdpSessionId=SDPUtils.generateSessionId();this._sdpSessionVersion=0;this._dtlsRole=undefined;this._isClosed=false};RTCPeerConnection.prototype.onicecandidate=null;RTCPeerConnection.prototype.onaddstream=null;RTCPeerConnection.prototype.ontrack=null;RTCPeerConnection.prototype.onremovestream=null;RTCPeerConnection.prototype.onsignalingstatechange=null;RTCPeerConnection.prototype.oniceconnectionstatechange=null;RTCPeerConnection.prototype.onicegatheringstatechange=null;RTCPeerConnection.prototype.onnegotiationneeded=null;RTCPeerConnection.prototype.ondatachannel=null;RTCPeerConnection.prototype._dispatchEvent=function(name,event){if(this._isClosed){return}this.dispatchEvent(event);if(typeof this["on"+name]==="function"){this["on"+name](event)}};RTCPeerConnection.prototype._emitGatheringStateChange=function(){var event=new Event("icegatheringstatechange");this._dispatchEvent("icegatheringstatechange",event)};RTCPeerConnection.prototype.getConfiguration=function(){return this._config};RTCPeerConnection.prototype.getLocalStreams=function(){return this.localStreams};RTCPeerConnection.prototype.getRemoteStreams=function(){return this.remoteStreams};RTCPeerConnection.prototype._createTransceiver=function(kind){var hasBundleTransport=this.transceivers.length>0;var transceiver={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:kind,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,associatedRemoteMediaStreams:[],wantReceive:true};if(this.usingBundle&&hasBundleTransport){transceiver.iceTransport=this.transceivers[0].iceTransport;transceiver.dtlsTransport=this.transceivers[0].dtlsTransport}else{var transports=this._createIceAndDtlsTransports();transceiver.iceTransport=transports.iceTransport;transceiver.dtlsTransport=transports.dtlsTransport}this.transceivers.push(transceiver);return transceiver};RTCPeerConnection.prototype.addTrack=function(track,stream){if(this._isClosed){throw makeError("InvalidStateError","Attempted to call addTrack on a closed peerconnection.")}var alreadyExists=this.transceivers.find(function(s){return s.track===track});if(alreadyExists){throw makeError("InvalidAccessError","Track already exists.")}var transceiver;for(var i=0;i<this.transceivers.length;i++){if(!this.transceivers[i].track&&this.transceivers[i].kind===track.kind){transceiver=this.transceivers[i]}}if(!transceiver){transceiver=this._createTransceiver(track.kind)}this._maybeFireNegotiationNeeded();if(this.localStreams.indexOf(stream)===-1){this.localStreams.push(stream)}transceiver.track=track;transceiver.stream=stream;transceiver.rtpSender=new window.RTCRtpSender(track,transceiver.dtlsTransport);return transceiver.rtpSender};RTCPeerConnection.prototype.addStream=function(stream){var pc=this;if(edgeVersion>=15025){stream.getTracks().forEach(function(track){pc.addTrack(track,stream)})}else{var clonedStream=stream.clone();stream.getTracks().forEach(function(track,idx){var clonedTrack=clonedStream.getTracks()[idx];track.addEventListener("enabled",function(event){clonedTrack.enabled=event.enabled})});clonedStream.getTracks().forEach(function(track){pc.addTrack(track,clonedStream)})}};RTCPeerConnection.prototype.removeTrack=function(sender){if(this._isClosed){throw makeError("InvalidStateError","Attempted to call removeTrack on a closed peerconnection.")}if(!(sender instanceof window.RTCRtpSender)){throw new TypeError("Argument 1 of RTCPeerConnection.removeTrack "+"does not implement interface RTCRtpSender.")}var transceiver=this.transceivers.find(function(t){return t.rtpSender===sender});if(!transceiver){throw makeError("InvalidAccessError","Sender was not created by this connection.")}var stream=transceiver.stream;transceiver.rtpSender.stop();transceiver.rtpSender=null;transceiver.track=null;transceiver.stream=null;var localStreams=this.transceivers.map(function(t){return t.stream});if(localStreams.indexOf(stream)===-1&&this.localStreams.indexOf(stream)>-1){this.localStreams.splice(this.localStreams.indexOf(stream),1)}this._maybeFireNegotiationNeeded()};RTCPeerConnection.prototype.removeStream=function(stream){var pc=this;stream.getTracks().forEach(function(track){var sender=pc.getSenders().find(function(s){return s.track===track});if(sender){pc.removeTrack(sender)}})};RTCPeerConnection.prototype.getSenders=function(){return this.transceivers.filter(function(transceiver){return!!transceiver.rtpSender}).map(function(transceiver){return transceiver.rtpSender})};RTCPeerConnection.prototype.getReceivers=function(){return this.transceivers.filter(function(transceiver){return!!transceiver.rtpReceiver}).map(function(transceiver){return transceiver.rtpReceiver})};RTCPeerConnection.prototype._createIceGatherer=function(sdpMLineIndex,usingBundle){var pc=this;if(usingBundle&&sdpMLineIndex>0){return this.transceivers[0].iceGatherer}else if(this._iceGatherers.length){return this._iceGatherers.shift()}var iceGatherer=new window.RTCIceGatherer({iceServers:this._config.iceServers,gatherPolicy:this._config.iceTransportPolicy});Object.defineProperty(iceGatherer,"state",{value:"new",writable:true});this.transceivers[sdpMLineIndex].bufferedCandidateEvents=[];this.transceivers[sdpMLineIndex].bufferCandidates=function(event){var end=!event.candidate||Object.keys(event.candidate).length===0;iceGatherer.state=end?"completed":"gathering";if(pc.transceivers[sdpMLineIndex].bufferedCandidateEvents!==null){pc.transceivers[sdpMLineIndex].bufferedCandidateEvents.push(event)}};iceGatherer.addEventListener("localcandidate",this.transceivers[sdpMLineIndex].bufferCandidates);return iceGatherer};RTCPeerConnection.prototype._gather=function(mid,sdpMLineIndex){var pc=this;var iceGatherer=this.transceivers[sdpMLineIndex].iceGatherer;if(iceGatherer.onlocalcandidate){return}var bufferedCandidateEvents=this.transceivers[sdpMLineIndex].bufferedCandidateEvents;this.transceivers[sdpMLineIndex].bufferedCandidateEvents=null;iceGatherer.removeEventListener("localcandidate",this.transceivers[sdpMLineIndex].bufferCandidates);iceGatherer.onlocalcandidate=function(evt){if(pc.usingBundle&&sdpMLineIndex>0){return}var event=new Event("icecandidate");event.candidate={sdpMid:mid,sdpMLineIndex:sdpMLineIndex};var cand=evt.candidate;var end=!cand||Object.keys(cand).length===0;if(end){if(iceGatherer.state==="new"||iceGatherer.state==="gathering"){iceGatherer.state="completed"}}else{if(iceGatherer.state==="new"){iceGatherer.state="gathering"}cand.component=1;var serializedCandidate=SDPUtils.writeCandidate(cand);event.candidate=Object.assign(event.candidate,SDPUtils.parseCandidate(serializedCandidate));event.candidate.candidate=serializedCandidate}var sections=SDPUtils.getMediaSections(pc.localDescription.sdp);if(!end){sections[event.candidate.sdpMLineIndex]+="a="+event.candidate.candidate+"\r\n"}else{sections[event.candidate.sdpMLineIndex]+="a=end-of-candidates\r\n"}pc.localDescription.sdp=SDPUtils.getDescription(pc.localDescription.sdp)+sections.join("");var complete=pc.transceivers.every(function(transceiver){return transceiver.iceGatherer&&transceiver.iceGatherer.state==="completed"});if(pc.iceGatheringState!=="gathering"){pc.iceGatheringState="gathering";pc._emitGatheringStateChange()}if(!end){pc._dispatchEvent("icecandidate",event)}if(complete){pc._dispatchEvent("icecandidate",new Event("icecandidate"));pc.iceGatheringState="complete";pc._emitGatheringStateChange()}};window.setTimeout(function(){bufferedCandidateEvents.forEach(function(e){iceGatherer.onlocalcandidate(e)})},0)};RTCPeerConnection.prototype._createIceAndDtlsTransports=function(){var pc=this;var iceTransport=new window.RTCIceTransport(null);iceTransport.onicestatechange=function(){pc._updateConnectionState()};var dtlsTransport=new window.RTCDtlsTransport(iceTransport);dtlsTransport.ondtlsstatechange=function(){pc._updateConnectionState()};dtlsTransport.onerror=function(){Object.defineProperty(dtlsTransport,"state",{value:"failed",writable:true});pc._updateConnectionState()};return{iceTransport:iceTransport,dtlsTransport:dtlsTransport}};RTCPeerConnection.prototype._disposeIceAndDtlsTransports=function(sdpMLineIndex){var iceGatherer=this.transceivers[sdpMLineIndex].iceGatherer;if(iceGatherer){delete iceGatherer.onlocalcandidate;delete this.transceivers[sdpMLineIndex].iceGatherer}var iceTransport=this.transceivers[sdpMLineIndex].iceTransport;if(iceTransport){delete iceTransport.onicestatechange;delete this.transceivers[sdpMLineIndex].iceTransport}var dtlsTransport=this.transceivers[sdpMLineIndex].dtlsTransport;if(dtlsTransport){delete dtlsTransport.ondtlsstatechange;delete dtlsTransport.onerror;delete this.transceivers[sdpMLineIndex].dtlsTransport}};RTCPeerConnection.prototype._transceive=function(transceiver,send,recv){var params=getCommonCapabilities(transceiver.localCapabilities,transceiver.remoteCapabilities);if(send&&transceiver.rtpSender){params.encodings=transceiver.sendEncodingParameters;params.rtcp={cname:SDPUtils.localCName,compound:transceiver.rtcpParameters.compound};if(transceiver.recvEncodingParameters.length){params.rtcp.ssrc=transceiver.recvEncodingParameters[0].ssrc}transceiver.rtpSender.send(params)}if(recv&&transceiver.rtpReceiver&&params.codecs.length>0){if(transceiver.kind==="video"&&transceiver.recvEncodingParameters&&edgeVersion<15019){transceiver.recvEncodingParameters.forEach(function(p){delete p.rtx})}if(transceiver.recvEncodingParameters.length){params.encodings=transceiver.recvEncodingParameters}else{params.encodings=[{}]}params.rtcp={compound:transceiver.rtcpParameters.compound};if(transceiver.rtcpParameters.cname){params.rtcp.cname=transceiver.rtcpParameters.cname}if(transceiver.sendEncodingParameters.length){params.rtcp.ssrc=transceiver.sendEncodingParameters[0].ssrc}transceiver.rtpReceiver.receive(params)}};RTCPeerConnection.prototype.setLocalDescription=function(description){var pc=this;if(["offer","answer"].indexOf(description.type)===-1){return Promise.reject(makeError("TypeError",'Unsupported type "'+description.type+'"'))}if(!isActionAllowedInSignalingState("setLocalDescription",description.type,pc.signalingState)||pc._isClosed){return Promise.reject(makeError("InvalidStateError","Can not set local "+description.type+" in state "+pc.signalingState))}var sections;var sessionpart;if(description.type==="offer"){sections=SDPUtils.splitSections(description.sdp);sessionpart=sections.shift();sections.forEach(function(mediaSection,sdpMLineIndex){var caps=SDPUtils.parseRtpParameters(mediaSection);pc.transceivers[sdpMLineIndex].localCapabilities=caps});pc.transceivers.forEach(function(transceiver,sdpMLineIndex){pc._gather(transceiver.mid,sdpMLineIndex)})}else if(description.type==="answer"){sections=SDPUtils.splitSections(pc.remoteDescription.sdp);sessionpart=sections.shift();var isIceLite=SDPUtils.matchPrefix(sessionpart,"a=ice-lite").length>0;sections.forEach(function(mediaSection,sdpMLineIndex){var transceiver=pc.transceivers[sdpMLineIndex];var iceGatherer=transceiver.iceGatherer;var iceTransport=transceiver.iceTransport;var dtlsTransport=transceiver.dtlsTransport;var localCapabilities=transceiver.localCapabilities;var remoteCapabilities=transceiver.remoteCapabilities;var rejected=SDPUtils.isRejected(mediaSection)&&SDPUtils.matchPrefix(mediaSection,"a=bundle-only").length===0;if(!rejected&&!transceiver.isDatachannel){var remoteIceParameters=SDPUtils.getIceParameters(mediaSection,sessionpart);var remoteDtlsParameters=SDPUtils.getDtlsParameters(mediaSection,sessionpart);if(isIceLite){remoteDtlsParameters.role="server"}if(!pc.usingBundle||sdpMLineIndex===0){pc._gather(transceiver.mid,sdpMLineIndex);if(iceTransport.state==="new"){iceTransport.start(iceGatherer,remoteIceParameters,isIceLite?"controlling":"controlled")}if(dtlsTransport.state==="new"){dtlsTransport.start(remoteDtlsParameters)}}var params=getCommonCapabilities(localCapabilities,remoteCapabilities);pc._transceive(transceiver,params.codecs.length>0,false)}})}pc.localDescription={type:description.type,sdp:description.sdp};if(description.type==="offer"){pc._updateSignalingState("have-local-offer")}else{pc._updateSignalingState("stable")}return Promise.resolve()};RTCPeerConnection.prototype.setRemoteDescription=function(description){var pc=this;if(["offer","answer"].indexOf(description.type)===-1){return Promise.reject(makeError("TypeError",'Unsupported type "'+description.type+'"'))}if(!isActionAllowedInSignalingState("setRemoteDescription",description.type,pc.signalingState)||pc._isClosed){return Promise.reject(makeError("InvalidStateError","Can not set remote "+description.type+" in state "+pc.signalingState))}var streams={};pc.remoteStreams.forEach(function(stream){streams[stream.id]=stream});var receiverList=[];var sections=SDPUtils.splitSections(description.sdp);var sessionpart=sections.shift();var isIceLite=SDPUtils.matchPrefix(sessionpart,"a=ice-lite").length>0;var usingBundle=SDPUtils.matchPrefix(sessionpart,"a=group:BUNDLE ").length>0;pc.usingBundle=usingBundle;var iceOptions=SDPUtils.matchPrefix(sessionpart,"a=ice-options:")[0];if(iceOptions){pc.canTrickleIceCandidates=iceOptions.substr(14).split(" ").indexOf("trickle")>=0}else{pc.canTrickleIceCandidates=false}sections.forEach(function(mediaSection,sdpMLineIndex){var lines=SDPUtils.splitLines(mediaSection);var kind=SDPUtils.getKind(mediaSection);var rejected=SDPUtils.isRejected(mediaSection)&&SDPUtils.matchPrefix(mediaSection,"a=bundle-only").length===0;var protocol=lines[0].substr(2).split(" ")[2];var direction=SDPUtils.getDirection(mediaSection,sessionpart);var remoteMsid=SDPUtils.parseMsid(mediaSection);var mid=SDPUtils.getMid(mediaSection)||SDPUtils.generateIdentifier();if(kind==="application"&&protocol==="DTLS/SCTP"){pc.transceivers[sdpMLineIndex]={mid:mid,isDatachannel:true};return}var transceiver;var iceGatherer;var iceTransport;var dtlsTransport;var rtpReceiver;var sendEncodingParameters;var recvEncodingParameters;var localCapabilities;var track;var remoteCapabilities=SDPUtils.parseRtpParameters(mediaSection);var remoteIceParameters;var remoteDtlsParameters;if(!rejected){remoteIceParameters=SDPUtils.getIceParameters(mediaSection,sessionpart);remoteDtlsParameters=SDPUtils.getDtlsParameters(mediaSection,sessionpart);remoteDtlsParameters.role="client"}recvEncodingParameters=SDPUtils.parseRtpEncodingParameters(mediaSection);var rtcpParameters=SDPUtils.parseRtcpParameters(mediaSection);var isComplete=SDPUtils.matchPrefix(mediaSection,"a=end-of-candidates",sessionpart).length>0;var cands=SDPUtils.matchPrefix(mediaSection,"a=candidate:").map(function(cand){return SDPUtils.parseCandidate(cand)}).filter(function(cand){return cand.component===1});if((description.type==="offer"||description.type==="answer")&&!rejected&&usingBundle&&sdpMLineIndex>0&&pc.transceivers[sdpMLineIndex]){pc._disposeIceAndDtlsTransports(sdpMLineIndex);pc.transceivers[sdpMLineIndex].iceGatherer=pc.transceivers[0].iceGatherer;pc.transceivers[sdpMLineIndex].iceTransport=pc.transceivers[0].iceTransport;pc.transceivers[sdpMLineIndex].dtlsTransport=pc.transceivers[0].dtlsTransport;if(pc.transceivers[sdpMLineIndex].rtpSender){pc.transceivers[sdpMLineIndex].rtpSender.setTransport(pc.transceivers[0].dtlsTransport)}if(pc.transceivers[sdpMLineIndex].rtpReceiver){pc.transceivers[sdpMLineIndex].rtpReceiver.setTransport(pc.transceivers[0].dtlsTransport)}}if(description.type==="offer"&&!rejected){transceiver=pc.transceivers[sdpMLineIndex]||pc._createTransceiver(kind);transceiver.mid=mid;if(!transceiver.iceGatherer){transceiver.iceGatherer=pc._createIceGatherer(sdpMLineIndex,usingBundle)}if(cands.length&&transceiver.iceTransport.state==="new"){if(isComplete&&(!usingBundle||sdpMLineIndex===0)){transceiver.iceTransport.setRemoteCandidates(cands)}else{cands.forEach(function(candidate){maybeAddCandidate(transceiver.iceTransport,candidate)})}}localCapabilities=window.RTCRtpReceiver.getCapabilities(kind);if(edgeVersion<15019){localCapabilities.codecs=localCapabilities.codecs.filter(function(codec){return codec.name!=="rtx"})}sendEncodingParameters=transceiver.sendEncodingParameters||[{ssrc:(2*sdpMLineIndex+2)*1001}];var isNewTrack=false;if(direction==="sendrecv"||direction==="sendonly"){isNewTrack=!transceiver.rtpReceiver;rtpReceiver=transceiver.rtpReceiver||new window.RTCRtpReceiver(transceiver.dtlsTransport,kind);if(isNewTrack){var stream;track=rtpReceiver.track;if(remoteMsid&&remoteMsid.stream==="-"){}else if(remoteMsid){if(!streams[remoteMsid.stream]){streams[remoteMsid.stream]=new window.MediaStream;Object.defineProperty(streams[remoteMsid.stream],"id",{get:function(){return remoteMsid.stream}})}Object.defineProperty(track,"id",{get:function(){return remoteMsid.track}});stream=streams[remoteMsid.stream]}else{if(!streams.default){streams.default=new window.MediaStream}stream=streams.default}if(stream){addTrackToStreamAndFireEvent(track,stream);transceiver.associatedRemoteMediaStreams.push(stream)}receiverList.push([track,rtpReceiver,stream])}}else if(transceiver.rtpReceiver&&transceiver.rtpReceiver.track){transceiver.associatedRemoteMediaStreams.forEach(function(s){var nativeTrack=s.getTracks().find(function(t){return t.id===transceiver.rtpReceiver.track.id});if(nativeTrack){removeTrackFromStreamAndFireEvent(nativeTrack,s)}});transceiver.associatedRemoteMediaStreams=[]}transceiver.localCapabilities=localCapabilities;transceiver.remoteCapabilities=remoteCapabilities;transceiver.rtpReceiver=rtpReceiver;transceiver.rtcpParameters=rtcpParameters;transceiver.sendEncodingParameters=sendEncodingParameters;transceiver.recvEncodingParameters=recvEncodingParameters;pc._transceive(pc.transceivers[sdpMLineIndex],false,isNewTrack)}else if(description.type==="answer"&&!rejected){transceiver=pc.transceivers[sdpMLineIndex];iceGatherer=transceiver.iceGatherer;iceTransport=transceiver.iceTransport;dtlsTransport=transceiver.dtlsTransport;rtpReceiver=transceiver.rtpReceiver;sendEncodingParameters=transceiver.sendEncodingParameters;localCapabilities=transceiver.localCapabilities;pc.transceivers[sdpMLineIndex].recvEncodingParameters=recvEncodingParameters;pc.transceivers[sdpMLineIndex].remoteCapabilities=remoteCapabilities;pc.transceivers[sdpMLineIndex].rtcpParameters=rtcpParameters;if(cands.length&&iceTransport.state==="new"){if((isIceLite||isComplete)&&(!usingBundle||sdpMLineIndex===0)){iceTransport.setRemoteCandidates(cands)}else{cands.forEach(function(candidate){maybeAddCandidate(transceiver.iceTransport,candidate)})}}if(!usingBundle||sdpMLineIndex===0){if(iceTransport.state==="new"){iceTransport.start(iceGatherer,remoteIceParameters,"controlling")}if(dtlsTransport.state==="new"){dtlsTransport.start(remoteDtlsParameters)}}pc._transceive(transceiver,direction==="sendrecv"||direction==="recvonly",direction==="sendrecv"||direction==="sendonly");if(rtpReceiver&&(direction==="sendrecv"||direction==="sendonly")){track=rtpReceiver.track;if(remoteMsid){if(!streams[remoteMsid.stream]){streams[remoteMsid.stream]=new window.MediaStream}addTrackToStreamAndFireEvent(track,streams[remoteMsid.stream]);receiverList.push([track,rtpReceiver,streams[remoteMsid.stream]])}else{if(!streams.default){streams.default=new window.MediaStream}addTrackToStreamAndFireEvent(track,streams.default);receiverList.push([track,rtpReceiver,streams.default])}}else{delete transceiver.rtpReceiver}}});if(pc._dtlsRole===undefined){pc._dtlsRole=description.type==="offer"?"active":"passive"}pc.remoteDescription={type:description.type,sdp:description.sdp};if(description.type==="offer"){pc._updateSignalingState("have-remote-offer")}else{pc._updateSignalingState("stable")}Object.keys(streams).forEach(function(sid){var stream=streams[sid];if(stream.getTracks().length){if(pc.remoteStreams.indexOf(stream)===-1){pc.remoteStreams.push(stream);var event=new Event("addstream");event.stream=stream;window.setTimeout(function(){pc._dispatchEvent("addstream",event)})}receiverList.forEach(function(item){var track=item[0];var receiver=item[1];if(stream.id!==item[2].id){return}fireAddTrack(pc,track,receiver,[stream])})}});receiverList.forEach(function(item){if(item[2]){return}fireAddTrack(pc,item[0],item[1],[])});window.setTimeout(function(){if(!(pc&&pc.transceivers)){return}pc.transceivers.forEach(function(transceiver){if(transceiver.iceTransport&&transceiver.iceTransport.state==="new"&&transceiver.iceTransport.getRemoteCandidates().length>0){console.warn("Timeout for addRemoteCandidate. Consider sending "+"an end-of-candidates notification");transceiver.iceTransport.addRemoteCandidate({})}})},4e3);return Promise.resolve()};RTCPeerConnection.prototype.close=function(){this.transceivers.forEach(function(transceiver){if(transceiver.iceTransport){transceiver.iceTransport.stop()}if(transceiver.dtlsTransport){transceiver.dtlsTransport.stop()}if(transceiver.rtpSender){transceiver.rtpSender.stop()}if(transceiver.rtpReceiver){transceiver.rtpReceiver.stop()}});this._isClosed=true;this._updateSignalingState("closed")};RTCPeerConnection.prototype._updateSignalingState=function(newState){this.signalingState=newState;var event=new Event("signalingstatechange");this._dispatchEvent("signalingstatechange",event)};RTCPeerConnection.prototype._maybeFireNegotiationNeeded=function(){var pc=this;if(this.signalingState!=="stable"||this.needNegotiation===true){return}this.needNegotiation=true;window.setTimeout(function(){if(pc.needNegotiation){pc.needNegotiation=false;var event=new Event("negotiationneeded");pc._dispatchEvent("negotiationneeded",event)}},0)};RTCPeerConnection.prototype._updateConnectionState=function(){var newState;var states={new:0,closed:0,connecting:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};this.transceivers.forEach(function(transceiver){states[transceiver.iceTransport.state]++;states[transceiver.dtlsTransport.state]++});states.connected+=states.completed;newState="new";if(states.failed>0){newState="failed"}else if(states.connecting>0||states.checking>0){newState="connecting"}else if(states.disconnected>0){newState="disconnected"}else if(states.new>0){newState="new"}else if(states.connected>0||states.completed>0){newState="connected"}if(newState!==this.iceConnectionState){this.iceConnectionState=newState;var event=new Event("iceconnectionstatechange");this._dispatchEvent("iceconnectionstatechange",event)}};RTCPeerConnection.prototype.createOffer=function(){var pc=this;if(pc._isClosed){return Promise.reject(makeError("InvalidStateError","Can not call createOffer after close"))}var numAudioTracks=pc.transceivers.filter(function(t){return t.kind==="audio"}).length;var numVideoTracks=pc.transceivers.filter(function(t){return t.kind==="video"}).length;var offerOptions=arguments[0];if(offerOptions){if(offerOptions.mandatory||offerOptions.optional){throw new TypeError("Legacy mandatory/optional constraints not supported.")}if(offerOptions.offerToReceiveAudio!==undefined){if(offerOptions.offerToReceiveAudio===true){numAudioTracks=1}else if(offerOptions.offerToReceiveAudio===false){numAudioTracks=0}else{numAudioTracks=offerOptions.offerToReceiveAudio}}if(offerOptions.offerToReceiveVideo!==undefined){if(offerOptions.offerToReceiveVideo===true){numVideoTracks=1}else if(offerOptions.offerToReceiveVideo===false){numVideoTracks=0}else{numVideoTracks=offerOptions.offerToReceiveVideo}}}pc.transceivers.forEach(function(transceiver){if(transceiver.kind==="audio"){numAudioTracks--;if(numAudioTracks<0){transceiver.wantReceive=false}}else if(transceiver.kind==="video"){numVideoTracks--;if(numVideoTracks<0){transceiver.wantReceive=false}}});while(numAudioTracks>0||numVideoTracks>0){if(numAudioTracks>0){pc._createTransceiver("audio");numAudioTracks--}if(numVideoTracks>0){pc._createTransceiver("video");numVideoTracks--}}var sdp=SDPUtils.writeSessionBoilerplate(pc._sdpSessionId,pc._sdpSessionVersion++);pc.transceivers.forEach(function(transceiver,sdpMLineIndex){var track=transceiver.track;var kind=transceiver.kind;var mid=transceiver.mid||SDPUtils.generateIdentifier();transceiver.mid=mid;if(!transceiver.iceGatherer){transceiver.iceGatherer=pc._createIceGatherer(sdpMLineIndex,pc.usingBundle)}var localCapabilities=window.RTCRtpSender.getCapabilities(kind);if(edgeVersion<15019){localCapabilities.codecs=localCapabilities.codecs.filter(function(codec){return codec.name!=="rtx"})}localCapabilities.codecs.forEach(function(codec){if(codec.name==="H264"&&codec.parameters["level-asymmetry-allowed"]===undefined){codec.parameters["level-asymmetry-allowed"]="1"}if(transceiver.remoteCapabilities&&transceiver.remoteCapabilities.codecs){transceiver.remoteCapabilities.codecs.forEach(function(remoteCodec){if(codec.name.toLowerCase()===remoteCodec.name.toLowerCase()&&codec.clockRate===remoteCodec.clockRate){codec.preferredPayloadType=remoteCodec.payloadType}})}});localCapabilities.headerExtensions.forEach(function(hdrExt){var remoteExtensions=transceiver.remoteCapabilities&&transceiver.remoteCapabilities.headerExtensions||[];remoteExtensions.forEach(function(rHdrExt){if(hdrExt.uri===rHdrExt.uri){hdrExt.id=rHdrExt.id}})});var sendEncodingParameters=transceiver.sendEncodingParameters||[{ssrc:(2*sdpMLineIndex+1)*1001}];if(track){if(edgeVersion>=15019&&kind==="video"&&!sendEncodingParameters[0].rtx){sendEncodingParameters[0].rtx={ssrc:sendEncodingParameters[0].ssrc+1}}}if(transceiver.wantReceive){transceiver.rtpReceiver=new window.RTCRtpReceiver(transceiver.dtlsTransport,kind)}transceiver.localCapabilities=localCapabilities;transceiver.sendEncodingParameters=sendEncodingParameters});if(pc._config.bundlePolicy!=="max-compat"){sdp+="a=group:BUNDLE "+pc.transceivers.map(function(t){return t.mid}).join(" ")+"\r\n"}sdp+="a=ice-options:trickle\r\n";pc.transceivers.forEach(function(transceiver,sdpMLineIndex){sdp+=writeMediaSection(transceiver,transceiver.localCapabilities,"offer",transceiver.stream,pc._dtlsRole);sdp+="a=rtcp-rsize\r\n";if(transceiver.iceGatherer&&pc.iceGatheringState!=="new"&&(sdpMLineIndex===0||!pc.usingBundle)){transceiver.iceGatherer.getLocalCandidates().forEach(function(cand){cand.component=1;sdp+="a="+SDPUtils.writeCandidate(cand)+"\r\n"});if(transceiver.iceGatherer.state==="completed"){sdp+="a=end-of-candidates\r\n"}}});var desc=new window.RTCSessionDescription({type:"offer",sdp:sdp});return Promise.resolve(desc)};RTCPeerConnection.prototype.createAnswer=function(){var pc=this;if(pc._isClosed){return Promise.reject(makeError("InvalidStateError","Can not call createAnswer after close"))}var sdp=SDPUtils.writeSessionBoilerplate(pc._sdpSessionId,pc._sdpSessionVersion++);if(pc.usingBundle){sdp+="a=group:BUNDLE "+pc.transceivers.map(function(t){return t.mid}).join(" ")+"\r\n"}var mediaSectionsInOffer=SDPUtils.getMediaSections(pc.remoteDescription.sdp).length;pc.transceivers.forEach(function(transceiver,sdpMLineIndex){if(sdpMLineIndex+1>mediaSectionsInOffer){return}if(transceiver.isDatachannel){sdp+="m=application 0 DTLS/SCTP 5000\r\n"+"c=IN IP4 0.0.0.0\r\n"+"a=mid:"+transceiver.mid+"\r\n";return}if(transceiver.stream){var localTrack;if(transceiver.kind==="audio"){localTrack=transceiver.stream.getAudioTracks()[0]}else if(transceiver.kind==="video"){localTrack=transceiver.stream.getVideoTracks()[0]}if(localTrack){if(edgeVersion>=15019&&transceiver.kind==="video"&&!transceiver.sendEncodingParameters[0].rtx){transceiver.sendEncodingParameters[0].rtx={ssrc:transceiver.sendEncodingParameters[0].ssrc+1}}}}var commonCapabilities=getCommonCapabilities(transceiver.localCapabilities,transceiver.remoteCapabilities);var hasRtx=commonCapabilities.codecs.filter(function(c){return c.name.toLowerCase()==="rtx"}).length;if(!hasRtx&&transceiver.sendEncodingParameters[0].rtx){delete transceiver.sendEncodingParameters[0].rtx}sdp+=writeMediaSection(transceiver,commonCapabilities,"answer",transceiver.stream,pc._dtlsRole);if(transceiver.rtcpParameters&&transceiver.rtcpParameters.reducedSize){sdp+="a=rtcp-rsize\r\n"}});var desc=new window.RTCSessionDescription({type:"answer",sdp:sdp});return Promise.resolve(desc)};RTCPeerConnection.prototype.addIceCandidate=function(candidate){var pc=this;var sections;if(candidate&&!(candidate.sdpMLineIndex!==undefined||candidate.sdpMid)){return Promise.reject(new TypeError("sdpMLineIndex or sdpMid required"))}return new Promise(function(resolve,reject){if(!pc.remoteDescription){return reject(makeError("InvalidStateError","Can not add ICE candidate without a remote description"))}else if(!candidate||candidate.candidate===""){for(var j=0;j<pc.transceivers.length;j++){if(pc.transceivers[j].isDatachannel){continue}pc.transceivers[j].iceTransport.addRemoteCandidate({});sections=SDPUtils.getMediaSections(pc.remoteDescription.sdp);sections[j]+="a=end-of-candidates\r\n";pc.remoteDescription.sdp=SDPUtils.getDescription(pc.remoteDescription.sdp)+sections.join("");if(pc.usingBundle){break}}}else{var sdpMLineIndex=candidate.sdpMLineIndex;if(candidate.sdpMid){for(var i=0;i<pc.transceivers.length;i++){if(pc.transceivers[i].mid===candidate.sdpMid){sdpMLineIndex=i;break}}}var transceiver=pc.transceivers[sdpMLineIndex];if(transceiver){if(transceiver.isDatachannel){return resolve()}var cand=Object.keys(candidate.candidate).length>0?SDPUtils.parseCandidate(candidate.candidate):{};if(cand.protocol==="tcp"&&(cand.port===0||cand.port===9)){return resolve()}if(cand.component&&cand.component!==1){return resolve()}if(sdpMLineIndex===0||sdpMLineIndex>0&&transceiver.iceTransport!==pc.transceivers[0].iceTransport){if(!maybeAddCandidate(transceiver.iceTransport,cand)){return reject(makeError("OperationError","Can not add ICE candidate"))}}var candidateString=candidate.candidate.trim();if(candidateString.indexOf("a=")===0){candidateString=candidateString.substr(2)}sections=SDPUtils.getMediaSections(pc.remoteDescription.sdp);sections[sdpMLineIndex]+="a="+(cand.type?candidateString:"end-of-candidates")+"\r\n";pc.remoteDescription.sdp=sections.join("")}else{return reject(makeError("OperationError","Can not add ICE candidate"))}}resolve()})};RTCPeerConnection.prototype.getStats=function(){var promises=[];this.transceivers.forEach(function(transceiver){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach(function(method){if(transceiver[method]){promises.push(transceiver[method].getStats())}})});var fixStatsType=function(stat){return{inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[stat.type]||stat.type};return new Promise(function(resolve){var results=new Map;Promise.all(promises).then(function(res){res.forEach(function(result){Object.keys(result).forEach(function(id){result[id].type=fixStatsType(result[id]);results.set(id,result[id])})});resolve(results)})})};var methods=["createOffer","createAnswer"];methods.forEach(function(method){var nativeMethod=RTCPeerConnection.prototype[method];RTCPeerConnection.prototype[method]=function(){var args=arguments;if(typeof args[0]==="function"||typeof args[1]==="function"){return nativeMethod.apply(this,[arguments[2]]).then(function(description){if(typeof args[0]==="function"){args[0].apply(null,[description])}},function(error){if(typeof args[1]==="function"){args[1].apply(null,[error])}})}return nativeMethod.apply(this,arguments)}});methods=["setLocalDescription","setRemoteDescription","addIceCandidate"];methods.forEach(function(method){var nativeMethod=RTCPeerConnection.prototype[method];RTCPeerConnection.prototype[method]=function(){var args=arguments;if(typeof args[1]==="function"||typeof args[2]==="function"){return nativeMethod.apply(this,arguments).then(function(){if(typeof args[1]==="function"){args[1].apply(null)}},function(error){if(typeof args[2]==="function"){args[2].apply(null,[error])}})}return nativeMethod.apply(this,arguments)}});["getStats"].forEach(function(method){var nativeMethod=RTCPeerConnection.prototype[method];RTCPeerConnection.prototype[method]=function(){var args=arguments;if(typeof args[1]==="function"){return nativeMethod.apply(this,arguments).then(function(){if(typeof args[1]==="function"){args[1].apply(null)}})}return nativeMethod.apply(this,arguments)}});return RTCPeerConnection}},{sdp:46}],46:[function(require,module,exports){"use strict";var SDPUtils={};SDPUtils.generateIdentifier=function(){return Math.random().toString(36).substr(2,10)};SDPUtils.localCName=SDPUtils.generateIdentifier();SDPUtils.splitLines=function(blob){return blob.trim().split("\n").map(function(line){return line.trim()})};SDPUtils.splitSections=function(blob){var parts=blob.split("\nm=");return parts.map(function(part,index){return(index>0?"m="+part:part).trim()+"\r\n"})};SDPUtils.getDescription=function(blob){var sections=SDPUtils.splitSections(blob);return sections&&sections[0]};SDPUtils.getMediaSections=function(blob){var sections=SDPUtils.splitSections(blob);sections.shift();return sections};SDPUtils.matchPrefix=function(blob,prefix){return SDPUtils.splitLines(blob).filter(function(line){return line.indexOf(prefix)===0})};SDPUtils.parseCandidate=function(line){var parts;if(line.indexOf("a=candidate:")===0){parts=line.substring(12).split(" ")}else{parts=line.substring(10).split(" ")}var candidate={foundation:parts[0],component:parseInt(parts[1],10),protocol:parts[2].toLowerCase(),priority:parseInt(parts[3],10),ip:parts[4],address:parts[4],port:parseInt(parts[5],10),type:parts[7]};for(var i=8;i<parts.length;i+=2){switch(parts[i]){case"raddr":candidate.relatedAddress=parts[i+1];break;case"rport":candidate.relatedPort=parseInt(parts[i+1],10);break;case"tcptype":candidate.tcpType=parts[i+1];break;case"ufrag":candidate.ufrag=parts[i+1];candidate.usernameFragment=parts[i+1];break;default:candidate[parts[i]]=parts[i+1];break}}return candidate};SDPUtils.writeCandidate=function(candidate){var sdp=[];sdp.push(candidate.foundation);sdp.push(candidate.component);sdp.push(candidate.protocol.toUpperCase());sdp.push(candidate.priority);sdp.push(candidate.address||candidate.ip);sdp.push(candidate.port);var type=candidate.type;sdp.push("typ");sdp.push(type);if(type!=="host"&&candidate.relatedAddress&&candidate.relatedPort){sdp.push("raddr");sdp.push(candidate.relatedAddress);sdp.push("rport");sdp.push(candidate.relatedPort)}if(candidate.tcpType&&candidate.protocol.toLowerCase()==="tcp"){sdp.push("tcptype");sdp.push(candidate.tcpType)}if(candidate.usernameFragment||candidate.ufrag){sdp.push("ufrag");sdp.push(candidate.usernameFragment||candidate.ufrag)}return"candidate:"+sdp.join(" ")};SDPUtils.parseIceOptions=function(line){return line.substr(14).split(" ")};SDPUtils.parseRtpMap=function(line){var parts=line.substr(9).split(" ");var parsed={payloadType:parseInt(parts.shift(),10)};parts=parts[0].split("/");parsed.name=parts[0];parsed.clockRate=parseInt(parts[1],10);parsed.channels=parts.length===3?parseInt(parts[2],10):1;parsed.numChannels=parsed.channels;return parsed};SDPUtils.writeRtpMap=function(codec){var pt=codec.payloadType;if(codec.preferredPayloadType!==undefined){pt=codec.preferredPayloadType}var channels=codec.channels||codec.numChannels||1;return"a=rtpmap:"+pt+" "+codec.name+"/"+codec.clockRate+(channels!==1?"/"+channels:"")+"\r\n"};SDPUtils.parseExtmap=function(line){var parts=line.substr(9).split(" ");return{id:parseInt(parts[0],10),direction:parts[0].indexOf("/")>0?parts[0].split("/")[1]:"sendrecv",uri:parts[1]}};SDPUtils.writeExtmap=function(headerExtension){return"a=extmap:"+(headerExtension.id||headerExtension.preferredId)+(headerExtension.direction&&headerExtension.direction!=="sendrecv"?"/"+headerExtension.direction:"")+" "+headerExtension.uri+"\r\n"};SDPUtils.parseFmtp=function(line){var parsed={};var kv;var parts=line.substr(line.indexOf(" ")+1).split(";");for(var j=0;j<parts.length;j++){kv=parts[j].trim().split("=");parsed[kv[0].trim()]=kv[1]}return parsed};SDPUtils.writeFmtp=function(codec){var line="";var pt=codec.payloadType;if(codec.preferredPayloadType!==undefined){pt=codec.preferredPayloadType}if(codec.parameters&&Object.keys(codec.parameters).length){var params=[];Object.keys(codec.parameters).forEach(function(param){if(codec.parameters[param]){params.push(param+"="+codec.parameters[param])}else{params.push(param)}});line+="a=fmtp:"+pt+" "+params.join(";")+"\r\n"}return line};SDPUtils.parseRtcpFb=function(line){var parts=line.substr(line.indexOf(" ")+1).split(" ");return{type:parts.shift(),parameter:parts.join(" ")}};SDPUtils.writeRtcpFb=function(codec){var lines="";var pt=codec.payloadType;if(codec.preferredPayloadType!==undefined){pt=codec.preferredPayloadType}if(codec.rtcpFeedback&&codec.rtcpFeedback.length){codec.rtcpFeedback.forEach(function(fb){lines+="a=rtcp-fb:"+pt+" "+fb.type+(fb.parameter&&fb.parameter.length?" "+fb.parameter:"")+"\r\n"})}return lines};SDPUtils.parseSsrcMedia=function(line){var sp=line.indexOf(" ");var parts={ssrc:parseInt(line.substr(7,sp-7),10)};var colon=line.indexOf(":",sp);if(colon>-1){parts.attribute=line.substr(sp+1,colon-sp-1);parts.value=line.substr(colon+1)}else{parts.attribute=line.substr(sp+1)}return parts};SDPUtils.parseSsrcGroup=function(line){var parts=line.substr(13).split(" ");return{semantics:parts.shift(),ssrcs:parts.map(function(ssrc){return parseInt(ssrc,10)})}};SDPUtils.getMid=function(mediaSection){var mid=SDPUtils.matchPrefix(mediaSection,"a=mid:")[0];if(mid){return mid.substr(6)}};SDPUtils.parseFingerprint=function(line){var parts=line.substr(14).split(" ");return{algorithm:parts[0].toLowerCase(),value:parts[1]}};SDPUtils.getDtlsParameters=function(mediaSection,sessionpart){var lines=SDPUtils.matchPrefix(mediaSection+sessionpart,"a=fingerprint:");return{role:"auto",fingerprints:lines.map(SDPUtils.parseFingerprint)}};SDPUtils.writeDtlsParameters=function(params,setupType){var sdp="a=setup:"+setupType+"\r\n";params.fingerprints.forEach(function(fp){sdp+="a=fingerprint:"+fp.algorithm+" "+fp.value+"\r\n"});return sdp};SDPUtils.parseCryptoLine=function(line){var parts=line.substr(9).split(" ");return{tag:parseInt(parts[0],10),cryptoSuite:parts[1],keyParams:parts[2],sessionParams:parts.slice(3)}};SDPUtils.writeCryptoLine=function(parameters){return"a=crypto:"+parameters.tag+" "+parameters.cryptoSuite+" "+(typeof parameters.keyParams==="object"?SDPUtils.writeCryptoKeyParams(parameters.keyParams):parameters.keyParams)+(parameters.sessionParams?" "+parameters.sessionParams.join(" "):"")+"\r\n"};SDPUtils.parseCryptoKeyParams=function(keyParams){if(keyParams.indexOf("inline:")!==0){return null}var parts=keyParams.substr(7).split("|");return{keyMethod:"inline",keySalt:parts[0],lifeTime:parts[1],mkiValue:parts[2]?parts[2].split(":")[0]:undefined,mkiLength:parts[2]?parts[2].split(":")[1]:undefined}};SDPUtils.writeCryptoKeyParams=function(keyParams){return keyParams.keyMethod+":"+keyParams.keySalt+(keyParams.lifeTime?"|"+keyParams.lifeTime:"")+(keyParams.mkiValue&&keyParams.mkiLength?"|"+keyParams.mkiValue+":"+keyParams.mkiLength:"")};SDPUtils.getCryptoParameters=function(mediaSection,sessionpart){var lines=SDPUtils.matchPrefix(mediaSection+sessionpart,"a=crypto:");return lines.map(SDPUtils.parseCryptoLine)};SDPUtils.getIceParameters=function(mediaSection,sessionpart){var ufrag=SDPUtils.matchPrefix(mediaSection+sessionpart,"a=ice-ufrag:")[0];var pwd=SDPUtils.matchPrefix(mediaSection+sessionpart,"a=ice-pwd:")[0];if(!(ufrag&&pwd)){return null}return{usernameFragment:ufrag.substr(12),password:pwd.substr(10)}};SDPUtils.writeIceParameters=function(params){return"a=ice-ufrag:"+params.usernameFragment+"\r\n"+"a=ice-pwd:"+params.password+"\r\n"};SDPUtils.parseRtpParameters=function(mediaSection){var description={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]};var lines=SDPUtils.splitLines(mediaSection);var mline=lines[0].split(" ");for(var i=3;i<mline.length;i++){var pt=mline[i];var rtpmapline=SDPUtils.matchPrefix(mediaSection,"a=rtpmap:"+pt+" ")[0];if(rtpmapline){var codec=SDPUtils.parseRtpMap(rtpmapline);var fmtps=SDPUtils.matchPrefix(mediaSection,"a=fmtp:"+pt+" ");codec.parameters=fmtps.length?SDPUtils.parseFmtp(fmtps[0]):{};codec.rtcpFeedback=SDPUtils.matchPrefix(mediaSection,"a=rtcp-fb:"+pt+" ").map(SDPUtils.parseRtcpFb);description.codecs.push(codec);switch(codec.name.toUpperCase()){case"RED":case"ULPFEC":description.fecMechanisms.push(codec.name.toUpperCase());break;default:break}}}SDPUtils.matchPrefix(mediaSection,"a=extmap:").forEach(function(line){description.headerExtensions.push(SDPUtils.parseExtmap(line))});return description};SDPUtils.writeRtpDescription=function(kind,caps){var sdp="";sdp+="m="+kind+" ";sdp+=caps.codecs.length>0?"9":"0";sdp+=" UDP/TLS/RTP/SAVPF ";sdp+=caps.codecs.map(function(codec){if(codec.preferredPayloadType!==undefined){return codec.preferredPayloadType}return codec.payloadType}).join(" ")+"\r\n";sdp+="c=IN IP4 0.0.0.0\r\n";sdp+="a=rtcp:9 IN IP4 0.0.0.0\r\n";caps.codecs.forEach(function(codec){sdp+=SDPUtils.writeRtpMap(codec);sdp+=SDPUtils.writeFmtp(codec);sdp+=SDPUtils.writeRtcpFb(codec)});var maxptime=0;caps.codecs.forEach(function(codec){if(codec.maxptime>maxptime){maxptime=codec.maxptime}});if(maxptime>0){sdp+="a=maxptime:"+maxptime+"\r\n"}sdp+="a=rtcp-mux\r\n";if(caps.headerExtensions){caps.headerExtensions.forEach(function(extension){sdp+=SDPUtils.writeExtmap(extension)})}return sdp};SDPUtils.parseRtpEncodingParameters=function(mediaSection){var encodingParameters=[];var description=SDPUtils.parseRtpParameters(mediaSection);var hasRed=description.fecMechanisms.indexOf("RED")!==-1;var hasUlpfec=description.fecMechanisms.indexOf("ULPFEC")!==-1;var ssrcs=SDPUtils.matchPrefix(mediaSection,"a=ssrc:").map(function(line){return SDPUtils.parseSsrcMedia(line)}).filter(function(parts){return parts.attribute==="cname"});var primarySsrc=ssrcs.length>0&&ssrcs[0].ssrc;var secondarySsrc;var flows=SDPUtils.matchPrefix(mediaSection,"a=ssrc-group:FID").map(function(line){var parts=line.substr(17).split(" ");return parts.map(function(part){return parseInt(part,10)})});if(flows.length>0&&flows[0].length>1&&flows[0][0]===primarySsrc){secondarySsrc=flows[0][1]}description.codecs.forEach(function(codec){if(codec.name.toUpperCase()==="RTX"&&codec.parameters.apt){var encParam={ssrc:primarySsrc,codecPayloadType:parseInt(codec.parameters.apt,10)};if(primarySsrc&&secondarySsrc){encParam.rtx={ssrc:secondarySsrc}}encodingParameters.push(encParam);if(hasRed){encParam=JSON.parse(JSON.stringify(encParam));encParam.fec={ssrc:primarySsrc,mechanism:hasUlpfec?"red+ulpfec":"red"};encodingParameters.push(encParam)}}});if(encodingParameters.length===0&&primarySsrc){encodingParameters.push({ssrc:primarySsrc})}var bandwidth=SDPUtils.matchPrefix(mediaSection,"b=");if(bandwidth.length){if(bandwidth[0].indexOf("b=TIAS:")===0){bandwidth=parseInt(bandwidth[0].substr(7),10)}else if(bandwidth[0].indexOf("b=AS:")===0){bandwidth=parseInt(bandwidth[0].substr(5),10)*1e3*.95-50*40*8}else{bandwidth=undefined}encodingParameters.forEach(function(params){params.maxBitrate=bandwidth})}return encodingParameters};SDPUtils.parseRtcpParameters=function(mediaSection){var rtcpParameters={};var remoteSsrc=SDPUtils.matchPrefix(mediaSection,"a=ssrc:").map(function(line){return SDPUtils.parseSsrcMedia(line)}).filter(function(obj){return obj.attribute==="cname"})[0];if(remoteSsrc){rtcpParameters.cname=remoteSsrc.value;rtcpParameters.ssrc=remoteSsrc.ssrc}var rsize=SDPUtils.matchPrefix(mediaSection,"a=rtcp-rsize");rtcpParameters.reducedSize=rsize.length>0;rtcpParameters.compound=rsize.length===0;var mux=SDPUtils.matchPrefix(mediaSection,"a=rtcp-mux");rtcpParameters.mux=mux.length>0;return rtcpParameters};SDPUtils.parseMsid=function(mediaSection){var parts;var spec=SDPUtils.matchPrefix(mediaSection,"a=msid:");if(spec.length===1){parts=spec[0].substr(7).split(" ");return{stream:parts[0],track:parts[1]}}var planB=SDPUtils.matchPrefix(mediaSection,"a=ssrc:").map(function(line){return SDPUtils.parseSsrcMedia(line)}).filter(function(msidParts){return msidParts.attribute==="msid"});if(planB.length>0){parts=planB[0].value.split(" ");return{stream:parts[0],track:parts[1]}}};SDPUtils.parseSctpDescription=function(mediaSection){var mline=SDPUtils.parseMLine(mediaSection);var maxSizeLine=SDPUtils.matchPrefix(mediaSection,"a=max-message-size:");var maxMessageSize;if(maxSizeLine.length>0){maxMessageSize=parseInt(maxSizeLine[0].substr(19),10)}if(isNaN(maxMessageSize)){maxMessageSize=65536}var sctpPort=SDPUtils.matchPrefix(mediaSection,"a=sctp-port:");if(sctpPort.length>0){return{port:parseInt(sctpPort[0].substr(12),10),protocol:mline.fmt,maxMessageSize:maxMessageSize}}var sctpMapLines=SDPUtils.matchPrefix(mediaSection,"a=sctpmap:");if(sctpMapLines.length>0){var parts=SDPUtils.matchPrefix(mediaSection,"a=sctpmap:")[0].substr(10).split(" ");return{port:parseInt(parts[0],10),protocol:parts[1],maxMessageSize:maxMessageSize}}};SDPUtils.writeSctpDescription=function(media,sctp){var output=[];if(media.protocol!=="DTLS/SCTP"){output=["m="+media.kind+" 9 "+media.protocol+" "+sctp.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+sctp.port+"\r\n"]}else{output=["m="+media.kind+" 9 "+media.protocol+" "+sctp.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+sctp.port+" "+sctp.protocol+" 65535\r\n"]}if(sctp.maxMessageSize!==undefined){output.push("a=max-message-size:"+sctp.maxMessageSize+"\r\n")}return output.join("")};SDPUtils.generateSessionId=function(){return Math.random().toString().substr(2,21)};SDPUtils.writeSessionBoilerplate=function(sessId,sessVer,sessUser){var sessionId;var version=sessVer!==undefined?sessVer:2;if(sessId){sessionId=sessId}else{sessionId=SDPUtils.generateSessionId()}var user=sessUser||"thisisadapterortc";return"v=0\r\n"+"o="+user+" "+sessionId+" "+version+" IN IP4 127.0.0.1\r\n"+"s=-\r\n"+"t=0 0\r\n"};SDPUtils.writeMediaSection=function(transceiver,caps,type,stream){var sdp=SDPUtils.writeRtpDescription(transceiver.kind,caps);sdp+=SDPUtils.writeIceParameters(transceiver.iceGatherer.getLocalParameters());sdp+=SDPUtils.writeDtlsParameters(transceiver.dtlsTransport.getLocalParameters(),type==="offer"?"actpass":"active");sdp+="a=mid:"+transceiver.mid+"\r\n";if(transceiver.direction){sdp+="a="+transceiver.direction+"\r\n"}else if(transceiver.rtpSender&&transceiver.rtpReceiver){sdp+="a=sendrecv\r\n"}else if(transceiver.rtpSender){sdp+="a=sendonly\r\n"}else if(transceiver.rtpReceiver){sdp+="a=recvonly\r\n"}else{sdp+="a=inactive\r\n"}if(transceiver.rtpSender){var msid="msid:"+stream.id+" "+transceiver.rtpSender.track.id+"\r\n";sdp+="a="+msid;sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].ssrc+" "+msid;if(transceiver.sendEncodingParameters[0].rtx){sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].rtx.ssrc+" "+msid;sdp+="a=ssrc-group:FID "+transceiver.sendEncodingParameters[0].ssrc+" "+transceiver.sendEncodingParameters[0].rtx.ssrc+"\r\n"}}sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].ssrc+" cname:"+SDPUtils.localCName+"\r\n";if(transceiver.rtpSender&&transceiver.sendEncodingParameters[0].rtx){sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].rtx.ssrc+" cname:"+SDPUtils.localCName+"\r\n"}return sdp};SDPUtils.getDirection=function(mediaSection,sessionpart){var lines=SDPUtils.splitLines(mediaSection);for(var i=0;i<lines.length;i++){switch(lines[i]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return lines[i].substr(2);default:}}if(sessionpart){return SDPUtils.getDirection(sessionpart)}return"sendrecv"};SDPUtils.getKind=function(mediaSection){var lines=SDPUtils.splitLines(mediaSection);var mline=lines[0].split(" ");return mline[0].substr(2)};SDPUtils.isRejected=function(mediaSection){return mediaSection.split(" ",2)[1]==="0"};SDPUtils.parseMLine=function(mediaSection){var lines=SDPUtils.splitLines(mediaSection);var parts=lines[0].substr(2).split(" ");return{kind:parts[0],port:parseInt(parts[1],10),protocol:parts[2],fmt:parts.slice(3).join(" ")}};SDPUtils.parseOLine=function(mediaSection){var line=SDPUtils.matchPrefix(mediaSection,"o=")[0];var parts=line.substr(2).split(" ");return{username:parts[0],sessionId:parts[1],sessionVersion:parseInt(parts[2],10),netType:parts[3],addressType:parts[4],address:parts[5]}};SDPUtils.isValidSDP=function(blob){if(typeof blob!=="string"||blob.length===0){return false}var lines=SDPUtils.splitLines(blob);for(var i=0;i<lines.length;i++){if(lines[i].length<2||lines[i].charAt(1)!=="="){return false}}return true};if(typeof module==="object"){module.exports=SDPUtils}},{}]},{},[1]);var Voice=bundle(1);if(typeof define==="function"&&define.amd){define([],function(){return Voice})}else{var Twilio=root.Twilio=root.Twilio||{};Twilio.Call=Twilio.Call||Voice.Call;Twilio.Device=Twilio.Device||Voice.Device;Twilio.PStream=Twilio.PStream||Voice.PStream;Twilio.PreflightTest=Twilio.PreflightTest||Voice.PreflightTest;Twilio.Logger=Twilio.Logger||Voice.Logger}})(typeof window!=="undefined"?window:typeof global!=="undefined"?global:this);