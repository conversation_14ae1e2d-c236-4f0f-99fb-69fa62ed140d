<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<link rel="shortcut icon" href="data:image/x-icon;," type="image/x-icon" />
	<link id="favicon1" rel="icon" type="image/png" sizes="32x32" href="./media/favicon-32x32.png" />
	<link id="favicon2" rel="icon" type="image/png" sizes="16x16" href="./media/favicon-16x16.png" />
	<link id="favicon3" rel="icon" href="./media/favicon.ico" />
	<style>
	html {
		border:0;
		margin:0;
		outline:0;
		overflow: hidden;
	}

	video {

		margin: 0;
		padding: 0;
		overflow: hidden;
		cursor: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=), none;
		user-select: none;
		
	}
	body {
		padding: 0 0px;
		height: 100%;
		width: 100%;
		background-color: -webkit-linear-gradient(to top, #363644, 50%, #151b29);  /* Chrome 10-25, Safari 5.1-6 */
		background: linear-gradient(to top, #363644, 50%, #151b29); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
		font-size: 2em;
		font-family: Helvetica, Arial, sans-serif;
		display: flex;
		flex-flow: column;
		border:0;
		margin:0;
		outline:0;
		overflow-y: auto;
		overflow-x: hidden;
	}

	button.glyphicon-button:focus,
	button.glyphicon-button:active:focus,
	button.glyphicon-button.active:focus,
	button.glyphicon-button.focus,
	button.glyphicon-button:active.focus,
	button.glyphicon-button.active.focus {
	  outline: none !important;
	}

	.gobutton {
		font-size:14px;
		font-weight: bold;
		border: none;
		background: #6aab23;
		display: flex;
		border-radius: 0px;
		border-top-right-radius: 10px;
		border-bottom-right-radius: 10px;
		box-shadow: 0 12px 15px -10px #5ca70b, 0 2px 0px #6aab23;
		color: white;
		cursor: pointer;
		box-sizing: border-box;
		align-items: center;
		padding: 0 1em;
		min-width: 50px;
	}
	.details{
		font-size: 14px;
		font-weight: bold;
		border: none;
		background: #555;
		display: flex;
		border-radius: 0px;
		border-top-right-radius: 10px;
		border-bottom-right-radius: 10px;
		box-shadow: 0 12px 15px -10px #444, 0 2px 0px #555;
		color: white;
		box-sizing: border-box;
		align-items: center;
		padding: 0 1em;
		min-width: 50px;
	}
	#header{
		width:100%;
		background-color: #101520;
	}
	.changeText {
		font-size: 1em;
		align-self: center;
		width: 100%;
		padding: 1em;
		font-weight: bold;
		background: white;
		border: 4px solid white;
		box-shadow: 0px 30px 40px -32px #6aab23, 0 2px 0px #6aab23;
		border-top-left-radius: 10px;
		border-bottom-left-radius: 10px;
		transition: all 0.2s linear;
		box-sizing: border-box;
		border-bottom-right-radius: 0;
		border-top-right-radius: 0;
	}

	.changeText:focus {
		outline: none;
	}
	select.changetext{
		padding: .1em;
	}

	.container{
		font-size: 16px;
		align-self:center;
		max-width: 100%;
		width: 720px;
		margin: auto auto;
	}
	label {
		font: white;
		font-size: 1em;
		color: white;
	}
	input[type='checkbox'] {
		-webkit-appearance:none;
		width:30px;
		height:30px;
		background:white;
		border-radius:5px;
		border:2px solid #555;
		cursor: pointer;
	}
	input[type='checkbox']:checked {
		background: #1A1;
	}
	#audioOutput, #lastUrls {
		font-size: calc(16px + 0.3vw);
		width: 730px;
		height: 100%;
		flex: 20;
		border-radius: 10px;
		padding: 1em;
		background: #eaeaea;
		cursor:pointer;
	}
	label[for="audioOutput"] {
		font-size: 3em;
		color: #FE53BB;
		text-shadow: 0px 0px 30px #fe53bb;
		padding-right: 10px;
	}
	label[for="changeText"] {
		font-size: 3em;
		color: #00F6FF;
		text-shadow: 0px 0px 30px #00f6ff;
		padding-top: 5px;
		padding-right: 10px;
	}

	label[for="lastUrls"] {
	font-size: 3em;
		color: #1a1;
		text-shadow: 0px 0px 30px #1a1;
		padding-right: 10px;
		cursor: pointer;
	}

	div#audioOutputContainer, #history {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		justify-content: center;
		margin: 2em;
	}
		
	@media only screen and (max-width: 1030px) {
		body{
			zoom: 0.9; 
			-moz-transform: scale(0.9); 
			-moz-transform-origin: 0 0;
		}
	}

	#messageDiv {
		font-size: .7em;
		color: #DDD;
		transition: all 0.5s linear;
		font-style: italic;
		opacity: 0;
		text-align: center;
		margin: 10px 0;
	}

	div.urlInput {
		padding: 0 0 1vh 0;
	}
	
	@media only screen and (max-height: 639px) {
		div.urlInput {
		}
		div#audioOutputContainer, #history {
			margin: 1em;
		}
	}
	
	@media only screen and (max-width: 767px) {
		
		div.urlInput {
		}
		div#audioOutputContainer, #history {
			margin:  2em 1em;
		}
	}
	
	
	@media only screen and (max-height: 380px) {
		div.urlInput {
		}
		div#audioOutputContainer, #history {
			margin: 1em;
		}
	}
	
	

	label[for="audioOutput"], label[for="lastUrls"] {
		font-size: 3em;
	}

	#warning4mac, #electronVersion {
		background: #8500f7;
		box-shadow: 0px 0px 50px 10px #8500f7ab, inset 0px 0px 10px 2px #8d08ffba;
		border: 2px solid #8500f7;
		border-radius: 10px;
		width: 90%;
		padding:1em;
		margin:0 auto;
		color:white;
		font-size:1.3em;
		margin-bottom: 20px;
	}

	#warning4mac a, #electronVersion a {
		color:white;
	 }

	 ul#lastUrls {
		list-style: none;
		background: #101520;
		color: white;
		padding: 1em;
	}

	ul#lastUrls li {
		padding: 5px 0px;
	}
	ul#lastUrls li:nth-child(even) {
		background-color: #182031;
	}

	.inputCombo {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		flex-grow: 1;
	}
	#version{
		margin: 0 auto;
		font-size: 30%;
		display: inline-block;
		color: #000A;
	}
	h3 {
		color: #b0e3ff;
	}
	.hidden{
		display:none;
		opacity:0;
		visibility:none;
		width:0;
		height:0
	}
	

	</style>
</head>
<body>
<div id="header" style="-webkit-app-region: drag; color:#6f6f6f;font-size:20px; line-height: 20px; padding: 5px 10px; letter-spacing: 3; font-weight: bold;">WHIP / WHEP simple sample setup</div>
	
<div class="container">
		
		Redirecting you to the correct page. <a href="./whip">or click here to go there now.</a>
	
</div>		
<script>window.location.href = "./whip.html";</script>

</body>
</html>