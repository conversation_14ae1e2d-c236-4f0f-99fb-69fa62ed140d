<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Bridge Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1f2e;
            color: white;
        }
        button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .result {
            background: #0a0e1a;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Simple Bridge Test</h1>
    
    <button onclick="testCreateRoom()">Test Create Room</button>
    <button onclick="testJoinRoom()">Test Join Room</button>
    <button onclick="testUtilities()">Test Utilities</button>
    
    <div id="result" class="result">Click a button to test...</div>

    <script>
        // Minimal bridge implementation for testing
        class SimpleBridge {
            constructor() {
                this.isInitialized = true;
                this.originalFunctions = {
                    sanitizeRoomName: this.sanitizeRoomName.bind(this),
                    generateString: this.generateRandomString.bind(this)
                };
            }
            
            sanitizeRoomName(roomName) {
                if (!roomName || typeof roomName !== 'string') {
                    return '';
                }
                return roomName.trim()
                    .replace(/[^a-zA-Z0-9\-_]/g, '')
                    .substring(0, 30);
            }
            
            generateRandomString(length = 10) {
                const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                let result = '';
                for (let i = 0; i < length; i++) {
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return result;
            }
            
            validateRoomSettings(settings) {
                const validated = { ...settings };
                
                if (!validated.roomName || validated.roomName.trim() === '') {
                    validated.roomName = this.generateRandomString(10);
                } else {
                    validated.roomName = this.sanitizeRoomName(validated.roomName);
                }
                
                if (validated.roomName === 'test') {
                    throw new Error('Please enter a unique room name');
                }
                
                return validated;
            }
            
            buildDirectorRoomUrl(settings) {
                const baseUrl = window.location.origin + window.location.pathname.replace(/test-simple\.html$/, 'index.html');
                const params = new URLSearchParams();
                
                params.set('director', settings.roomName);
                
                if (settings.roomPassword) {
                    params.set('password', settings.roomPassword);
                }
                
                if (settings.videoQuality && settings.videoQuality !== 'auto') {
                    params.set('quality', settings.videoQuality);
                }
                
                if (settings.audioOnly) {
                    params.set('novideo', '1');
                }
                
                return `${baseUrl}?${params.toString()}`;
            }
            
            buildJoinRoomUrl(roomId, settings) {
                const baseUrl = window.location.origin + window.location.pathname.replace(/test-simple\.html$/, 'index.html');
                const params = new URLSearchParams();
                
                params.set('room', roomId);
                
                if (settings.roomPassword) {
                    params.set('password', settings.roomPassword);
                }
                
                if (settings.displayName) {
                    params.set('label', settings.displayName);
                }
                
                if (settings.audioOnly) {
                    params.set('novideo', '1');
                }
                
                return `${baseUrl}?${params.toString()}`;
            }
            
            modernCreateRoom(settings = {}) {
                try {
                    const validatedSettings = this.validateRoomSettings(settings);
                    const roomUrl = this.buildDirectorRoomUrl(validatedSettings);
                    
                    return {
                        roomName: validatedSettings.roomName,
                        roomUrl: roomUrl,
                        success: true
                    };
                } catch (error) {
                    throw error;
                }
            }
            
            modernJoinRoom(roomId, settings = {}) {
                try {
                    const sanitizedRoomId = this.sanitizeRoomName(roomId);
                    const joinUrl = this.buildJoinRoomUrl(sanitizedRoomId, settings);
                    
                    return {
                        roomId: sanitizedRoomId,
                        joinUrl: joinUrl,
                        success: true
                    };
                } catch (error) {
                    throw error;
                }
            }
        }
        
        // Initialize bridge
        const bridge = new SimpleBridge();
        
        function testCreateRoom() {
            const result = document.getElementById('result');
            
            try {
                const settings = {
                    roomName: 'TestRoom123',
                    roomPassword: 'secret',
                    directorMode: true,
                    hdVideo: true
                };
                
                result.textContent = `Testing room creation with settings:
${JSON.stringify(settings, null, 2)}

Calling modernCreateRoom...

`;
                
                const createResult = bridge.modernCreateRoom(settings);
                
                result.textContent += `Result:
${JSON.stringify(createResult, null, 2)}`;
                
            } catch (error) {
                result.textContent += `Error: ${error.message}`;
            }
        }
        
        function testJoinRoom() {
            const result = document.getElementById('result');
            
            try {
                const roomId = 'Techspace';
                const settings = {
                    displayName: 'Dennis',
                    roomPassword: '',
                    audioOnly: false
                };
                
                result.textContent = `Testing room joining with:
Room ID: ${roomId}
Settings: ${JSON.stringify(settings, null, 2)}

Calling modernJoinRoom...

`;
                
                const joinResult = bridge.modernJoinRoom(roomId, settings);
                
                result.textContent += `Result:
${JSON.stringify(joinResult, null, 2)}`;
                
            } catch (error) {
                result.textContent += `Error: ${error.message}`;
            }
        }
        
        function testUtilities() {
            const result = document.getElementById('result');
            
            const testNames = [
                'test-room-123',
                'Room With Spaces!@#',
                'special$chars%^&*()',
                '',
                'verylongroomnamethatexceedsthirtychars',
                'normal123'
            ];
            
            let output = 'Sanitize Room Name Tests:\n\n';
            
            testNames.forEach(name => {
                const sanitized = bridge.sanitizeRoomName(name);
                output += `"${name}" -> "${sanitized}"\n`;
            });
            
            output += '\nGenerate Random String Tests:\n\n';
            
            for (let i = 0; i < 5; i++) {
                const randomString = bridge.generateRandomString(10);
                output += `Generated string ${i + 1}: "${randomString}"\n`;
            }
            
            result.textContent = output;
        }
    </script>
</body>
</html>
