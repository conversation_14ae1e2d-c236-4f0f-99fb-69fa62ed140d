<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VDO.Ninja Modern UI Test</title>
    <link rel="stylesheet" href="./modern-vdo-ninja.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-lg);
        }
    </style>
</head>
<body class="modern-theme">
    <nav class="modern-nav">
        <a href="./" class="modern-nav-brand">
            <span>V</span>DO.Ninja
        </a>
        <div class="modern-nav-actions">
            <div class="version-switcher">
                <span>Modern UI Test</span>
                <a href="index.html" class="version-switch">Switch to Classic</a>
            </div>
        </div>
    </nav>

    <div class="modern-container">
        <h1>VDO.Ninja Modern UI Test</h1>
        
        <div class="test-section">
            <h2>Landing Page Components</h2>
            <div class="action-cards">
                <div class="action-card">
                    <div class="action-card-icon">🎬</div>
                    <h3 class="action-card-title">Create a Room</h3>
                    <p class="action-card-description">Start as a director and manage multiple guests.</p>
                    <div class="modern-form-group">
                        <label class="modern-label">Room Name</label>
                        <input type="text" class="modern-input" placeholder="Enter room name">
                    </div>
                    <button class="modern-btn modern-btn-primary w-100">Enter as Director</button>
                </div>
                
                <div class="action-card">
                    <div class="action-card-icon">📹</div>
                    <h3 class="action-card-title">Add Your Camera</h3>
                    <p class="action-card-description">Share your camera directly to OBS Studio.</p>
                    <div class="device-preview">
                        <div class="device-preview-placeholder">
                            <span>Camera preview</span>
                        </div>
                    </div>
                    <button class="modern-btn modern-btn-success w-100">Start Camera</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Button Styles</h2>
            <div class="modern-grid modern-grid-4">
                <button class="modern-btn modern-btn-primary">Primary</button>
                <button class="modern-btn modern-btn-secondary">Secondary</button>
                <button class="modern-btn modern-btn-success">Success</button>
                <button class="modern-btn modern-btn-error">Error</button>
                <button class="modern-btn modern-btn-warning">Warning</button>
                <button class="modern-btn modern-btn-outline">Outline</button>
                <button class="modern-btn modern-btn-sm">Small</button>
                <button class="modern-btn modern-btn-lg">Large</button>
            </div>
        </div>

        <div class="test-section">
            <h2>Form Elements</h2>
            <div class="modern-form-row">
                <div class="modern-form-col">
                    <label class="modern-label">Text Input</label>
                    <input type="text" class="modern-input" placeholder="Enter text">
                </div>
                <div class="modern-form-col">
                    <label class="modern-label">Select</label>
                    <select class="modern-select">
                        <option>Option 1</option>
                        <option>Option 2</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Video Grid</h2>
            <div class="modern-video-grid">
                <div class="modern-video-container">
                    <div style="width: 100%; height: 100%; background: var(--secondary); display: flex; align-items: center; justify-content: center; color: white;">
                        Video 1
                    </div>
                    <div class="modern-video-overlay">
                        <span class="modern-video-name">Participant 1</span>
                        <div class="modern-video-controls">
                            <button class="modern-video-control-btn">🎤</button>
                            <button class="modern-video-control-btn">📹</button>
                        </div>
                    </div>
                </div>
                <div class="modern-video-container">
                    <div style="width: 100%; height: 100%; background: var(--secondary); display: flex; align-items: center; justify-content: center; color: white;">
                        Video 2
                    </div>
                    <div class="modern-video-overlay">
                        <span class="modern-video-name">Participant 2</span>
                        <div class="modern-video-controls">
                            <button class="modern-video-control-btn">🎤</button>
                            <button class="modern-video-control-btn">📹</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Status Indicators</h2>
            <div class="d-flex" style="gap: 1rem;">
                <div class="status-indicator status-online">Online</div>
                <div class="status-indicator status-offline">Offline</div>
                <div class="status-indicator status-connecting">Connecting</div>
            </div>
        </div>

        <div class="test-section">
            <h2>Loading State</h2>
            <div class="modern-loading">
                <div class="loading-spinner"></div>
                <span class="loading-text">Loading...</span>
            </div>
        </div>

        <div class="test-section">
            <h2>Control Buttons</h2>
            <div class="d-flex justify-center" style="gap: 1rem;">
                <button class="control-btn control-btn-mute">🎤</button>
                <button class="control-btn control-btn-video">📹</button>
                <button class="control-btn control-btn-screen">🖥️</button>
                <button class="control-btn control-btn-hangup">📞</button>
            </div>
        </div>
    </div>
</body>
</html>
