// VDO.Ninja Modern UI Enhancements
// This script adds modern UI functionality without breaking existing VDO.Ninja features

(function() {
    'use strict';
    
    // Wait for DOM and VDO.Ninja to initialize
    document.addEventListener('DOMContentLoaded', function() {
        // Give VDO.Ninja time to initialize
        setTimeout(initModernUI, 500);
    });
    
    function initModernUI() {
        console.log('Initializing Modern UI...');
        
        // Detect if we should show modern interface
        detectAndShowModernInterface();
        
        // Enhance existing elements
        enhanceExistingElements();
        
        // Add modern functionality
        addModernFunctionality();
        
        // Add mobile support
        addMobileSupport();
        
        // Setup version switching
        setupVersionSwitching();
        
        console.log('Modern UI initialized');
    }
    
    function detectAndShowModernInterface() {
        // Check URL parameters to determine which interface to show
        const urlParams = new URLSearchParams(window.location.search);
        const isDirector = urlParams.has('director');
        const isRoom = urlParams.has('room') || urlParams.has('join');
        const isView = urlParams.has('view');
        const isPush = urlParams.has('push');

        // If no specific parameters, show modern landing page
        if (!isDirector && !isRoom && !isView && !isPush) {
            showModernLanding();
        } else if (isDirector) {
            showModernDirector();
        } else if (isRoom) {
            showModernGuest();
        }
    }
    
    function showModernLanding() {
        // Hide original landing elements
        const mainMenu = document.getElementById('mainmenu');
        const infoSection = document.getElementById('info');
        
        if (mainMenu) {
            mainMenu.style.display = 'none';
        }
        if (infoSection) {
            infoSection.style.display = 'none';
        }
        
        // Show modern landing
        const modernLanding = document.getElementById('modern-landing-wrapper');
        if (modernLanding) {
            modernLanding.style.display = 'flex';
        }
    }
    
    function enhanceExistingElements() {
        // Add modern classes to existing buttons
        document.querySelectorAll('button:not(.modern-btn)').forEach(btn => {
            if (!btn.classList.contains('modern-btn')) {
                btn.classList.add('modern-btn', 'modern-btn-secondary');
            }
        });
        
        // Add modern classes to existing inputs
        document.querySelectorAll('input[type="text"], input[type="password"], select, textarea').forEach(input => {
            if (!input.classList.contains('modern-input') && !input.classList.contains('modern-select')) {
                if (input.tagName.toLowerCase() === 'select') {
                    input.classList.add('modern-select');
                } else {
                    input.classList.add('modern-input');
                }
            }
        });
        
        // Enhance video elements
        document.querySelectorAll('video').forEach(video => {
            const container = video.parentElement;
            if (container && !container.classList.contains('modern-video-container')) {
                container.classList.add('modern-video-container');
            }
        });
    }
    
    function addModernFunctionality() {
        // Modern room creation
        window.modernCreateRoom = function() {
            const roomName = document.getElementById('modern-room-name').value;
            const password = document.getElementById('modern-room-password').value;
            const broadcastFlag = document.getElementById('modern-broadcast-flag').checked;
            const directorFlag = document.getElementById('modern-director-flag').checked;
            
            if (!roomName.trim()) {
                alert('Please enter a room name');
                return;
            }
            
            // Use existing VDO.Ninja functionality
            document.getElementById('videoname1').value = roomName;
            document.getElementById('passwordRoom').value = password;
            document.getElementById('broadcastFlag').checked = broadcastFlag;
            document.getElementById('showdirectorFlag').checked = directorFlag;
            
            // Call existing createRoom function
            if (typeof createRoom === 'function') {
                createRoom();
            }
        };
        
        // Modern join room
        window.modernJoinRoom = function() {
            const roomName = document.getElementById('modern-room-name').value;
            
            if (!roomName.trim()) {
                alert('Please enter a room name');
                return;
            }
            
            // Use existing VDO.Ninja functionality
            document.getElementById('joinroomID').value = roomName;
            
            // Call existing jumptoroom function
            if (typeof jumptoroom === 'function') {
                jumptoroom();
            }
        };
        
        // Modern camera preview
        window.modernPreviewWebcam = function() {
            // Call existing previewWebcam function
            if (typeof previewWebcam === 'function') {
                previewWebcam();
            }
        };
    }
    
    function enhanceDirectorInterface() {
        // Add modern classes to director layout
        const directorLayout = document.getElementById('directorlayout');
        if (directorLayout) {
            directorLayout.classList.add('modern-director');
        }
        
        // Enhance participant controls
        enhanceParticipantControls();
    }
    
    function enhanceGuestInterface() {
        // Add modern classes to guest interface elements
        const controlButtons = document.getElementById('controlButtons');
        if (controlButtons) {
            controlButtons.classList.add('modern-comms-controls');
        }
    }
    
    function enhanceParticipantControls() {
        // Find and enhance participant control buttons
        document.querySelectorAll('[data-action-type]').forEach(button => {
            if (!button.classList.contains('modern-btn')) {
                button.classList.add('modern-btn', 'modern-btn-sm');
                
                // Add specific styling based on action type
                const actionType = button.getAttribute('data-action-type');
                if (actionType === 'hangup') {
                    button.classList.add('modern-btn-error');
                } else if (actionType === 'mute-video' || actionType === 'mute-audio') {
                    button.classList.add('modern-btn-warning');
                }
            }
        });
    }
    
    function addMobileSupport() {
        // Add touch-friendly interactions
        let touchStartY = 0;
        let touchEndY = 0;
        
        document.addEventListener('touchstart', function(e) {
            touchStartY = e.changedTouches[0].screenY;
        });
        
        document.addEventListener('touchend', function(e) {
            touchEndY = e.changedTouches[0].screenY;
            handleSwipeGesture();
        });
        
        function handleSwipeGesture() {
            const swipeThreshold = 50;
            const diff = touchStartY - touchEndY;
            
            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    // Swipe up - could be used for showing controls
                    console.log('Swipe up detected');
                } else {
                    // Swipe down - could be used for hiding controls
                    console.log('Swipe down detected');
                }
            }
        }
        
        // Improve button touch targets
        document.querySelectorAll('.modern-btn').forEach(btn => {
            btn.style.minHeight = '44px';
            btn.style.minWidth = '44px';
        });
    }
    
    function setupVersionSwitching() {
        window.switchToClassic = function() {
            const currentUrl = new URL(window.location);
            currentUrl.pathname = currentUrl.pathname.replace('app.html', 'index.html');
            window.location.href = currentUrl.toString();
        };
        
        window.switchToModern = function() {
            const currentUrl = new URL(window.location);
            currentUrl.pathname = currentUrl.pathname.replace('index.html', 'app.html');
            window.location.href = currentUrl.toString();
        };
    }
    
    // Utility functions
    function addLoadingState(element, text = 'Loading...') {
        element.innerHTML = `
            <div class="modern-loading">
                <div class="loading-spinner"></div>
                <span class="loading-text">${text}</span>
            </div>
        `;
    }
    
    function removeLoadingState(element, originalContent) {
        element.innerHTML = originalContent;
    }
    
    function showModernDirector() {
        // Hide original elements
        const directorLayout = document.getElementById('directorlayout');
        const mainMenu = document.getElementById('mainmenu');
        const infoSection = document.getElementById('info');

        if (directorLayout) directorLayout.style.display = 'none';
        if (mainMenu) mainMenu.style.display = 'none';
        if (infoSection) infoSection.style.display = 'none';

        // Show modern director interface
        const modernDirector = document.getElementById('modern-director-wrapper');
        if (modernDirector) {
            modernDirector.style.display = 'grid';

            // Update room name display
            const urlParams = new URLSearchParams(window.location.search);
            const roomName = urlParams.get('director');
            const roomDisplay = document.getElementById('modern-room-name-display');
            if (roomDisplay && roomName) {
                roomDisplay.textContent = `Room: ${roomName}`;
            }
        }

        enhanceDirectorInterface();
    }

    function showModernGuest() {
        // Hide original elements
        const mainMenu = document.getElementById('mainmenu');
        const infoSection = document.getElementById('info');
        const controlButtons = document.getElementById('controlButtons');

        if (mainMenu) mainMenu.style.display = 'none';
        if (infoSection) infoSection.style.display = 'none';

        // Show modern guest interface
        const modernGuest = document.getElementById('modern-guest-wrapper');
        if (modernGuest) {
            modernGuest.style.display = 'flex';

            // Update joining room name
            const urlParams = new URLSearchParams(window.location.search);
            const roomName = urlParams.get('room') || urlParams.get('join');
            const roomDisplay = document.getElementById('modern-joining-room-name');
            if (roomDisplay && roomName) {
                roomDisplay.textContent = `Joining: ${roomName}`;
            }
        }

        enhanceGuestInterface();
    }

    // Add modern director functions
    window.modernInviteGuests = function() {
        // Call existing invite functionality or show invite dialog
        console.log('Modern invite guests');
    };

    window.modernManageScenes = function() {
        console.log('Modern manage scenes');
    };

    window.modernRecordingControls = function() {
        console.log('Modern recording controls');
    };

    window.modernMuteAll = function() {
        console.log('Modern mute all');
    };

    window.modernVideoOffAll = function() {
        console.log('Modern video off all');
    };

    window.modernBlindAll = function() {
        console.log('Modern blind all');
    };

    window.modernHangupAll = function() {
        console.log('Modern hangup all');
    };

    // Add modern guest functions
    window.modernTestDevices = function() {
        console.log('Modern test devices');
    };

    window.modernJoinRoomFinal = function() {
        const roomName = new URLSearchParams(window.location.search).get('room') ||
                         new URLSearchParams(window.location.search).get('join');
        const displayName = document.getElementById('modern-guest-name').value;
        const password = document.getElementById('modern-guest-password').value;

        console.log('Modern join room final', { roomName, displayName, password });

        // Show communications interface
        showModernComms();
    };

    function showModernComms() {
        const modernGuest = document.getElementById('modern-guest-wrapper');
        const modernComms = document.getElementById('modern-comms-wrapper');

        if (modernGuest) modernGuest.style.display = 'none';
        if (modernComms) modernComms.style.display = 'flex';

        // Update room info
        const urlParams = new URLSearchParams(window.location.search);
        const roomName = urlParams.get('room') || urlParams.get('join');
        const roomInfo = document.getElementById('modern-room-info');
        if (roomInfo && roomName) {
            roomInfo.textContent = `Room: ${roomName}`;
        }
    }

    // Add modern communications functions
    window.modernToggleMute = function() {
        const btn = document.getElementById('modern-mute-btn');
        btn.classList.toggle('active');
        console.log('Modern toggle mute');
    };

    window.modernToggleVideo = function() {
        const btn = document.getElementById('modern-video-btn');
        btn.classList.toggle('active');
        console.log('Modern toggle video');
    };

    window.modernToggleScreen = function() {
        const btn = document.getElementById('modern-screen-btn');
        btn.classList.toggle('active');
        console.log('Modern toggle screen');
    };

    window.modernHangup = function() {
        console.log('Modern hangup');
        // Could redirect back to landing or show hangup confirmation
    };

    window.modernShowSettings = function() {
        console.log('Modern show settings');
    };

    // Export functions for global access
    window.modernUI = {
        showModernLanding,
        showModernDirector,
        showModernGuest,
        showModernComms,
        enhanceDirectorInterface,
        enhanceGuestInterface,
        addLoadingState,
        removeLoadingState
    };

})();
