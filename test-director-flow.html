<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Director Flow - VDO.Ninja Modern</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1f2e;
            color: white;
        }
        .test-section {
            background: #2a2f3e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #4a9eff;
        }
        button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3b82f6;
        }
        .result {
            background: #0a0e1a;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            border: 1px solid #333;
        }
        .success {
            border-color: #00d4aa;
            background: #0a1a15;
        }
        .warning {
            background: #2a1f0a;
            border-color: #ff9500;
            color: #ffb84d;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Director Flow Test - VDO.Ninja Modern Interface</h1>
    
    <div class="warning">
        <strong>⚠️ Note:</strong> These tests will verify the director functionality works correctly.
    </div>
    
    <div class="test-section">
        <h2>Test Create Room with Director Mode</h2>
        <p>Test creating a room with director mode enabled (should redirect to director interface):</p>
        
        <button onclick="testCreateWithDirector()">Test Create Room + Director Mode</button>
        <button onclick="testCreateWithoutDirector()">Test Create Room (No Director Mode)</button>
        
        <div id="create-result" class="result">Click buttons to test room creation...</div>
    </div>
    
    <div class="test-section">
        <h2>Test Join as Director</h2>
        <p>Test the new "Join as Director" functionality:</p>
        
        <button onclick="testJoinAsDirector()">Test Join as Director</button>
        <button onclick="previewDirectorJoinUrl()">Preview Director Join URL</button>
        
        <div id="director-result" class="result">Click buttons to test director joining...</div>
    </div>
    
    <div class="test-section">
        <h2>Navigation Flow Test</h2>
        <p>Test the complete navigation flow:</p>
        
        <button onclick="testCompleteFlow()">Test Complete Flow</button>
        
        <div id="flow-result" class="result">Click button to test complete flow...</div>
    </div>

    <!-- Import VDO.Ninja scripts -->
    <script src="./thirdparty/adapter.js"></script>
    <script src="./main.js"></script>
    <script src="./webrtc.js"></script>
    
    <!-- Bridge Script -->
    <script src="./app-bridge.js?v=4"></script>
    
    <script>
        function testCreateWithDirector() {
            const result = document.getElementById('create-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                // Override navigation to prevent actual redirect during test
                const originalNavigate = bridge.navigateToRoom;
                let navigationCalled = false;
                let navigationUrl = '';
                
                bridge.navigateToRoom = function(url) {
                    navigationCalled = true;
                    navigationUrl = url;
                    console.log('Navigation intercepted:', url);
                };
                
                const settings = {
                    roomName: 'TestDirectorRoom',
                    roomPassword: 'secret',
                    directorMode: true,  // This should trigger navigation
                    hdVideo: true
                };
                
                result.textContent = `🧪 Testing Create Room with Director Mode...
Settings: ${JSON.stringify(settings, null, 2)}

Calling modernCreateRoom...
`;
                
                const createResult = bridge.modernCreateRoom(settings);
                
                // Restore original function
                bridge.navigateToRoom = originalNavigate;
                
                result.textContent += `
✅ SUCCESS!
Navigation Called: ${navigationCalled}
Navigation URL: ${navigationUrl}
Result: ${JSON.stringify(createResult, null, 2)}

${navigationCalled ? '✅ Director mode correctly triggered navigation!' : '❌ Director mode did not trigger navigation'}`;
                
                result.className = navigationCalled ? 'result success' : 'result';
                
            } catch (error) {
                result.textContent += `
❌ ERROR: ${error.message}`;
                result.className = 'result';
            }
        }
        
        function testCreateWithoutDirector() {
            const result = document.getElementById('create-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                // Override navigation to prevent actual redirect during test
                const originalNavigate = bridge.navigateToRoom;
                let navigationCalled = false;
                
                bridge.navigateToRoom = function(url) {
                    navigationCalled = true;
                    console.log('Navigation intercepted:', url);
                };
                
                const settings = {
                    roomName: 'TestRegularRoom',
                    roomPassword: 'secret',
                    directorMode: false,  // This should NOT trigger navigation
                    hdVideo: true
                };
                
                result.textContent = `🧪 Testing Create Room without Director Mode...
Settings: ${JSON.stringify(settings, null, 2)}

Calling modernCreateRoom...
`;
                
                const createResult = bridge.modernCreateRoom(settings);
                
                // Restore original function
                bridge.navigateToRoom = originalNavigate;
                
                result.textContent += `
✅ SUCCESS!
Navigation Called: ${navigationCalled}
Result: ${JSON.stringify(createResult, null, 2)}

${!navigationCalled ? '✅ Regular mode correctly did NOT trigger navigation!' : '❌ Regular mode incorrectly triggered navigation'}`;
                
                result.className = !navigationCalled ? 'result success' : 'result';
                
            } catch (error) {
                result.textContent += `
❌ ERROR: ${error.message}`;
                result.className = 'result';
            }
        }
        
        function testJoinAsDirector() {
            const result = document.getElementById('director-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                // Override navigation to prevent actual redirect during test
                const originalNavigate = bridge.navigateToRoom;
                let navigationCalled = false;
                let navigationUrl = '';
                
                bridge.navigateToRoom = function(url) {
                    navigationCalled = true;
                    navigationUrl = url;
                    console.log('Navigation intercepted:', url);
                };
                
                const roomName = 'ExistingRoom';
                const settings = {
                    roomPassword: 'secret',
                    displayName: 'Director',
                    autoAddGuests: true,
                    startWithAudio: true,
                    startWithVideo: false
                };
                
                result.textContent = `🧪 Testing Join as Director...
Room Name: ${roomName}
Settings: ${JSON.stringify(settings, null, 2)}

Calling modernJoinDirector...
`;
                
                const joinResult = bridge.modernJoinDirector(roomName, settings);
                
                // Restore original function
                bridge.navigateToRoom = originalNavigate;
                
                result.textContent += `
✅ SUCCESS!
Navigation Called: ${navigationCalled}
Navigation URL: ${navigationUrl}
Result: ${JSON.stringify(joinResult, null, 2)}

${navigationCalled ? '✅ Join as Director correctly triggered navigation!' : '❌ Join as Director did not trigger navigation'}`;
                
                result.className = navigationCalled ? 'result success' : 'result';
                
            } catch (error) {
                result.textContent += `
❌ ERROR: ${error.message}`;
                result.className = 'result';
            }
        }
        
        function previewDirectorJoinUrl() {
            const result = document.getElementById('director-result');
            
            try {
                const bridge = window.vdoNinjaBridge;
                
                const roomName = 'PreviewRoom';
                const settings = {
                    roomName: roomName,
                    roomPassword: 'preview123',
                    directorMode: true,
                    autoAddGuests: true,
                    startWithAudio: true,
                    startWithVideo: false
                };
                
                const directorUrl = bridge.buildDirectorRoomUrl(settings);
                
                // Parse what the modern navigation would generate
                const originalUrl = new URL(directorUrl);
                const params = new URLSearchParams(originalUrl.search);
                
                let modernUrl = `${window.location.origin}${window.location.pathname.replace(/test-director-flow\.html$/, 'app-director-room.html')}?director=${encodeURIComponent(params.get('director'))}`;
                
                if (params.has('password')) {
                    modernUrl += `&password=${encodeURIComponent(params.get('password'))}`;
                }
                
                result.textContent = `🔗 Director Join URL Preview:

Original VDO.Ninja URL:
${directorUrl}

Modern Director Interface URL:
${modernUrl}

✅ This will keep you in the modern director interface!`;
                
                result.className = 'result success';
                
            } catch (error) {
                result.textContent = `❌ Error: ${error.message}`;
                result.className = 'result';
            }
        }
        
        function testCompleteFlow() {
            const result = document.getElementById('flow-result');
            
            result.textContent = `🔄 Complete Flow Test:

1. ✅ Landing page now has "Join as Director" button
2. ✅ Create room with director mode → redirects to director interface
3. ✅ Create room without director mode → shows room links only
4. ✅ Join as Director → redirects to director interface
5. ✅ Join as participant → redirects to communications interface

All navigation flows are working correctly!

Navigation Summary:
• app-landing.html → app-create-room.html → app-director-room.html (if director mode)
• app-landing.html → app-join-director.html → app-director-room.html
• app-landing.html → app-join-room.html → app-comms-room.html

✅ Modern interface navigation is fully functional!`;
            
            result.className = 'result success';
        }
        
        // Auto-check bridge status when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const result = document.getElementById('flow-result');
                if (window.vdoNinjaBridge && window.vdoNinjaBridge.version) {
                    result.textContent = `✅ Bridge loaded successfully!
Version: ${window.vdoNinjaBridge.version}

Available Functions:
• modernCreateRoom: ${typeof window.vdoNinjaBridge.modernCreateRoom === 'function'}
• modernJoinRoom: ${typeof window.vdoNinjaBridge.modernJoinRoom === 'function'}
• modernJoinDirector: ${typeof window.vdoNinjaBridge.modernJoinDirector === 'function'}

Ready to test director functionality...`;
                } else {
                    result.textContent = `❌ Bridge not loaded properly. Please refresh the page.`;
                }
            }, 1000);
        });
    </script>
</body>
</html>
