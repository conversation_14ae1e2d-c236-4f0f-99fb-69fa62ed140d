<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<style>
	html {
		border:0;
		margin:0;
		outline:0;
		overflow: hidden;
	}

	video {

		margin: 0;
		padding: 0;
		overflow: hidden;
		cursor: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=), none;
		user-select: none;
		
	}
	body {
		padding:0;
		margin:0;
		background-color:#003;
		width:100%;
		height:100%;
		background-color: -webkit-linear-gradient(to top, #363644, 50%, #151b29);  /* Chrome 10-25, Safari 5.1-6 */
		background: linear-gradient(to top, #363644, 50%, #151b29); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
		font-size: 2em;
		font-family: Helvetica, Arial, sans-serif;
		display: flex;
		flex-flow: column;
		border:0;
		outline:0;
	}

	button.glyphicon-button:focus,
	button.glyphicon-button:active:focus,
	button.glyphicon-button.active:focus,
	button.glyphicon-button.focus,
	button.glyphicon-button:active.focus,
	button.glyphicon-button.active.focus {
	  outline: none !important;
	}

	iframe {
		border:0;
		margin:0;
		padding:0;
		display:block;
		width: 100vw;
		height: calc(100vh - 100px);
		transform: rotate(0deg);
		transform-origin: 0 0;
		left: 0;
		position: absolute;
		top: 100px;
	}

	.gobutton {
		font-size:min(30px, 2vw);
		font-weight: bold;
		border: none;
		background: #6aab23;
		display: flex;
		border-radius: 0px;
		border-top-right-radius: 10px;
		border-bottom-right-radius: 10px;
		box-shadow: 0 12px 15px -10px #5ca70b, 0 2px 0px #6aab23;
		color: white;
		cursor: pointer;
		box-sizing: border-box;
		align-items: center;
		padding: 0 min(1vw, 10px);
		margin: min(1vw, 10px) 0 ;
	}
	.details{
		font-size: 14px;
		font-weight: bold;
		border: none;
		background: #555;
		display: flex;
		border-radius: 0px;
		border-top-right-radius: 10px;
		border-bottom-right-radius: 10px;
		box-shadow: 0 12px 15px -10px #444, 0 2px 0px #555;
		color: white;
		box-sizing: border-box;
		align-items: center;
		padding: 0 min(1vw, 10px);
	}
	#header{
		width:100%;
		background-color: #101520;
	}
	.changeText {
		font-size: max(1vw, 10px)
		align-self: center;
		width: 100%;
		padding: min(1vw, 10px);
		font-weight: bold;
		background: white;
		border: 4px solid white;
		box-shadow: 0px 30px 40px -32px #6aab23, 0 2px 0px #6aab23;
		border-top-left-radius: 10px;
		border-bottom-left-radius: 10px;
		transition: all 0.2s linear;
		box-sizing: border-box;
		border-bottom-right-radius: 0;
		border-top-right-radius: 0;
		margin: min(1vw, 10px) 0;
	}

	.changeText:focus {
		outline: none;
	}
	select.changetext{
		padding: .1vw;
	}

	.container{
		width:100%;
		top:0;
		position:absolute;
		left:0;
		margin: auto auto;
		height: 70px;
	}
	label {
		font: white;
		font-size: 1vw;
		color: white;
	}
	input[type='checkbox'] {
		-webkit-appearance:none;
		width:30px;
		height:30px;
		background:white;
		border-radius:5px;
		border:2px solid #555;
		cursor: pointer;
	}
	input[type='checkbox']:checked {
		background: #1A1;
	}
	#audioOutput, #lastUrls {
		font-size: calc(16px + 0.3vw);
		width: 730px;
		height: 100%;
		flex: 20;
		border-radius: 10px;
		padding: min(1vw, 10px);
		background: #eaeaea;
		cursor:pointer;
	}
	label[for="audioOutput"] {
		font-size: min(30px, 2vw);
		color: #FE53BB;
		text-shadow: 0px 0px 30px #fe53bb;
		padding-right: 10px;
	}
	label[for="changeText"] {
		font-size: min(30px, 2.5vw);
		color: #00F6FF;
		text-shadow: 0px 0px 30px #00f6ff;
		padding-right: 10px;
		margin:auto auto;
	}

	label[for="lastUrls"] {
	font-size: min(min(30px, 2vw), 2vw);
		color: #1a1;
		text-shadow: 0px 0px 30px #1a1;
		padding-right: 10px;
		cursor: pointer;
	}

	div#audioOutputContainer, #history {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		justify-content: center;
		margin: 4em;
	}
		
	#messageDiv {
		font-size: .7em;
		color: #DDD;
		transition: all 0.5s linear;
		font-style: italic;
		opacity: 0;
		text-align: center;
		margin: 10px 0;
	}

	div.urlInput {
		padding: 0 0 4vh 0;
	}
	
	

	label[for="audioOutput"], label[for="lastUrls"] {
		font-size: min(30px, 2vw);
	}

	#warning4mac, #electronVersion {
		background: #8500f7;
		box-shadow: 0px 0px 50px 10px #8500f7ab, inset 0px 0px 10px 2px #8d08ffba;
		border: 2px solid #8500f7;
		border-radius: 10px;
		width: 90%;
		padding:min(1vw, 10px);
		margin:0 auto;
		color:white;
		font-size: 1.40px;
		margin-bottom: 20px;
	}

	#warning4mac a, #electronVersion a {
		color:white;
	 }

	 ul#lastUrls {
		list-style: none;
		background: #101520;
		color: white;
		padding: min(1vw, 10px);
	}

	ul#lastUrls li {
		padding: 5px 0px;
	}
	ul#lastUrls li:nth-child(even) {
		background-color: #182031;
	}

	.inputCombo {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		flex-grow: 1;
	}
	#version{
		margin: 0 auto;
		font-size: 30%;
		display: inline-block;
		color: #000A;
	}
	h3 {
		color: #b0e3ff;
	}
	.hidden{
		display:none;
		opacity:0;
		visibility:none;
		width:0;
		height:0
	}
	.hidebutton{
		font-size:min(30px, 2vw);
		font-weight: bold;
		border: none;
		background: #ab236a;
		display: flex;
		border-radius: 10px;
		box-shadow: 0 12px 15px -10px #a70b5c, 0 2px 0px #ab236a;
		color: white;
		cursor: pointer;
		box-sizing: border-box;
		align-items: center;
		padding: 0 min(1vw, 10px);
		margin: min(1vw, 5px) 0;
	}
	
	

	</style>
</head>
<body>
	<div class="container" id="container">
		
					
	</div>
<script>
location.href = './teleprompter.html';
</script>
</body>
</html>