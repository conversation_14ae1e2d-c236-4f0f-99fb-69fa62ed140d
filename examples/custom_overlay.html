<html>
<head>
<title>Video with sensor overlayed data</title>
<meta charset="UTF-8">
<style>
body{
	padding:0;
	margin:0;
	background-color: #0000;
	overflow: hidden;
}

#vdoninja {
	border:0;
	margin:0;
	padding:0;
	display:block;
	width:100%;
	height:100%;
	position:absolute;
	top:0;
	left:0;
	animation: none;
}

#overlay{
	border:0;
	margin:0;
	padding:0;
	width:100%;
	height:100%;
	text-align:right;
	position:absolute;
	top:0;
	left:0;
	background-repeat: no-repeat;
	background-size: contain;
	background-position: center;
	display:none;
}

.fadein {
	animation: fadeIn 200ms ease-in;
	display: block!important;
}

@keyframes fadeIn {
	0% { opacity: 0; }
	20% { opacity: 0; }
	100% { opacity: 1; }
}

#start {
	margin:auto auto;
	padding:10%;
	font-size:200%;
	position:absolute;
	top:0;
	left:0;
	width:100%;
	height:100%;
	cursor:pointer;
}

#label {
    left: 6vw;
    bottom: 3.5vw;
	font-size: 4vw;
    position: absolute;
	font-family:Tahoma;
	text-shadow: 0 0 1vw white;
}

.aspect-ratio-container {
	position: relative;
	width: 100%;
	padding-bottom: 56.25%; /* 16:9 aspect ratio */
	height: 0;
	overflow: hidden;
}
</style>
</head>
<body id="main">
	<div class="aspect-ratio-container">
		<iframe id="vdoninja"></iframe>
		<div id="overlay">
			<div id="label"></div>
		</div>
		<div id="start" onclick="start();" >
			Join with this sample guest link: <a href='https://vdo.ninja/?push=testoverlay123&autostart&webcam&label' target="_blank">https://vdo.ninja/?push=testoverlay123&autostart&webcam&label</a><br /><br />
			Click anywhere to start playback in this window.
		</div>
	</div>
<script>

var overlay = document.getElementById("overlay");
var iframe = document.getElementById("vdoninja");
var label = document.getElementById("label");

function loadIframe(url){ 
	
	iframe.allow = "autoplay;camera;microphone;fullscreen;picture-in-picture;";

	if (url==""){
		url="./";
	}
	
	iframe.src = url;
	
	var eventMethod = window.addEventListener ? "addEventListener" : "attachEvent";
	var eventer = window[eventMethod];
	var messageEvent = eventMethod === "attachEvent" ? "onmessage" : "message";

	eventer(messageEvent, function (e) {
		if (e.source != iframe.contentWindow){return} // reject messages send from other iframes
		console.log(e.data);
		if (!e.data.action || !("value" in e.data)){return;}
		
		if (e.data.action=="view-connection-info"){
			if (e.data.value.label){
				overlay.style.backgroundImage = "url(../media/overlay2.png)";
				label.innerText = e.data.value.label.replace("_"," ");
			} else {
				overlay.style.backgroundImage = "url(../media/overlay1.png)";
				label.innerText = "";
			}
		} else if (e.data.action=="view-connection"){
			if (e.data.value){
				overlay.classList.add("fadein");
			} else {
				overlay.classList.remove("fadein");
				label.innerText = "";
			}
		}
		
	});
}

function start(){
	document.querySelector("#start").remove();
	loadIframe("https://vdo.ninja/?view=testoverlay123&transparent&cleanoutput&animate=0"); // vdo.ninja/?push=testoverlay123&label=Test_Label&autostart&webcam
}
if (window.obsstudio){
	start();
}

</script>
</body>
</html>