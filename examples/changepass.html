<html><body><script>
var generateHash = function (str, length=false){
	var buffer = new TextEncoder("utf-8").encode(str);
	return crypto.subtle.digest("SHA-256", buffer).then(
		function (hash) {
			hash = new Uint8Array(hash);
			if (length){
				hash = hash.slice(0, parseInt(parseInt(length)/2));
			}
			hash = toHexString(hash);
			return hash;
		}
	);
};
function toHexString(byteArray){
	return Array.prototype.map.call(byteArray, function(byte){
		return ('0' + (byte & 0xFF).toString(16)).slice(-2);
	}).join('');
}
var password = prompt("Please enter the password");
password = password.trim();
password = encodeURIComponent(password);

var salt = location.hostname;

if (location.hostname == "steveseguin.github.io"){ // allows github to be a backup ; passwords will still work
	salt = "vdo.ninja";
} else if (["vdo.ninja","rtc.ninja","versus.cam","socialstream.ninja"].includes(location.hostname.split(".").slice(-2).join("."))){ 
	salt = location.hostname.split(".").slice(-2).join("."); // official sub-domains will retain their passwords
}

// Million to one error with a hash length of 4, and still decent security if using a long randomized password.
// For more security, you can use a hash length of just 2 rather than 4; VDO.Ninja v27.10 and newer supports 1,2,3,4,5, or 6 hash lengths.
generateHash(password + salt, 4).then(function(hash) { 
	alert("hash value: "+hash)
});
</script></body></html>