/*!
 * OBS WebSocket Javascript API (obs-websocket-js) v4.0.2
 * Author: <PERSON> (haganbmj)
 * License: MIT
 * Repository: https://github.com/haganbmj/obs-websocket-js
 * Build Timestamp: 2020-09-24 01:45:11+00:00
 * Built from Commit: https://github.com/haganbmj/obs-websocket-js/commit/7a89c8ae9d02e9edaa0f878e0d93aa934e422cfe
 */
(function(e,t){'object'==typeof exports&&'object'==typeof module?module.exports=t():'function'==typeof define&&define.amd?define([],t):'object'==typeof exports?exports.OBSWebSocket=t():e.OBSWebSocket=t()})(window,function(){var t=Math.abs,n=Math.floor,o=Math.pow,e=Math.min;return function(e){function t(o){if(n[o])return n[o].exports;var r=n[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,o){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:o})},t.r=function(e){'undefined'!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:'Module'}),Object.defineProperty(e,'__esModule',{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&'object'==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(t.r(o),Object.defineProperty(o,'default',{enumerable:!0,value:e}),2&n&&'string'!=typeof e)for(var r in e)t.d(o,r,function(t){return e[t]}.bind(null,r));return o},t.n=function(e){var n=e&&e.__esModule?function(){return e['default']}:function(){return e};return t.d(n,'a',n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p='',t(t.s=0)}([function(e,t,n){function o(){return l++ +''}const r=n(1),s=n(14),i=n(15)('obs-websocket-js:Core');let l=0;e.exports=class extends r{send(e,t={}){return t=t||{},new Promise((n,r)=>{const l=o();let a;if(e||(a=s.REQUEST_TYPE_NOT_SPECIFIED),this._connected||(a=s.NOT_CONNECTED),this.once(`obs:internal:message:id-${l}`,(e,t)=>{e?(i('[send:reject] %o',e),r(e)):(i('[send:resolve] %o',t),n(t))}),!a){t['request-type']=e,t['message-id']=l,i('[send] %s %s %o',l,e,t);try{this._socket.send(JSON.stringify(t))}catch(e){a=s.SOCKET_EXCEPTION}}a&&this.emit(`obs:internal:message:id-${l}`,a)})}sendCallback(e,t={},n){n===void 0&&'function'==typeof t&&(n=t,t={}),this.send(e,t).then((...e)=>{n(null,...e)}).catch((e)=>{n(e)})}}},function(e,t,n){const o=n(2),r=n(4),s=n(5),i=n(14),l=n(15)('obs-websocket-js:Socket'),a=n(19),u=n(20);e.exports=class extends r{constructor(){super(),this._connected=!1,this._socket=void 0;const e=this.emit;this.emit=function(...t){l('[emit] %s err: %o data: %o',...t),e.apply(this,t)}}async connect(e={}){e=e||{};const t=e.address||'localhost:4444';if(this._socket)try{this._socket.close()}catch(e){l('Failed to close previous WebSocket:',e.message)}return new Promise(async(n,o)=>{try{await this._connect(t,!!e.secure),await this._authenticate(e.password),n()}catch(e){this._socket.close(),this._connected=!1,a(l,'Connection failed:',e),o(e)}})}async _connect(e,t){return new Promise((n,r)=>{let s=!1;l('Attempting to connect to: %s (secure: %s)',e,t),this._socket=new o((t?'wss://':'ws://')+e),this._socket.onerror=(e)=>{return s?(a(l,'Unknown Socket Error',e),void this.emit('error',e)):void(s=!0,a(l,'Websocket Connection failed:',e),r(i.CONNECTION_ERROR))},this._socket.onopen=()=>{s||(this._connected=!0,s=!0,l('Connection opened: %s',e),this.emit('ConnectionOpened'),n())},this._socket.onclose=()=>{this._connected=!1,l('Connection closed: %s',e),this.emit('ConnectionClosed')},this._socket.onmessage=(e)=>{l('[OnMessage]: %o',e);const t=u(JSON.parse(e.data));let n,o;'error'===t.status?n=t:o=t,t.messageId?this.emit(`obs:internal:message:id-${t.messageId}`,n,o):t.updateType?this.emit(t.updateType,o):(a(l,'Unrecognized Socket Message:',t),this.emit('message',t))}})}async _authenticate(e=''){if(!this._connected)throw i.NOT_CONNECTED;const t=await this.send('GetAuthRequired');if(!t.authRequired)return l('Authentication not Required'),this.emit('AuthenticationSuccess'),i.AUTH_NOT_REQUIRED;try{await this.send('Authenticate',{auth:s(t.salt,t.challenge,e)})}catch(t){throw l('Authentication Failure %o',t),this.emit('AuthenticationFailure'),t}l('Authentication Success'),this.emit('AuthenticationSuccess')}disconnect(){l('Disconnect requested.'),this._socket&&this._socket.close()}}},function(e,t,n){(function(t){var n=null;'undefined'==typeof WebSocket?'undefined'==typeof MozWebSocket?'undefined'==typeof t?'undefined'==typeof window?'undefined'!=typeof self&&(n=self.WebSocket||self.MozWebSocket):n=window.WebSocket||window.MozWebSocket:n=t.WebSocket||t.MozWebSocket:n=MozWebSocket:n=WebSocket,e.exports=n}).call(this,n(3))},function(e){var t=function(){return this}();try{t=t||new Function('return this')()}catch(n){'object'==typeof window&&(t=window)}e.exports=t},function(e){'use strict';function t(e){console&&console.warn&&console.warn(e)}function n(){n.init.call(this)}function o(e){return void 0===e._maxListeners?n.defaultMaxListeners:e._maxListeners}function r(e,n,r,s){var i,l,a;if('function'!=typeof r)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof r);if(l=e._events,void 0===l?(l=e._events=Object.create(null),e._eventsCount=0):(void 0!==l.newListener&&(e.emit('newListener',n,r.listener?r.listener:r),l=e._events),a=l[n]),void 0===a)a=l[n]=r,++e._eventsCount;else if('function'==typeof a?a=l[n]=s?[r,a]:[a,r]:s?a.unshift(r):a.push(r),i=o(e),0<i&&a.length>i&&!a.warned){a.warned=!0;var u=new Error('Possible EventEmitter memory leak detected. '+a.length+' '+(n+' listeners added. Use emitter.setMaxListeners() to increase limit'));u.name='MaxListenersExceededWarning',u.emitter=e,u.type=n,u.count=a.length,t(u)}return e}function s(){for(var e=[],t=0;t<arguments.length;t++)e.push(arguments[t]);this.fired||(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,g(this.listener,this.target,e))}function i(e,t,n){var o={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},r=s.bind(o);return r.listener=n,o.wrapFn=r,r}function l(e,t,n){var o=e._events;if(o===void 0)return[];var r=o[t];return void 0===r?[]:'function'==typeof r?n?[r.listener||r]:[r]:n?d(r):u(r,r.length)}function a(e){var t=this._events;if(t!==void 0){var n=t[e];if('function'==typeof n)return 1;if(void 0!==n)return n.length}return 0}function u(e,t){for(var n=Array(t),o=0;o<t;++o)n[o]=e[o];return n}function p(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function d(e){for(var t=Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}var c,f='object'==typeof Reflect?Reflect:null,g=f&&'function'==typeof f.apply?f.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};c=f&&'function'==typeof f.ownKeys?f.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var h=Number.isNaN||function(e){return e!==e};e.exports=n,n.EventEmitter=n,n.prototype._events=void 0,n.prototype._eventsCount=0,n.prototype._maxListeners=void 0;var _=10;Object.defineProperty(n,'defaultMaxListeners',{enumerable:!0,get:function(){return _},set:function(e){if('number'!=typeof e||0>e||h(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+'.');_=e}}),n.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},n.prototype.setMaxListeners=function(e){if('number'!=typeof e||0>e||h(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+'.');return this._maxListeners=e,this},n.prototype.getMaxListeners=function(){return o(this)},n.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var o='error'===e,r=this._events;if(r!==void 0)o=o&&r.error===void 0;else if(!o)return!1;if(o){var s;if(0<t.length&&(s=t[0]),s instanceof Error)throw s;var i=new Error('Unhandled error.'+(s?' ('+s.message+')':''));throw i.context=s,i}var l=r[e];if(l===void 0)return!1;if('function'==typeof l)g(l,this,t);else for(var a=l.length,p=u(l,a),n=0;n<a;++n)g(p[n],this,t);return!0},n.prototype.addListener=function(e,t){return r(this,e,t,!1)},n.prototype.on=n.prototype.addListener,n.prototype.prependListener=function(e,t){return r(this,e,t,!0)},n.prototype.once=function(e,t){if('function'!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t);return this.on(e,i(this,e,t)),this},n.prototype.prependOnceListener=function(e,t){if('function'!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t);return this.prependListener(e,i(this,e,t)),this},n.prototype.removeListener=function(e,t){var n,o,r,s,i;if('function'!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t);if(o=this._events,void 0===o)return this;if(n=o[e],void 0===n)return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete o[e],o.removeListener&&this.emit('removeListener',e,n.listener||t));else if('function'!=typeof n){for(r=-1,s=n.length-1;0<=s;s--)if(n[s]===t||n[s].listener===t){i=n[s].listener,r=s;break}if(0>r)return this;0===r?n.shift():p(n,r),1===n.length&&(o[e]=n[0]),void 0!==o.removeListener&&this.emit('removeListener',e,i||t)}return this},n.prototype.off=n.prototype.removeListener,n.prototype.removeAllListeners=function(e){var t,n,o;if(n=this._events,void 0===n)return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var r,s=Object.keys(n);for(o=0;o<s.length;++o)r=s[o],'removeListener'===r||this.removeAllListeners(r);return this.removeAllListeners('removeListener'),this._events=Object.create(null),this._eventsCount=0,this}if(t=n[e],'function'==typeof t)this.removeListener(e,t);else if(void 0!==t)for(o=t.length-1;0<=o;o--)this.removeListener(e,t[o]);return this},n.prototype.listeners=function(e){return l(this,e,!0)},n.prototype.rawListeners=function(e){return l(this,e,!1)},n.listenerCount=function(e,t){return'function'==typeof e.listenerCount?e.listenerCount(t):a.call(e,t)},n.prototype.listenerCount=a,n.prototype.eventNames=function(){return 0<this._eventsCount?c(this._events):[]}},function(e,t,n){const o=n(6);e.exports=function(e='',t='',n){const r=new o().update(n).update(e).digest('base64'),s=new o().update(r).update(t).digest('base64');return s}},function(e,t,n){function o(){this.init(),this._w=f,d.call(this,64,56)}function r(e,t,n){return n^e&(t^n)}function s(e,t,n){return e&t|n&(e|t)}function i(e){return(e>>>2|e<<30)^(e>>>13|e<<19)^(e>>>22|e<<10)}function l(e){return(e>>>6|e<<26)^(e>>>11|e<<21)^(e>>>25|e<<7)}function a(e){return(e>>>7|e<<25)^(e>>>18|e<<14)^e>>>3}function u(e){return(e>>>17|e<<15)^(e>>>19|e<<13)^e>>>10}var p=n(7),d=n(8),c=n(9).Buffer,_=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],f=Array(64);p(o,d),o.prototype.init=function(){return this._a=1779033703,this._b=3144134277,this._c=1013904242,this._d=2773480762,this._e=1359893119,this._f=2600822924,this._g=528734635,this._h=1541459225,this},o.prototype._update=function(t){for(var n=this._w,o=0|this._a,p=0|this._b,m=0|this._c,c=0|this._d,d=0|this._e,e=0|this._f,f=0|this._g,g=0|this._h,h=0;16>h;++h)n[h]=t.readInt32BE(4*h);for(;64>h;++h)n[h]=0|u(n[h-2])+n[h-7]+a(n[h-15])+n[h-16];for(var y=0;64>y;++y){var C=0|g+l(d)+r(d,e,f)+_[y]+n[y],b=0|i(o)+s(o,p,m);g=f,f=e,e=d,d=0|c+C,c=m,m=p,p=o,o=0|C+b}this._a=0|o+this._a,this._b=0|p+this._b,this._c=0|m+this._c,this._d=0|c+this._d,this._e=0|d+this._e,this._f=0|e+this._f,this._g=0|f+this._g,this._h=0|g+this._h},o.prototype._hash=function(){var e=c.allocUnsafe(32);return e.writeInt32BE(this._a,0),e.writeInt32BE(this._b,4),e.writeInt32BE(this._c,8),e.writeInt32BE(this._d,12),e.writeInt32BE(this._e,16),e.writeInt32BE(this._f,20),e.writeInt32BE(this._g,24),e.writeInt32BE(this._h,28),e},e.exports=o},function(e){e.exports='function'==typeof Object.create?function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:function(e,t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}},function(t,n,o){function r(e,t){this._block=s.alloc(e),this._finalSize=t,this._blockSize=e,this._len=0}var s=o(9).Buffer;r.prototype.update=function(t,n){'string'==typeof t&&(n=n||'utf8',t=s.from(t,n));for(var o=this._block,r=this._blockSize,l=t.length,a=this._len,u=0;u<l;){for(var p=a%r,d=e(l-u,r-p),c=0;c<d;c++)o[p+c]=t[u+c];a+=d,u+=d,0==a%r&&this._update(o)}return this._len+=l,this},r.prototype.digest=function(e){var t=this._len%this._blockSize;this._block[t]=128,this._block.fill(0,t+1),t>=this._finalSize&&(this._update(this._block),this._block.fill(0));var n=8*this._len;if(4294967295>=n)this._block.writeUInt32BE(n,this._blockSize-4);else{var o=(4294967295&n)>>>0;this._block.writeUInt32BE((n-o)/4294967296,this._blockSize-8),this._block.writeUInt32BE(o,this._blockSize-4)}this._update(this._block);var r=this._hash();return e?r.toString(e):r},r.prototype._update=function(){throw new Error('_update must be implemented by subclass')},t.exports=r},function(e,t,n){function o(e,t){for(var n in e)t[n]=e[n]}function r(e,t,n){return i(e,t,n)}var s=n(10),i=s.Buffer;i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=s:(o(s,t),t.Buffer=r),o(i,r),r.from=function(e,t,n){if('number'==typeof e)throw new TypeError('Argument must not be a number');return i(e,t,n)},r.alloc=function(e,t,n){if('number'!=typeof e)throw new TypeError('Argument must be a number');var o=i(e);return void 0===t?o.fill(0):'string'==typeof n?o.fill(t,n):o.fill(t),o},r.allocUnsafe=function(e){if('number'!=typeof e)throw new TypeError('Argument must be a number');return i(e)},r.allocUnsafeSlow=function(e){if('number'!=typeof e)throw new TypeError('Argument must be a number');return s.SlowBuffer(e)}},function(t,r,s){'use strict';(function(t){function i(){return u.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function l(e,t){if(i()<t)throw new RangeError('Invalid typed array length');return u.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=u.prototype):(null===e&&(e=new u(t)),e.length=t),e}function u(e,t,n){if(!u.TYPED_ARRAY_SUPPORT&&!(this instanceof u))return new u(e,t,n);if('number'==typeof e){if('string'==typeof t)throw new Error('If encoding is specified then the first argument must be a string');return c(this,e)}return a(this,e,t,n)}function a(e,t,n,o){if('number'==typeof t)throw new TypeError('"value" argument must not be a number');return'undefined'!=typeof ArrayBuffer&&t instanceof ArrayBuffer?h(e,t,n,o):'string'==typeof t?f(e,t,n):_(e,t)}function p(e){if('number'!=typeof e)throw new TypeError('"size" argument must be a number');else if(0>e)throw new RangeError('"size" argument must not be negative')}function d(e,t,n,o){return p(t),0>=t?l(e,t):void 0===n?l(e,t):'string'==typeof o?l(e,t).fill(n,o):l(e,t).fill(n)}function c(e,t){if(p(t),e=l(e,0>t?0:0|m(t)),!u.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function f(e,t,n){if(('string'!=typeof n||''===n)&&(n='utf8'),!u.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var o=0|y(t,n);e=l(e,o);var r=e.write(t,n);return r!==o&&(e=e.slice(0,r)),e}function g(e,t){var n=0>t.length?0:0|m(t.length);e=l(e,n);for(var o=0;o<n;o+=1)e[o]=255&t[o];return e}function h(e,t,n,o){if(t.byteLength,0>n||t.byteLength<n)throw new RangeError('\'offset\' is out of bounds');if(t.byteLength<n+(o||0))throw new RangeError('\'length\' is out of bounds');return t=void 0===n&&void 0===o?new Uint8Array(t):void 0===o?new Uint8Array(t,n):new Uint8Array(t,n,o),u.TYPED_ARRAY_SUPPORT?(e=t,e.__proto__=u.prototype):e=g(e,t),e}function _(e,t){if(u.isBuffer(t)){var n=0|m(t.length);return(e=l(e,n),0===e.length)?e:(t.copy(e,0,0,n),e)}if(t){if('undefined'!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||'length'in t)return'number'!=typeof t.length||$(t.length)?l(e,0):g(e,t);if('Buffer'===t.type&&te(t.data))return g(e,t.data)}throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.')}function m(e){if(e>=i())throw new RangeError('Attempt to allocate Buffer larger than maximum size: 0x'+i().toString(16)+' bytes');return 0|e}function y(e,t){if(u.isBuffer(e))return e.length;if('undefined'!=typeof ArrayBuffer&&'function'==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;'string'!=typeof e&&(e=''+e);var n=e.length;if(0===n)return 0;for(var o=!1;;)switch(t){case'ascii':case'latin1':case'binary':return n;case'utf8':case'utf-8':case void 0:return Q(e).length;case'ucs2':case'ucs-2':case'utf16le':case'utf-16le':return 2*n;case'hex':return n>>>1;case'base64':return H(e).length;default:if(o)return Q(e).length;t=(''+t).toLowerCase(),o=!0;}}function C(e,t,n){var o=!1;if((void 0===t||0>t)&&(t=0),t>this.length)return'';if((void 0===n||n>this.length)&&(n=this.length),0>=n)return'';if(n>>>=0,t>>>=0,n<=t)return'';for(e||(e='utf8');;)switch(e){case'hex':return x(this,t,n);case'utf8':case'utf-8':return k(this,t,n);case'ascii':return L(this,t,n);case'latin1':case'binary':return I(this,t,n);case'base64':return S(this,t,n);case'ucs2':case'ucs-2':case'utf16le':case'utf-16le':return O(this,t,n);default:if(o)throw new TypeError('Unknown encoding: '+e);e=(e+'').toLowerCase(),o=!0;}}function b(e,t,n){var o=e[t];e[t]=e[n],e[n]=o}function E(e,t,n,o,r){if(0===e.length)return-1;if('string'==typeof n?(o=n,n=0):2147483647<n?n=2147483647:-2147483648>n&&(n=-2147483648),n=+n,isNaN(n)&&(n=r?0:e.length-1),0>n&&(n=e.length+n),n>=e.length){if(r)return-1;n=e.length-1}else if(0>n)if(r)n=0;else return-1;if('string'==typeof t&&(t=u.from(t,o)),u.isBuffer(t))return 0===t.length?-1:A(e,t,n,o,r);if('number'==typeof t)return t&=255,u.TYPED_ARRAY_SUPPORT&&'function'==typeof Uint8Array.prototype.indexOf?r?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):A(e,[t],n,o,r);throw new TypeError('val must be string, number or Buffer')}function A(e,t,n,o,r){function s(e,t){return 1==l?e[t]:e.readUInt16BE(t*l)}var l=1,a=e.length,u=t.length;if(void 0!==o&&(o=(o+'').toLowerCase(),'ucs2'===o||'ucs-2'===o||'utf16le'===o||'utf-16le'===o)){if(2>e.length||2>t.length)return-1;l=2,a/=2,u/=2,n/=2}var p;if(r){var i=-1;for(p=n;p<a;p++)if(s(e,p)!==s(t,-1==i?0:p-i))-1!=i&&(p-=p-i),i=-1;else if(-1==i&&(i=p),p-i+1===u)return i*l}else for(n+u>a&&(n=a-u),p=n;0<=p;p--){for(var d=!0,c=0;c<u;c++)if(s(e,p+c)!==s(t,c)){d=!1;break}if(d)return p}return-1}function w(e,t,n,o){n=+n||0;var r=e.length-n;o?(o=+o,o>r&&(o=r)):o=r;var s=t.length;if(0!=s%2)throw new TypeError('Invalid hex string');o>s/2&&(o=s/2);for(var l,a=0;a<o;++a){if(l=parseInt(t.substr(2*a,2),16),isNaN(l))return a;e[n+a]=l}return a}function P(e,t,n,o){return G(Q(t,e.length-n),e,n,o)}function F(e,t,n,o){return G(X(t),e,n,o)}function R(e,t,n,o){return F(e,t,n,o)}function v(e,t,n,o){return G(H(t),e,n,o)}function T(e,t,n,o){return G(V(t,e.length-n),e,n,o)}function S(e,t,n){return 0===t&&n===e.length?Z.fromByteArray(e):Z.fromByteArray(e.slice(t,n))}function k(t,n,o){o=e(t.length,o);for(var r=[],s=n;s<o;){var i=t[s],l=null,a=239<i?4:223<i?3:191<i?2:1;if(s+a<=o){var u,p,d,c;1==a?128>i&&(l=i):2==a?(u=t[s+1],128==(192&u)&&(c=(31&i)<<6|63&u,127<c&&(l=c))):3==a?(u=t[s+1],p=t[s+2],128==(192&u)&&128==(192&p)&&(c=(15&i)<<12|(63&u)<<6|63&p,2047<c&&(55296>c||57343<c)&&(l=c))):4==a?(u=t[s+1],p=t[s+2],d=t[s+3],128==(192&u)&&128==(192&p)&&128==(192&d)&&(c=(15&i)<<18|(63&u)<<12|(63&p)<<6|63&d,65535<c&&1114112>c&&(l=c))):void 0}null===l?(l=65533,a=1):65535<l&&(l-=65536,r.push(55296|1023&l>>>10),l=56320|1023&l),r.push(l),s+=a}return B(r)}function B(e){var t=e.length;if(t<=ne)return J.apply(String,e);for(var n='',o=0;o<t;)n+=J.apply(String,e.slice(o,o+=ne));return n}function L(t,n,o){var r='';o=e(t.length,o);for(var s=n;s<o;++s)r+=J(127&t[s]);return r}function I(t,n,o){var r='';o=e(t.length,o);for(var s=n;s<o;++s)r+=J(t[s]);return r}function x(e,t,n){var o=e.length;(!t||0>t)&&(t=0),(!n||0>n||n>o)&&(n=o);for(var r='',s=t;s<n;++s)r+=K(e[s]);return r}function O(e,t,n){for(var o=e.slice(t,n),r='',s=0;s<o.length;s+=2)r+=J(o[s]+256*o[s+1]);return r}function U(e,t,n){if(0!=e%1||0>e)throw new RangeError('offset is not uint');if(e+t>n)throw new RangeError('Trying to access beyond buffer length')}function Y(e,t,n,o,r,s){if(!u.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>r||t<s)throw new RangeError('"value" argument is out of bounds');if(n+o>e.length)throw new RangeError('Index out of range')}function D(t,n,o,r){0>n&&(n=65535+n+1);for(var s=0,i=e(t.length-o,2);s<i;++s)t[o+s]=(n&255<<8*(r?s:1-s))>>>8*(r?s:1-s)}function N(t,n,o,r){0>n&&(n=4294967295+n+1);for(var s=0,i=e(t.length-o,4);s<i;++s)t[o+s]=255&n>>>8*(r?s:3-s)}function z(e,t,n,o){if(n+o>e.length)throw new RangeError('Index out of range');if(0>n)throw new RangeError('Index out of range')}function j(e,t,n,o,r){return r||z(e,t,n,4,3.4028234663852886e38,-3.4028234663852886e38),ee.write(e,t,n,o,23,4),n+4}function M(e,t,n,o,r){return r||z(e,t,n,8,1.7976931348623157e308,-1.7976931348623157e308),ee.write(e,t,n,o,52,8),n+8}function W(e){if(e=q(e).replace(oe,''),2>e.length)return'';for(;0!=e.length%4;)e+='=';return e}function q(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,'')}function K(e){return 16>e?'0'+e.toString(16):e.toString(16)}function Q(e,t){t=t||Infinity;for(var n,o=e.length,r=null,s=[],l=0;l<o;++l){if(n=e.charCodeAt(l),55295<n&&57344>n){if(!r){if(56319<n){-1<(t-=3)&&s.push(239,191,189);continue}else if(l+1===o){-1<(t-=3)&&s.push(239,191,189);continue}r=n;continue}if(56320>n){-1<(t-=3)&&s.push(239,191,189),r=n;continue}n=(r-55296<<10|n-56320)+65536}else r&&-1<(t-=3)&&s.push(239,191,189);if(r=null,128>n){if(0>(t-=1))break;s.push(n)}else if(2048>n){if(0>(t-=2))break;s.push(192|n>>6,128|63&n)}else if(65536>n){if(0>(t-=3))break;s.push(224|n>>12,128|63&n>>6,128|63&n)}else if(1114112>n){if(0>(t-=4))break;s.push(240|n>>18,128|63&n>>12,128|63&n>>6,128|63&n)}else throw new Error('Invalid code point')}return s}function X(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}function V(e,t){for(var n,o,r,s=[],l=0;l<e.length&&!(0>(t-=2));++l)n=e.charCodeAt(l),o=n>>8,r=n%256,s.push(r),s.push(o);return s}function H(e){return Z.toByteArray(W(e))}function G(e,t,n,o){for(var r=0;r<o&&!(r+n>=t.length||r>=e.length);++r)t[r+n]=e[r];return r}function $(e){return e!==e}var J=String.fromCharCode,Z=s(11),ee=s(12),te=s(13);r.Buffer=u,r.SlowBuffer=function(e){return+e!=e&&(e=0),u.alloc(+e)},r.INSPECT_MAX_BYTES=50,u.TYPED_ARRAY_SUPPORT=t.TYPED_ARRAY_SUPPORT===void 0?function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&'function'==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(t){return!1}}():t.TYPED_ARRAY_SUPPORT,r.kMaxLength=i(),u.poolSize=8192,u._augment=function(e){return e.__proto__=u.prototype,e},u.from=function(e,t,n){return a(null,e,t,n)},u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,'undefined'!=typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0})),u.alloc=function(e,t,n){return d(null,e,t,n)},u.allocUnsafe=function(e){return c(null,e)},u.allocUnsafeSlow=function(e){return c(null,e)},u.isBuffer=function(e){return!!(null!=e&&e._isBuffer)},u.compare=function(t,n){if(!u.isBuffer(t)||!u.isBuffer(n))throw new TypeError('Arguments must be Buffers');if(t===n)return 0;for(var o=t.length,r=n.length,s=0,i=e(o,r);s<i;++s)if(t[s]!==n[s]){o=t[s],r=n[s];break}return o<r?-1:r<o?1:0},u.isEncoding=function(e){switch((e+'').toLowerCase()){case'hex':case'utf8':case'utf-8':case'ascii':case'latin1':case'binary':case'base64':case'ucs2':case'ucs-2':case'utf16le':case'utf-16le':return!0;default:return!1;}},u.concat=function(e,t){if(!te(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return u.alloc(0);var n;if(t===void 0)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var o=u.allocUnsafe(t),r=0;for(n=0;n<e.length;++n){var s=e[n];if(!u.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(o,r),r+=s.length}return o},u.byteLength=y,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var e=this.length;if(0!=e%2)throw new RangeError('Buffer size must be a multiple of 16-bits');for(var t=0;t<e;t+=2)b(this,t,t+1);return this},u.prototype.swap32=function(){var e=this.length;if(0!=e%4)throw new RangeError('Buffer size must be a multiple of 32-bits');for(var t=0;t<e;t+=4)b(this,t,t+3),b(this,t+1,t+2);return this},u.prototype.swap64=function(){var e=this.length;if(0!=e%8)throw new RangeError('Buffer size must be a multiple of 64-bits');for(var t=0;t<e;t+=8)b(this,t,t+7),b(this,t+1,t+6),b(this,t+2,t+5),b(this,t+3,t+4);return this},u.prototype.toString=function(){var e=0|this.length;return 0==e?'':0===arguments.length?k(this,0,e):C.apply(this,arguments)},u.prototype.equals=function(e){if(!u.isBuffer(e))throw new TypeError('Argument must be a Buffer');return this===e||0===u.compare(this,e)},u.prototype.inspect=function(){var e='',t=r.INSPECT_MAX_BYTES;return 0<this.length&&(e=this.toString('hex',0,t).match(/.{2}/g).join(' '),this.length>t&&(e+=' ... ')),'<Buffer '+e+'>'},u.prototype.compare=function(t,n,o,r,s){if(!u.isBuffer(t))throw new TypeError('Argument must be a Buffer');if(void 0===n&&(n=0),void 0===o&&(o=t?t.length:0),void 0===r&&(r=0),void 0===s&&(s=this.length),0>n||o>t.length||0>r||s>this.length)throw new RangeError('out of range index');if(r>=s&&n>=o)return 0;if(r>=s)return-1;if(n>=o)return 1;if(n>>>=0,o>>>=0,r>>>=0,s>>>=0,this===t)return 0;for(var l=s-r,a=o-n,p=e(l,a),d=this.slice(r,s),c=t.slice(n,o),f=0;f<p;++f)if(d[f]!==c[f]){l=d[f],a=c[f];break}return l<a?-1:a<l?1:0},u.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},u.prototype.indexOf=function(e,t,n){return E(this,e,t,n,!0)},u.prototype.lastIndexOf=function(e,t,n){return E(this,e,t,n,!1)},u.prototype.write=function(e,t,n,o){if(void 0===t)o='utf8',n=this.length,t=0;else if(void 0===n&&'string'==typeof t)o=t,n=this.length,t=0;else if(isFinite(t))t|=0,isFinite(n)?(n|=0,void 0===o&&(o='utf8')):(o=n,n=void 0);else throw new Error('Buffer.write(string, encoding, offset[, length]) is no longer supported');var r=this.length-t;if((void 0===n||n>r)&&(n=r),0<e.length&&(0>n||0>t)||t>this.length)throw new RangeError('Attempt to write outside buffer bounds');o||(o='utf8');for(var s=!1;;)switch(o){case'hex':return w(this,e,t,n);case'utf8':case'utf-8':return P(this,e,t,n);case'ascii':return F(this,e,t,n);case'latin1':case'binary':return R(this,e,t,n);case'base64':return v(this,e,t,n);case'ucs2':case'ucs-2':case'utf16le':case'utf-16le':return T(this,e,t,n);default:if(s)throw new TypeError('Unknown encoding: '+o);o=(''+o).toLowerCase(),s=!0;}},u.prototype.toJSON=function(){return{type:'Buffer',data:Array.prototype.slice.call(this._arr||this,0)}};var ne=4096;u.prototype.slice=function(e,t){var n=this.length;e=~~e,t=t===void 0?n:~~t,0>e?(e+=n,0>e&&(e=0)):e>n&&(e=n),0>t?(t+=n,0>t&&(t=0)):t>n&&(t=n),t<e&&(t=e);var o;if(u.TYPED_ARRAY_SUPPORT)o=this.subarray(e,t),o.__proto__=u.prototype;else{var r=t-e;o=new u(r,void 0);for(var s=0;s<r;++s)o[s]=this[s+e]}return o},u.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||U(e,t,this.length);for(var o=this[e],r=1,s=0;++s<t&&(r*=256);)o+=this[e+s]*r;return o},u.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||U(e,t,this.length);for(var o=this[e+--t],r=1;0<t&&(r*=256);)o+=this[e+--t]*r;return o},u.prototype.readUInt8=function(e,t){return t||U(e,1,this.length),this[e]},u.prototype.readUInt16LE=function(e,t){return t||U(e,2,this.length),this[e]|this[e+1]<<8},u.prototype.readUInt16BE=function(e,t){return t||U(e,2,this.length),this[e]<<8|this[e+1]},u.prototype.readUInt32LE=function(e,t){return t||U(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},u.prototype.readUInt32BE=function(e,t){return t||U(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},u.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||U(e,t,this.length);for(var r=this[e],s=1,l=0;++l<t&&(s*=256);)r+=this[e+l]*s;return s*=128,r>=s&&(r-=o(2,8*t)),r},u.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||U(e,t,this.length);for(var r=t,s=1,i=this[e+--r];0<r&&(s*=256);)i+=this[e+--r]*s;return s*=128,i>=s&&(i-=o(2,8*t)),i},u.prototype.readInt8=function(e,t){return t||U(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},u.prototype.readInt16LE=function(e,t){t||U(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},u.prototype.readInt16BE=function(e,t){t||U(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},u.prototype.readInt32LE=function(e,t){return t||U(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},u.prototype.readInt32BE=function(e,t){return t||U(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},u.prototype.readFloatLE=function(e,t){return t||U(e,4,this.length),ee.read(this,e,!0,23,4)},u.prototype.readFloatBE=function(e,t){return t||U(e,4,this.length),ee.read(this,e,!1,23,4)},u.prototype.readDoubleLE=function(e,t){return t||U(e,8,this.length),ee.read(this,e,!0,52,8)},u.prototype.readDoubleBE=function(e,t){return t||U(e,8,this.length),ee.read(this,e,!1,52,8)},u.prototype.writeUIntLE=function(e,t,n,r){if(e=+e,t|=0,n|=0,!r){var s=o(2,8*n)-1;Y(this,e,t,n,s,0)}var l=1,a=0;for(this[t]=255&e;++a<n&&(l*=256);)this[t+a]=255&e/l;return t+n},u.prototype.writeUIntBE=function(e,t,n,r){if(e=+e,t|=0,n|=0,!r){var s=o(2,8*n)-1;Y(this,e,t,n,s,0)}var l=n-1,i=1;for(this[t+l]=255&e;0<=--l&&(i*=256);)this[t+l]=255&e/i;return t+n},u.prototype.writeUInt8=function(e,t,o){return e=+e,t|=0,o||Y(this,e,t,1,255,0),u.TYPED_ARRAY_SUPPORT||(e=n(e)),this[t]=255&e,t+1},u.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||Y(this,e,t,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):D(this,e,t,!0),t+2},u.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||Y(this,e,t,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):D(this,e,t,!1),t+2},u.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||Y(this,e,t,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):N(this,e,t,!0),t+4},u.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||Y(this,e,t,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):N(this,e,t,!1),t+4},u.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t|=0,!r){var s=o(2,8*n-1);Y(this,e,t,n,s-1,-s)}var l=0,i=1,a=0;for(this[t]=255&e;++l<n&&(i*=256);)0>e&&0==a&&0!==this[t+l-1]&&(a=1),this[t+l]=255&(e/i>>0)-a;return t+n},u.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t|=0,!r){var s=o(2,8*n-1);Y(this,e,t,n,s-1,-s)}var l=n-1,i=1,a=0;for(this[t+l]=255&e;0<=--l&&(i*=256);)0>e&&0==a&&0!==this[t+l+1]&&(a=1),this[t+l]=255&(e/i>>0)-a;return t+n},u.prototype.writeInt8=function(e,t,o){return e=+e,t|=0,o||Y(this,e,t,1,127,-128),u.TYPED_ARRAY_SUPPORT||(e=n(e)),0>e&&(e=255+e+1),this[t]=255&e,t+1},u.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||Y(this,e,t,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):D(this,e,t,!0),t+2},u.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||Y(this,e,t,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):D(this,e,t,!1),t+2},u.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||Y(this,e,t,4,2147483647,-2147483648),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):N(this,e,t,!0),t+4},u.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||Y(this,e,t,4,2147483647,-2147483648),0>e&&(e=4294967295+e+1),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):N(this,e,t,!1),t+4},u.prototype.writeFloatLE=function(e,t,n){return j(this,e,t,!0,n)},u.prototype.writeFloatBE=function(e,t,n){return j(this,e,t,!1,n)},u.prototype.writeDoubleLE=function(e,t,n){return M(this,e,t,!0,n)},u.prototype.writeDoubleBE=function(e,t,n){return M(this,e,t,!1,n)},u.prototype.copy=function(e,t,n,o){if(n||(n=0),o||0===o||(o=this.length),t>=e.length&&(t=e.length),t||(t=0),0<o&&o<n&&(o=n),o===n)return 0;if(0===e.length||0===this.length)return 0;if(0>t)throw new RangeError('targetStart out of bounds');if(0>n||n>=this.length)throw new RangeError('sourceStart out of bounds');if(0>o)throw new RangeError('sourceEnd out of bounds');o>this.length&&(o=this.length),e.length-t<o-n&&(o=e.length-t+n);var r,s=o-n;if(this===e&&n<t&&t<o)for(r=s-1;0<=r;--r)e[r+t]=this[r+n];else if(1e3>s||!u.TYPED_ARRAY_SUPPORT)for(r=0;r<s;++r)e[r+t]=this[r+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+s),t);return s},u.prototype.fill=function(e,t,n,o){if('string'==typeof e){if('string'==typeof t?(o=t,t=0,n=this.length):'string'==typeof n&&(o=n,n=this.length),1===e.length){var r=e.charCodeAt(0);256>r&&(e=r)}if(void 0!==o&&'string'!=typeof o)throw new TypeError('encoding must be a string');if('string'==typeof o&&!u.isEncoding(o))throw new TypeError('Unknown encoding: '+o)}else'number'==typeof e&&(e&=255);if(0>t||this.length<t||this.length<n)throw new RangeError('Out of range index');if(n<=t)return this;t>>>=0,n=n===void 0?this.length:n>>>0,e||(e=0);var s;if('number'==typeof e)for(s=t;s<n;++s)this[s]=e;else{var i=u.isBuffer(e)?e:Q(new u(e,o).toString()),l=i.length;for(s=0;s<n-t;++s)this[s+t]=i[s%l]}return this};var oe=/[^+\/0-9A-Za-z-_]/g}).call(this,s(3))},function(e,t){'use strict';function n(e){var t=e.length;if(0<t%4)throw new Error('Invalid string. Length must be a multiple of 4');var n=e.indexOf('=');-1===n&&(n=t);var o=n===t?0:4-n%4;return[n,o]}function o(e,t,n){return 3*(t+n)/4-n}function r(e){return l[63&e>>18]+l[63&e>>12]+l[63&e>>6]+l[63&e]}function s(e,t,n){for(var o,s=[],l=t;l<n;l+=3)o=(16711680&e[l]<<16)+(65280&e[l+1]<<8)+(255&e[l+2]),s.push(r(o));return s.join('')}t.byteLength=function(e){var t=n(e),o=t[0],r=t[1];return 3*(o+r)/4-r},t.toByteArray=function(e){var t,r,s=n(e),i=s[0],l=s[1],p=new u(o(e,i,l)),d=0,c=0<l?i-4:i;for(r=0;r<c;r+=4)t=a[e.charCodeAt(r)]<<18|a[e.charCodeAt(r+1)]<<12|a[e.charCodeAt(r+2)]<<6|a[e.charCodeAt(r+3)],p[d++]=255&t>>16,p[d++]=255&t>>8,p[d++]=255&t;return 2===l&&(t=a[e.charCodeAt(r)]<<2|a[e.charCodeAt(r+1)]>>4,p[d++]=255&t),1===l&&(t=a[e.charCodeAt(r)]<<10|a[e.charCodeAt(r+1)]<<4|a[e.charCodeAt(r+2)]>>2,p[d++]=255&t>>8,p[d++]=255&t),p},t.fromByteArray=function(e){for(var t,n=e.length,o=n%3,r=[],a=16383,u=0,i=n-o;u<i;u+=a)r.push(s(e,u,u+a>i?i:u+a));return 1==o?(t=e[n-1],r.push(l[t>>2]+l[63&t<<4]+'==')):2==o&&(t=(e[n-2]<<8)+e[n-1],r.push(l[t>>10]+l[63&t>>4]+l[63&t<<2]+'=')),r.join('')};for(var l=[],a=[],u='undefined'==typeof Uint8Array?Array:Uint8Array,p='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',d=0,i=p.length;d<i;++d)l[d]=p[d],a[p.charCodeAt(d)]=d;a[45]=62,a[95]=63},function(e,r){r.read=function(t,n,r,l,a){var u,e,p=8*a-l-1,c=(1<<p)-1,f=c>>1,g=-7,h=r?a-1:0,i=r?-1:1,d=t[n+h];for(h+=i,u=d&(1<<-g)-1,d>>=-g,g+=p;0<g;u=256*u+t[n+h],h+=i,g-=8);for(e=u&(1<<-g)-1,u>>=-g,g+=l;0<g;e=256*e+t[n+h],h+=i,g-=8);if(0===u)u=1-f;else{if(u===c)return e?NaN:(d?-1:1)*Infinity;e+=o(2,l),u-=f}return(d?-1:1)*e*o(2,u-l)},r.write=function(r,l,a,u,p,f){var g,h,_,c=8*f-p-1,m=(1<<c)-1,y=m>>1,C=23===p?5.960464477539063e-8-6.617444900424222e-24:0,b=u?0:f-1,i=u?1:-1,d=0>l||0===l&&0>1/l?1:0;for(l=t(l),isNaN(l)||l===Infinity?(h=isNaN(l)?1:0,g=m):(g=n(Math.log(l)/Math.LN2),1>l*(_=o(2,-g))&&(g--,_*=2),l+=1<=g+y?C/_:C*o(2,1-y),2<=l*_&&(g++,_/=2),g+y>=m?(h=0,g=m):1<=g+y?(h=(l*_-1)*o(2,p),g+=y):(h=l*o(2,y-1)*o(2,p),g=0));8<=p;r[a+b]=255&h,b+=i,h/=256,p-=8);for(g=g<<p|h,c+=p;0<c;r[a+b]=255&g,b+=i,g/=256,c-=8);r[a+b-i]|=128*d}},function(e){var t={}.toString;e.exports=Array.isArray||function(e){return'[object Array]'==t.call(e)}},function(e){e.exports={NOT_CONNECTED:{status:'error',description:'There is no Socket connection available.'},CONNECTION_ERROR:{status:'error',description:'Connection error.'},SOCKET_EXCEPTION:{status:'error',description:'An exception occurred from the underlying WebSocket.'},AUTH_NOT_REQUIRED:{status:'ok',description:'Authentication is not required.'},REQUEST_TYPE_NOT_SPECIFIED:{status:'error',description:'A Request Type was not specified.'},init(){for(const e in this)({}).hasOwnProperty.call(this,e)&&(this[e].code=e,'error'!==this[e].status||this[e].error||(this[e].error=this[e].description));return delete this.init,this}}.init()},function(e,t,n){(function(o){t.log=function(...e){return'object'==typeof console&&console.log&&console.log(...e)},t.formatArgs=function(t){if(t[0]=(this.useColors?'%c':'')+this.namespace+(this.useColors?' %c':' ')+t[0]+(this.useColors?'%c ':' ')+'+'+e.exports.humanize(this.diff),!this.useColors)return;const n='color: '+this.color;t.splice(1,0,n,'color: inherit');let o=0,r=0;t[0].replace(/%[a-zA-Z%]/g,(e)=>{'%%'===e||(o++,'%c'===e&&(r=o))}),t.splice(r,0,n)},t.save=function(e){try{e?t.storage.setItem('debug',e):t.storage.removeItem('debug')}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem('debug')}catch(e){}return!e&&'undefined'!=typeof o&&'env'in o&&(e=o.env.DEBUG),e},t.useColors=function(){return'undefined'!=typeof window&&window.process&&('renderer'===window.process.type||window.process.__nwjs)||('undefined'!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:'undefined'!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||'undefined'!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||'undefined'!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&31<=parseInt(RegExp.$1,10)||'undefined'!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.colors=['#0000CC','#0000FF','#0033CC','#0033FF','#0066CC','#0066FF','#0099CC','#0099FF','#00CC00','#00CC33','#00CC66','#00CC99','#00CCCC','#00CCFF','#3300CC','#3300FF','#3333CC','#3333FF','#3366CC','#3366FF','#3399CC','#3399FF','#33CC00','#33CC33','#33CC66','#33CC99','#33CCCC','#33CCFF','#6600CC','#6600FF','#6633CC','#6633FF','#66CC00','#66CC33','#9900CC','#9900FF','#9933CC','#9933FF','#99CC00','#99CC33','#CC0000','#CC0033','#CC0066','#CC0099','#CC00CC','#CC00FF','#CC3300','#CC3333','#CC3366','#CC3399','#CC33CC','#CC33FF','#CC6600','#CC6633','#CC9900','#CC9933','#CCCC00','#CCCC33','#FF0000','#FF0033','#FF0066','#FF0099','#FF00CC','#FF00FF','#FF3300','#FF3333','#FF3366','#FF3399','#FF33CC','#FF33FF','#FF6600','#FF6633','#FF9900','#FF9933','#FFCC00','#FFCC33'],e.exports=n(17)(t);const{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return'[UnexpectedJSONParseError]: '+e.message}}}).call(this,n(16))},function(e){function t(){throw new Error('setTimeout has not been defined')}function n(){throw new Error('clearTimeout has not been defined')}function o(e){if(u===setTimeout)return setTimeout(e,0);if((u===t||!u)&&setTimeout)return u=setTimeout,setTimeout(e,0);try{return u(e,0)}catch(t){try{return u.call(null,e,0)}catch(t){return u.call(this,e,0)}}}function r(e){if(p===clearTimeout)return clearTimeout(e);if((p===n||!p)&&clearTimeout)return p=clearTimeout,clearTimeout(e);try{return p(e)}catch(t){try{return p.call(null,e)}catch(t){return p.call(this,e)}}}function s(){g&&c&&(g=!1,c.length?f=c.concat(f):h=-1,f.length&&l())}function l(){if(!g){var e=o(s);g=!0;for(var t=f.length;t;){for(c=f,f=[];++h<t;)c&&c[h].run();h=-1,t=f.length}c=null,g=!1,r(e)}}function a(e,t){this.fun=e,this.array=t}function i(){}var u,p,d=e.exports={};(function(){try{u='function'==typeof setTimeout?setTimeout:t}catch(n){u=t}try{p='function'==typeof clearTimeout?clearTimeout:n}catch(t){p=n}})();var c,f=[],g=!1,h=-1;d.nextTick=function(e){var t=Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];f.push(new a(e,t)),1!==f.length||g||o(l)},a.prototype.run=function(){this.fun.apply(null,this.array)},d.title='browser',d.browser=!0,d.env={},d.argv=[],d.version='',d.versions={},d.on=i,d.addListener=i,d.once=i,d.off=i,d.removeListener=i,d.removeAllListeners=i,d.emit=i,d.prependListener=i,d.prependOnceListener=i,d.listeners=function(){return[]},d.binding=function(){throw new Error('process.binding is not supported')},d.cwd=function(){return'/'},d.chdir=function(){throw new Error('process.chdir is not supported')},d.umask=function(){return 0}},function(e,n,o){e.exports=function(e){function n(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return r.colors[t(n)%r.colors.length]}function r(e){function t(...e){if(!t.enabled)return;const n=t,s=+new Date,i=s-(o||s);n.diff=i,n.prev=o,n.curr=s,o=s,e[0]=r.coerce(e[0]),'string'!=typeof e[0]&&e.unshift('%O');let l=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(t,o)=>{if('%%'===t)return t;l++;const s=r.formatters[o];if('function'==typeof s){const o=e[l];t=s.call(n,o),e.splice(l,1),l--}return t}),r.formatArgs.call(n,e);const a=n.log||r.log;a.apply(n,e)}let o;return t.namespace=e,t.enabled=r.enabled(e),t.useColors=r.useColors(),t.color=n(e),t.destroy=s,t.extend=i,'function'==typeof r.init&&r.init(t),r.instances.push(t),t}function s(){const e=r.instances.indexOf(this);return-1!==e&&(r.instances.splice(e,1),!0)}function i(e,t){const n=r(this.namespace+('undefined'==typeof t?':':t)+e);return n.log=this.log,n}function l(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,'*')}return r.debug=r,r.default=r,r.coerce=function(e){return e instanceof Error?e.stack||e.message:e},r.disable=function(){const e=[...r.names.map(l),...r.skips.map(l).map((e)=>'-'+e)].join(',');return r.enable(''),e},r.enable=function(e){r.save(e),r.names=[],r.skips=[];let t;const n=('string'==typeof e?e:'').split(/[\s,]+/),o=n.length;for(t=0;t<o;t++)n[t]&&(e=n[t].replace(/\*/g,'.*?'),'-'===e[0]?r.skips.push(new RegExp('^'+e.substr(1)+'$')):r.names.push(new RegExp('^'+e+'$')));for(t=0;t<r.instances.length;t++){const e=r.instances[t];e.enabled=r.enabled(e.namespace)}},r.enabled=function(e){if('*'===e[e.length-1])return!0;let t,n;for(t=0,n=r.skips.length;t<n;t++)if(r.skips[t].test(e))return!1;for(t=0,n=r.names.length;t<n;t++)if(r.names[t].test(e))return!0;return!1},r.humanize=o(18),Object.keys(e).forEach((t)=>{r[t]=e[t]}),r.instances=[],r.names=[],r.skips=[],r.formatters={},r.selectColor=n,r.enable(r.load()),r}},function(e){function n(e){if(e+='',!(100<e.length)){var t=/^((?:\d+)?\-?\d?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var o=parseFloat(t[1]),n=(t[2]||'ms').toLowerCase();return'years'===n||'year'===n||'yrs'===n||'yr'===n||'y'===n?o*c:'weeks'===n||'week'===n||'w'===n?o*d:'days'===n||'day'===n||'d'===n?o*p:'hours'===n||'hour'===n||'hrs'===n||'hr'===n||'h'===n?o*u:'minutes'===n||'minute'===n||'mins'===n||'min'===n||'m'===n?o*s:'seconds'===n||'second'===n||'secs'===n||'sec'===n||'s'===n?o*a:'milliseconds'===n||'millisecond'===n||'msecs'===n||'msec'===n||'ms'===n?o:void 0}}}function o(e){var n=t(e);return n>=p?l(e/p)+'d':n>=u?l(e/u)+'h':n>=s?l(e/s)+'m':n>=a?l(e/a)+'s':e+'ms'}function r(e){var n=t(e);return n>=p?i(e,n,p,'day'):n>=u?i(e,n,u,'hour'):n>=s?i(e,n,s,'minute'):n>=a?i(e,n,a,'second'):e+' ms'}function i(e,t,o,n){return l(e/o)+' '+n+(t>=1.5*o?'s':'')}var l=Math.round,a=1e3,s=60*a,u=60*s,p=24*u,d=7*p,c=365.25*p;e.exports=function(e,t){t=t||{};var s=typeof e;if('string'==s&&0<e.length)return n(e);if('number'==s&&!1===isNaN(e))return t.long?r(e):o(e);throw new Error('val is not a non-empty string or a valid number. val='+JSON.stringify(e))}},function(e){e.exports=function(e,t,n){n&&n.stack?e(`${t}\n %O`,n.stack):'object'==typeof n?e(`${t} %o`,n):e(`${t} %s`,n)}},function(e){e.exports=function(e){for(const t in e=e||{},e){if(!{}.hasOwnProperty.call(e,t))continue;const n=t.replace(/-([a-z])/gi,(e,t)=>{return t.toUpperCase()});e[n]=e[t]}return e}}])});
//# sourceMappingURL=obs-websocket.min.js.map