# This is a basic workflow to help you get started with Actions

name: Update translations

# Controls when the action will run. 
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    paths:
      - 'index.html'
      - 'main.js'
    branches:
      - master

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
        - uses: actions/checkout@v2
        - uses: actions/setup-node@v2
          with:
            node-version: '14'
        - run: npm install jsdom && npm install axios
        - run: node .github/ci-generateTranslations.js
        - name: Create Pull Request
          uses: peter-evans/create-pull-request@v3
          with:
            commit-message: Generated updated translations
            branch: generated_translations
            title: "[VDONinja Bot] Updated translations"
            labels: i18n
